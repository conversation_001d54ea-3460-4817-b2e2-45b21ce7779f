# 节点: SendFileNode

## 描述

此节点负责识别客户对特定文件或资源（如录音、奖励、思维导图、课程安排）的请求并发送相应的文件或链接。

1. **分类请求:** 调用LLM（`classifier`）分析客户消息和最近聊天历史。
    * LLM被提示一个已知文件名/类型列表（`fileNameList`）及其相应的内部`FileType`枚举值（`fileCase`）。
    * 它尝试将客户请求（如"发我回放"、"要奖励"、"思维导图"）匹配到已知文件类型之一。
    * 如果未找到匹配项，返回`FileType.NodeInvoke`。
2. **确定文件类型:** 基于LLM分类和可能的当前时间（`currentTime`），确定最终`FileType`。
    * **录音特殊处理:** 如果请求是录音，检查当前时间相对于课程安排。
        * 如果直播课程仍在进行或尚未开始，可能返回`FileType.LiveClassReminder`（鼓励现场参与）或特定"无录音"类型（`FileType.NoRecordingReason`, `FileType.ExtraClassNoRecording`），取决于当天。
        * 如果课程已结束，返回适当的录音链接类型（如`FileType.Class1RecordingLink`）。
3. **发送文件/消息:** 基于确定的`FileType`:
    * **特定文件类型（奖励、思维导图、音频、PDF、安排）:** 从`course_day`或`pre_course_day`脚本检索相应消息（通常是包含图片、文件或链接卡URL的`ISendMedia`对象）并使用`GroupSend().sendMsg`发送。更新相关状态标志（如`has_send_class1_reward`）。
    * **录音链接:** 使用`DataService.getCourseLink`检索正确的录音链接并作为文本消息发送。
    * **提醒/反馈（LiveClassReminder, NoRecordingReason等）:** 使用动态提示调用`LLMNode`生成上下文响应，解释为什么录音尚不可用或鼓励现场参与。
    * **NodeInvoke:** 如果文件类型是`NodeInvoke`（意味着未识别特定文件），它检索*先前*节点（传入`SendFileNode.invoke`的`entryNode`）并重新调用该节点以处理客户消息作为一般查询。
4. **转换:**
    * 如果发送了文件/链接或生成了特定提醒消息，通常会转回`SendFileNode`被调用*前*活动的节点（从`ChatStateStore.get(state.chat_id).nextStage`读取）。
    * 如果触发了`FileType.NodeInvoke`，转换由重新调用的节点决定。

## 流程图

```mermaid
graph TD
    A[开始 SendFileNode] --> B["LLM: 分类客户请求<br>(匹配fileNameList)"];
    B --> C{已识别文件类型?};
    C -- 否 --> D[重新调用上一节点];
    C -- 是 --> E{是录音请求?};

    E -- 是 --> F[检查当前时间vs课程时间];
    F -- 直播课程进行中? --> G[LLMNode: 鼓励现场参与];
    F -- 录音未就绪? --> H[LLMNode: 解释延迟/无回放];
    F -- 录音可用 --> I[获取录音链接];
    I --> J[发送录音链接消息];

    E -- 否 --> K{文件类型?};
    K -- 奖励/思维导图/音频/PDF/安排 --> L[从脚本获取文件/链接];
    L --> M[发送文件/链接卡消息];
    M --> N["更新状态标志(如奖励已发送)"];

    G --> O["结束 SendFileNode (返回上一阶段)"];
    H --> O;
    J --> O;
    N --> O;
    D --> O;
```