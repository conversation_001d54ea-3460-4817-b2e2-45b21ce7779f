# 节点: SendInviteLinkNode

## 描述

此节点专门负责向客户发送21天系统课程购买邀请链接，通常在销售阶段客户表达同意或强烈兴趣时触发。

1. **检查支付状态:** 如果客户已经支付（`is_complete_payment`标志为true），立即转换到`PostSaleNode`。
2. **发送邀请链接消息:**
    * 使用指示LLM发送购买链接（`https://t.meihao.com/HhYJ`）并发起购买邀请的提示调用`SalesNodeHelper.replyMessage`。
3. **检查链接发送:** 验证链接`https://t.meihao.com/HhYJ`是否实际出现在最近的聊天历史中（检查最后1-2轮）。
    * 如果**未**发送（如LLM未能包含），调用`sendInviteLink`辅助函数，发送包含链接的固定消息，可能根据当前日期调整措辞（如果是一周晚些时候，提及紧迫性）。
4. **检查/发送分期视频:**
    * 调用`sendInstallmentVideo`辅助函数。
    * 检查客户在最近历史中是否提到"分期"。
    * 如果是，且分期视频尚未发送（`is_send_installment_video`标志）:
        * 发送介绍视频的消息。
        * 发送分期指南视频文件。
        * 设置`is_send_installment_video`标志为true。
5. **转换:** 转换到`SalesNode`继续销售对话或处理进一步的客户回应。

## 流程图

```mermaid
graph TD
    A[开始 SendInviteLinkNode] --> B{已支付?};
    B -- 是 --> C[PostSaleNode];
    B -- 否 --> D["LLMNode: 发送邀请链接消息<br>(包含 https://t.meihao.com/HhYJ)"];
    D --> E{检查最近历史:<br>链接已发送?};
    E -- 否 --> F[发送带链接的固定消息];
    E -- 是 --> G[检查/发送分期视频];
    F --> G;
    G --> H[SalesNode];
```