# 节点: WealthOrchardAnalyze

## 描述

此节点处理客户在第2天冥想练习后描述的"财富果园"可视化的分析和解释。

1. **检查重复:** 使用Jaccard相似度比较当前客户消息（他们的可视化描述）与先前存储的第2天作业（`userSlots.day2_homework`）。如果相似度高（>= 0.8），假定这是重复提交并转换到`FreeTalk`而不重新分析。
2. **处理课堂模板:** 如果是在第2天直播课程期间（约晚上8点-9点）且模板消息尚未发送，发送引导客户分享其果园可视化详情的模板。设置`in_class_wealth_orchard_template_send`标志。
3. **存储和延迟:**
    * 设置`is_complete_wealth_orchard_analyze`标志为true。
    * 将客户当前消息添加到与`chat_id`关联的临时存储（`WealthOrchardStore`）。这允许收集可能通过多条消息发送的零散描述。
    * 安排清理任务（15分钟后）清除此`chat_id`的临时存储。
    * 等待显著延迟（5分钟，在本地测试中跳过）以允许客户完成发送描述。
4. **处理消息:**
    * 检索在此`chat_id`的`WealthOrchardStore`中收集的所有消息。
    * 检查*当前*处理的消息是否是临时存储中收到的*最后*一条。如果不是（意味着在延迟期间有更新消息到达），记录警告并转换到`FreeTalk`，让更新消息稍后触发分析。
    * 如果*是*最后一条，将所有收集的消息合并为单个`userImage`字符串。
    * 清除此`chat_id`的`WealthOrchardStore`。
    * 使用合并的`userImage`更新`day2_homework`客户槽位。
5. **预分析检查:** 调用`isWealthOrchardImage`（基于LLM的检查）验证`userImage`是否确实看起来像财富果园描述。如果不是，转换到`FreeTalk`。
6. **RAG和LLM解释:**
    * 调用`MoerRag.wealthOrchardRag`，它:
        * 使用LLM（`WealthKeyExtract`）从`userImage`中提取关键元素（子类别）。
        * 使用Elasticsearch（`exactMatchSearchWealth`，带嵌入回退）检索对应于这些提取元素的解释。
    * 使用详细提示调用`LLMNode.invoke`，指示它:
        * 扮演麦子老师。
        * 基于检索的`relevanceContext`（来自RAG的解释）解释客户的`userImage`。
        * 解释不同元素（门、树、果实、栅栏、动作、季节）的含义。
        * 总结潜在的财富"卡点"。
        * 可选（如果`nodeInvokeCount` < 1），提出澄清问题。
7. **转换:** 转换到`FreeTalk`。

## 流程图

```mermaid
graph TD
    A[开始 WealthOrchardAnalyze] --> B{"作业重复? (相似度 >= 0.8)"};
    B -- 是 --> C[FreeTalk];
    B -- 否 --> D{是在第2天直播课程期间<br>且模板未发送?};
    D -- 是 --> E[发送果园描述模板];
    E --> F[设置标志: in_class_template_sent = true];
    F --> G[设置标志: is_complete_wealth_orchard_analyze = true];
    D -- 否 --> G;

    G --> H[将客户消息添加到临时存储];
    H --> I["安排临时存储清理 (15分钟)"];
    I --> J[等待5分钟];
    J --> K[从临时存储检索所有消息];
    K --> L{当前消息是最后一条收到的?};
    L -- 否 --> C;
    L -- 是 --> M[将消息连接成userImage];
    M --> N[清除临时存储];
    N --> O[更新userSlots.day2_homework];
    O --> P{"是财富果园图像? (LLM检查)"};
    P -- 否 --> C;
    P -- 是 --> Q[RAG: 获取userImage的解释];
    Q --> R[LLMNode: 解释果园,<br>说明元素, 总结卡点,<br>可能提问];
    R --> C;
```