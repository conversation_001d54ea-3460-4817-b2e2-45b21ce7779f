# 节点: EnergyTestAnalyze

## 描述

此节点负责分析并回应客户的能量测试分数。

1. **检查分数:** 使用`DataService.getEnergyTestScore`检索客户能量测试分数。如果未找到分数，转换到`FreeTalk`。
2. **检查完成标志:** 检查能量测试分析（`is_complete_energy_test_analyze`）是否已经为此客户完成。如果是，转换到`FreeTalk`。
3. **基于分数的路由:**
    * **高分 (>= 200):**
        * 设置`is_complete_energy_test_analyze`标志为true。
        * 发送预定义的鼓励消息，确认他们的良好能量水平。
        * 转回自身（`EnergyTestAnalyze`），有效结束此特定分数分析的交互路径（因为标志现已设置）。
    * **低分 (< 200):**
        * 检查子状态'LowScoreAsk'的`nodeInvokeCount`。
        * **首次调用 (计数 = 0):**
            * 设置`is_complete_energy_test_analyze`标志为true。
            * 调用`LLMNode`解释分数表明能量较低，并提出共情问题，将其与客户先前确定的痛点联系起来（如果客户槽位中有）。
            * 增加'LowScoreAsk'计数。
            * 转回自身（`EnergyTestAnalyze`）等待客户回应。
        * **后续调用 (计数 > 0):**
            * 调用`LLMNode`根据客户对之前问题的回应提供反馈。LLM被提示共情，解释客户感受的潜在心理原因，介绍冥想作为解决方案，并提供鼓励。
            * 转回自身（`EnergyTestAnalyze`）。

## 流程图

```mermaid
graph TD
    A[开始 EnergyTestAnalyze] --> B{获取能量分数};
    B -- 未找到分数 --> C[FreeTalk];
    B -- 找到分数 --> D{分析已<br>完成?};
    D -- 是 --> C;
    D -- 否 --> E{分数 >= 200?};

    E -- 是 (高分) --> F[更新状态:<br>is_complete_energy_test_analyze = true];
    F --> G[发送鼓励消息];
    G --> A;

    E -- 否 (低分) --> H{LowScoreAsk计数?};
    H -- 0 --> I[更新状态:<br>is_complete_energy_test_analyze = true];
    I --> J[LLMNode: 解释低分,<br>提出共情问题<br>链接到痛点];
    J --> K[增加LowScoreAsk计数];
    K --> A;

    H -- "> 0" --> L[LLMNode: 提供反馈,<br>解释心理原因,<br>建议冥想,<br>鼓励];
    L --> A;
```