# 节点: CourseFeedBackDay3InClass

## 描述

此节点处理客户在第3天"红靴子"练习*之后*的互动，特别是当客户*参加*了现场课程时。旨在了解客户体验并巧妙引导他们参加系统课程。

1. **检查调用次数:** 跟踪此节点在当前会话中被调用的次数。
2. **第一次调用 (计数 = 0):**
    * 检索客户槽位（痛点、目标）和能量测试分数（如果有）。
    * 调用`LLMNode`询问客户的"红靴子"体验，将其与已知痛点或能量水平联系起来。
    * 增加调用计数。
    * 转回自身（`CourseFeedBackDay3InClass`）等待客户回应。
3. **第二次调用 (计数 = 1):**
    * 检查是否已经是周四或之后。如果是，转换到`FreeTalk`。
    * 调用`LLMNode`响应客户反馈，使体验正常化，解决问题，并提及即将到来的"蓝鹰预演"练习（可能作为特别加播内容）。
    * 增加调用计数。
    * 转回自身（`CourseFeedBackDay3InClass`）。
4. **第三至第五次调用 (计数 <= 4):**
    * 检查是否已经是周四或之后。如果是，转换到`FreeTalk`。
    * 调用`LLMNode`继续对话，了解客户感受，温和反驳关于进一步学习的异议或犹豫（可能与系统课程相关），并强化冥想的价值。提及即将到来的"蓝鹰"课程。
    * 增加调用计数。
    * 转回自身（`CourseFeedBackDay3InClass`）。
5. **后续调用 (计数 > 4):** 直接转换到`FreeTalk`。

## 流程图

```mermaid
graph TD
    A[开始 CourseFeedBackDay3InClass] --> B{调用计数?};
    B -- 0 --> C[获取客户槽位/能量分数];
    C --> D["LLMNode: 询问红靴子<br>体验 (链接到痛点)"];
    D --> E[增加计数];
    E --> A;

    B -- 1 --> F{是周四+?};
    F -- 是 --> G[FreeTalk];
    F -- 否 --> H[LLMNode: 回应反馈,<br>正常化, 提及蓝鹰];
    H --> E;

    B -- 2-4 --> I{是周四+?};
    I -- 是 --> G;
    I -- 否 --> J[LLMNode: 继续对话,<br>解决问题, 强化价值,<br>提及蓝鹰];
    J --> E;

    B -- "> 4" --> G;
```