# 节点: CourseFeedBackDay3NotInClass

## 描述

此节点处理客户在第3天"红靴子"练习*之后*的互动，特别是当客户*未参加*现场课程时。

1. **LLM响应:** 调用`LLMNode`生成回复。
    * LLM被提示确认客户消息，表达遗憾他们错过了重要的"红靴子"练习，强调其价值（如提升能量），并强烈鼓励他们观看回放（如果有）或提及即将到来的"蓝鹰"课程作为另一次宝贵机会。
    * 使用RAG和记忆召回。
2. **转换:** 转换到`FreeTalk`节点。

## 流程图

```mermaid
graph TD
    A[开始 CourseFeedBackDay3NotInClass] --> B[LLMNode: 回应客户,<br>表达遗憾错过课程,<br>强调红靴子价值,<br>提及蓝鹰];
    B --> C[FreeTalk];
```