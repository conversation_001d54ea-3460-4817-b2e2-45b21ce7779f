# 节点: IntentionQueryBeforeClass

## 描述

此节点专门处理客户在机器人询问他们加入冥想课程的主要动机或目标*在课程开始前*（使用1-7的编号列表）后的回应。

1. **检查调用计数:** 将执行限制为最多2次以避免卡住。如果计数 >= 2，转换到`FreeTalk`。
2. **检查客户回应格式:** 确定客户的消息是否是1到7之间的单个数字。
3. **处理数字回应:**
    * 如果客户回应是有效数字(1-7)*并且*最近没有发送特定确认消息:
        * 发送固定回应确认选择并将其链接到第一课的内容（情绪释放、专注、睡眠）。
        * 发送第二条固定消息强调课程的渐进性质（第2天 - 财富果园，第3天 - 红靴子）。
        * 转回自身（`IntentionQueryBeforeClass`）。这允许它可能处理客户立即回复的另一条消息，但调用计数限制防止无限循环。
4. **处理非数字/其他回应:** 如果客户的回应不是简单的1-7数字，或者最近已经发送了确认消息，转换到`FreeTalk`进行一般对话处理。

## 流程图

```mermaid
graph TD
    A[开始 IntentionQueryBeforeClass] --> B{调用计数 >= 2?};
    B -- 是 --> C[FreeTalk];
    B -- 否 --> D{客户回应是<br>1-7的数字?};
    D -- 是 --> E{最近已发送确认?};
    E -- 否 --> F[发送消息: 确认选择,<br>链接到第1课];
    F --> G[发送消息: 强调<br>第2和第3课];
    G --> H[增加调用计数];
    H --> A;
    E -- 是 --> C;
    D -- 否 --> C;
```