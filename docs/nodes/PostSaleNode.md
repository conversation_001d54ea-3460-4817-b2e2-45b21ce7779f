# 节点: PostSaleNode

## 描述

此节点处理客户成功支付21天系统课程后的售后流程。其主要目标是收集必要的物理礼品（坐垫 - 冥想坐垫）的配送信息。

1. **检查完成标志:** 检查售后流程（`is_complete_post_sale`）是否已标记为完成。如果是，转换到`FreeTalk`。
2. **提取地址信息:**
    * 调用`getCustomerSlots`，使用`ExtractAddressSlots.extract`（由LLM提供支持）解析最近聊天历史中的客户姓名、电话号码和配送地址。
    * 将任何新提取的信息与现有客户槽位合并。
3. **检查地址完整性:** 检查客户槽位中是否现在存在`address`槽位。
    * 如果**是**（地址可用）:
        * 调用`completePostSale`发送关于配送礼品、系统课程开始日期、APP访问（季卡）和课程笔记分发的确认消息。
        * 发送系统课程安排图片。
        * 发送APP会员指南图片。
        * 设置`is_complete_post_sale`标志为true。
        * 转接至人工客服（`HumanTransferType.ConfirmedAddress`）进行最终验证/处理。
        * 转换到`FreeTalk`。
    * 如果**否**（地址缺失）:
        * 调用`requestCustomerInfo`，使用`LLMNode`特别询问客户缺失的配送地址（如果也缺失，可能还包括姓名/电话）。
        * 转回自身（`PostSaleNode`）等待客户的地址信息回应。

## 流程图

```mermaid
graph TD
    A[开始 PostSaleNode] --> B{售后<br>已完成?};
    B -- 是 --> C[FreeTalk];
    B -- 否 --> D["从聊天历史中提取地址信息<br>(LLM)"];
    D --> E{地址槽位已填?};
    E -- 是 --> F[发送配送确认和<br>课程信息消息];
    F --> G[发送课程安排图片];
    G --> H[发送App会员指南图片];
    H --> I[更新状态:<br>is_complete_post_sale = true];
    I --> J[人工转接: ConfirmedAddress];
    J --> C;

    E -- 否 --> K[LLMNode: 询问缺失的<br>地址/姓名/电话];
    K --> A;
```