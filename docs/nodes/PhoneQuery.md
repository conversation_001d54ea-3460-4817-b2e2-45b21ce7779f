# 节点: PhoneQuery

## 描述

此节点负责获取和验证客户的电话号码，这对于将其微信账号与课程购买和Moer系统档案联系起来至关重要。此节点*不*用于小红书等0元渠道。

1. **检查现有槽位:** 检查客户状态中是否已存在`phoneNumber`槽位。如果是，转换到`FreeTalk`。
2. **提取电话号码:** 尝试使用`RegexHelper.extractPhoneNumber`从客户当前消息中提取11位电话号码。
3. **处理提取的号码:**
    * 如果提取了电话号码:
        * 调用`MoerAPI.getUserByPhone`在Moer系统中查找客户。
        * 如果**找到**:
            * 调用`NewCourseUser.create`更新聊天记录的Moer ID、电话号码和课程号。它还处理潜在的重复购买（如有必要触发退款流程和人工转接）。
            * 如果`NewCourseUser.create`表明是*新的*有效注册（非重复），发送确认消息（"好的，收到..."）。
            * 转换到`FreeTalk`。
        * 如果**未找到**（或API错误）:
            * 记录问题。
            * 转接至人工客服（`HumanTransferType.NotBindPhone`）。
            * 转回自身（`PhoneQuery`）以便可能重新询问。
    * 如果**未**提取电话号码:
        * 调用`LLMNode`重新询问客户电话号码，强调其对课程访问的必要性。
        * 检查`PhoneQuery`的`nodeInvokeCount`。如果超过阈值（如3次尝试），转接至人工客服（`HumanTransferType.NotBindPhone`）。
        * 转回自身（`PhoneQuery`）。

## 流程图

```mermaid
graph TD
    A[开始 PhoneQuery] --> B{客户槽位中<br>有电话号码?};
    B -- 是 --> C[FreeTalk];
    B -- 否 --> D{从客户消息中<br>提取电话号码?};
    D -- 是 --> E[调用MoerAPI: 通过电话获取客户];
    E -- 找到 --> F["NewCourseUser.create<br>(更新数据库, 检查重复)"];
    F -- 重复购买 --> G[发送退款消息];
    G --> H[人工转接: RefundCourse];
    H --> C;
    F -- 新注册 --> I[发送确认消息];
    I --> C;
    E -- 未找到/错误 --> J[记录错误];
    J --> K[人工转接: NotBindPhone];
    K --> A;

    D -- 否 --> L[LLMNode: 重新询问电话号码];
    L --> M{调用计数 >= 3?};
    M -- 是 --> K;
    M -- 否 --> N[增加调用计数];
    N --> A;
```