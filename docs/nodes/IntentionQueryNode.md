# 节点: IntentionQueryNode

## 描述

此节点负责在对话早期主动收集客户关键信息（槽位），特别是在介绍预课程材料*之后*但*在*主课程开始之前。它专注于了解客户对现场课程的可用性及其学习冥想的目标。

1. **检查完成标志:** 检查主要意图查询阶段（`is_complete_user_query`）是否已标记为完成。
    * 如果**是**: 调用`continueIntentionQuery`潜在地进行进一步的共情对话或转换到`FreeTalk`。然后，如果适当，安排发送能量测试。
    * 如果**否**: 继续询问槽位。
2. **确定要询问的槽位:**
    * 调用`getSlotAsk`确定下一个需要的信息。
    * 优先询问`live_class_confirmation`（他们能否参加晚上8点的现场课程？）然后是`meditation_goal`（他们为什么想学习冥想？）。
    * 检查`ChatStateStore.slotAskedCount`和现有的`userSlots`以避免多次询问同一问题或询问已提供的信息。
    * 如果所有必需槽位都已填写或已询问，`getSlotAsk`返回null。
3. **提问:**
    * 如果`getSlotAsk`返回要询问的槽位:
        * 如果槽位是`live_class_confirmation`，发送特定消息询问8点的可用性（根据是课程周还是预课程周调整措辞）。
        * 如果槽位是`meditation_goal`，检查是否最近询问过类似问题。如果没有，可能在发送关于目标/先前经验的问题之前先调用`FreeTalk`（如果最后一条消息来自客户）。
        * 如果是其他类型的槽位（虽然目前只定义了上述两个），则使用动态提示调用`LLMNode`来制定问题。
        * 增加所询问槽位的`slotAskedCount`。
        * 转回自身（`IntentionQueryNode`）等待客户回应。
4. **槽位询问完成:**
    * 如果`getSlotAsk`返回null（所有槽位都已收集/询问）:
        * 设置`is_complete_user_query`标志为true。
        * 异步从最近的聊天历史中提取客户槽位（`ExtractUserSlotsV2`，`ExtractUserSlots`）。
        * 调用`continueIntentionQuery`进行潜在的后续。
        * 安排发送能量测试（`sendEnergyTest`）。
        * `continueIntentionQuery`的返回值（可能是`FreeTalk`或`IntentionQueryNode`）决定下一个状态。

**辅助逻辑 (`continueIntentionQuery`):**

* 根据客户的最后一条消息和目标（`isNeedEmpathy`）检查是否需要共情。
* 如果需要共情且节点未被调用太多次（当前 < 5），调用`LLMNode`提供共情回应，可能链接到客户痛点。转回`IntentionQueryNode`。
* 否则（不需要共情或达到限制），调用`LLMNode`提供可能基于冥想原则重新框架客户情况并暗示未来课程如何帮助的回应。转换到`FreeTalk`。

## 流程图

```mermaid
graph TD
    A[开始 IntentionQueryNode] --> B{意图查询<br>完成?};
    B -- 是 --> C[调用 continueIntentionQuery];
    C --> D{安排能量测试};
    D --> E[来自continueIntentionQuery的下一节点];

    B -- 否 --> F[获取下一个要询问的槽位];
    F -- 无剩余槽位 --> G[设置标志: is_complete_user_query = true];
    G --> H[异步提取客户槽位];
    H --> C;

    F -- 找到槽位 --> I{槽位类型?};
    I -- live_class_confirmation --> J[发送消息: 询问8点可用性];
    I -- meditation_goal --> K[检查历史; 可能先FreeTalk];
    K --> L[发送消息: 询问目标/经验];
    I -- 其他 --> M[LLMNode: 提问槽位问题];

    J --> N[增加槽位询问计数];
    L --> N;
    M --> N;
    N --> A;

    subgraph continueIntentionQuery
        direction TB
        C1[开始 continueIntentionQuery] --> C2{需要共情 AND<br>调用计数 < 5?};
        C2 -- 是 --> C3[LLMNode: 共情回应];
        C3 --> C4[返回 IntentionQueryNode];
        C2 -- 否 --> C5[LLMNode: 重新框架情况,<br>链接到课程价值];
        C5 --> C6[返回 FreeTalk];
    end
```