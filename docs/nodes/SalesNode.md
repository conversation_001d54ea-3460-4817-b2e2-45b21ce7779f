# 节点: SalesNode

## 描述

此节点管理销售对话阶段，通常在介绍性课程的第3天或第4天后激活，旨在将客户转化为21天系统课程。

1. **检查支付状态:** 如果客户已经支付（`is_complete_payment`标志为true），立即转换到`PostSaleNode`。
2. **检查调用计数:** 监控此节点被调用的次数。如果超过阈值（30），转接到人工客服（`HumanTransferType.ExceededSalesNodeInvokeCountLimit`），因为对话可能卡住。
3. **分类客户意图:** 使用`SalesCategoryHelper.intentionCategorize`（基于LLM）将客户的最新消息分类为:
    * `Agree`: 客户表达明确购买意图。
    * `QuestionAboutSystemCourse`: 客户询问关于21天课程的特定问题（价格、内容、安排等）。
    * `Refuse`: 客户明确拒绝购买。
    * `Other`: 一般聊天，模糊回应或不相关主题。
4. **基于意图路由:**
    * **Agree:** 直接转换到`SendInviteLinkNode`提供购买链接。
    * **QuestionAboutSystemCourse:** 调用`promoteSales`解答问题然后可能发送邀请链接。
    * **Refuse:** 调用`handleRefuseType`管理拒绝。
    * **Other:** 调用`promoteSales`温和地将对话引导回系统课程价值主张。
5. **转换:** 最终下一个节点由特定处理函数（`promoteSales`, `handleRefuseType`, `SendInviteLinkNode`）内的逻辑决定。

**辅助逻辑 (`promoteSales`):**

* 根据聊天历史和客户档案从`SalesPrompt.getSalesStrategy`（基于LLM）获取销售策略和具体说辞。
* 使用生成的策略作为动态提示调用`LLMNode`制定回应。
* 发送相关案例研究图片（`SalesNodeHelper.sendCaseImage`）。
* 如果初始意图是`QuestionAboutSystemCourse`，它继续发送邀请链接（`sendInvitation`）。
* 否则，它执行清单（`invitationCheckList`）来决定是现在发送邀请，稍后发送（通过`SilentReAsk`），还是继续促销。

**辅助逻辑 (`handleRefuseType`):**

* 增加特定`PurchaseRefuseCount`。
* 如果在周四之前，调用`promoteSales`（允许温和重新吸引）。
* 如果是周四或之后:
    * 如果拒绝计数很高（>5），转接至人工（`HumanTransferType.RefusePurchase`）。
    * 否则，调用`promoteSales`（允许更持续的重新吸引）。

**辅助逻辑 (`sendInvitation`):**

* 检查邀请链接最近是否已发送。
* 如果没有，调用`SendInviteLinkNode`。
* 检查客户最近是否提到"分期"，如果需要且尚未发送，则发送分期视频指南。

## 流程图

```mermaid
graph TD
    A[开始 SalesNode] --> B{已支付?};
    B -- 是 --> C[PostSaleNode];
    B -- 否 --> D{调用计数 > 30?};
    D -- 是 --> E[人工转接: 超出限制];
    D -- 否 --> F["分类客户意图 (LLM)"];

    F -- 同意 --> G[SendInviteLinkNode];
    F -- 问题 --> H[promoteSales];
    F -- 拒绝 --> I[handleRefuseType];
    F -- 其他 --> H;

    subgraph promoteSales
        H1[开始 promoteSales] --> H2["获取销售策略 (LLM)"];
        H2 --> H3[LLMNode: 基于策略回复];
        H3 --> H4[发送案例图片];
        H4 --> H5{初始意图 == 问题?};
        H5 -- 是 --> H6[sendInvitation];
        H5 -- 否 --> H7[invitationCheckList];
        H6 --> H8[确定下一阶段];
        H7 --> H8;
    end

    subgraph handleRefuseType
        I1[开始 handleRefuseType] --> I2[增加拒绝计数];
        I2 --> I3{是周四之前?};
        I3 -- 是 --> I4[promoteSales];
        I3 -- 否 --> I5{拒绝计数 > 5?};
        I5 -- 是 --> I6[人工转接: RefusePurchase];
        I5 -- 否 --> I4;
        I4 --> I7[确定下一阶段];
        I6 --> I7;
    end

    subgraph sendInvitation
        SI1[开始 sendInvitation] --> SI2{最近发送过链接?};
        SI2 -- 是 --> SI3[确定下一阶段];
        SI2 -- 否 --> SI4[SendInviteLinkNode];
        SI4 --> SI5{提到分期?};
        SI5 -- 是 --> SI6{分期视频已发送?};
        SI6 -- 否 --> SI7[发送分期视频];
        SI7 --> SI3;
        SI6 -- 是 --> SI3;
        SI5 -- 否 --> SI3;
    end

    subgraph invitationCheckList
        ICL1[开始 invitationCheckList] --> ICL2["获取清单结果 (LLM)"];
        ICL2 -- "促销 (1)" --> ICL3[确定下一阶段];
        ICL2 -- "现在发送 (2)" --> ICL4[sendInvitation];
        ICL2 -- "稍后发送 (3)" --> ICL5["安排 sendInvitation (5分钟)"];
        ICL5 --> ICL3;
        ICL4 --> ICL3;
    end

    H --> Z[结束 SalesNode];
    I --> Z;
    G --> Z;
    ICL3 --> Z;
    SI3 --> Z;
    I7 --> Z;
    H8 --> Z;
```