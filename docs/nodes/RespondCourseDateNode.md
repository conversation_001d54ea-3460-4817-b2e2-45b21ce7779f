# 节点: RespondCourseDateNode

## 描述

此节点专门设计用于回答客户关于课程安排、时间和日期的问题。

1. **收集上下文:**
    * 从数据库检索客户分配的`course_no`。
    * 使用`DataService`获取该特定课程号的开始日期（`courseDate`）。
    * 使用`DataService`获取相对于课程安排的当前时间（`currentTime`）。
    * 计算关键日期: `campStartDate`（开营班会）, `courseWeekStart`, `addOnCourseDate`（加播课）。
    * 检索系统课程开始日期（`systemStartTime`）。
2. **构建动态提示:** 为`LLMNode`构建详细提示，包含:
    * 当前日期/时间和课程上下文（课程号、周状态、日、时间）。
    * 上面计算的关键日期。
    * 课程安排摘要（第-1天至第4天内容和时间）。
    * 基于当前时间和客户查询上下文的特定回答规则:
        * **系统课程查询:** 如果查询是关于21天系统课程（特别是在第3天晚上8点之后），提供有关其安排、格式（直播+录播）和开始日期（`systemStartTime`）的详细信息。
        * **加播课（加播课）可见性:** 限制提及第4天加播课，直到第4天的特定时间，以建立期待感。
        * **一般时间查询:** 指导LLM如何基于查询的课程日是否在未来、过去或当前进行来回应。强调鼓励现场参与但提及有回放存在（加播课除外）。
        * **格式:** 要求简洁、鼓励、准确的回答，避免特定短语如"有问题随时告诉我"。
3. **处理课程后场景:** 如果当前时间在主要4天课程期间之后（`isAfterDay4Course`），它使用简化提示（`getAfterCourseDynamicPrompt`）仅关注21天系统课程详情。
4. **LLM调用:** 使用构建的动态提示和当前状态调用`LLMNode.invoke`。
5. **转换:**
    * 读取由LLM设置的`nextStage`（或如果逻辑决定，可能由其他节点设置）。
    * 如果`nextStage`是`Sales`，它直接调用`SalesNode`。
    * 否则，它返回由LLM或后续逻辑确定的`nextStage`。

## 流程图

```mermaid
graph TD
    A[开始 RespondCourseDateNode] --> B[获取客户课程号];
    B --> C[获取课程开始日期];
    C --> D[获取当前时间上下文];
    D --> E["计算关键日期<br>(开营班会, 加播课开始)"];
    E --> F[获取系统课程开始日期];
    F --> G{是第4天课程之后?};
    G -- 是 --> H["构建课程后提示<br>(专注于系统课程)"];
    G -- 否 --> I["构建主要动态提示<br>(包括安排, 时间规则等)"];
    H --> J[LLMNode: 回答时间查询];
    I --> J;
    J --> K{下一阶段 == Sales?};
    K -- 是 --> L[调用SalesNode];
    K -- 否 --> M[返回下一阶段];
    L --> M;
```