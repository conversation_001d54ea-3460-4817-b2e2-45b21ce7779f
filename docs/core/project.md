# **Moer Bot**

# 项目介绍

本项目实现了一个 AI 驱动的聊天机器人系统，旨在与客户进行互动。该机器人利用有状态工作流引擎来管理对话，根据客户历史记录、个人资料和当前上下文提供个性化互动。



# 核心功能

**工作流引擎**：通过一系列定义好的节点（service/moer/components/flow/nodes）来协调对话流程。入口点是 Workflow\.step.

**状态管理**：为每个客户维护对话状态（service/moer/storage/chat\_state\_store.ts），使用 MongoDB进行持久化, LRUCache(三方包）作为本地缓存 ，如客户槽位、客户状态和工作流进度。每次加载的时候从缓存加载，更新的时候，写入到数据库。

**RAG（检索增强生成）**：使用 Elasticsearch 实现 RAG 技术，从知识库（例如课程资料、FAQs、销售脚本）中检索信息，以提供与上下文相关的答案

**记忆系统**：随着时间推移存储并回忆客户特定的信息，将聊天记录汇总成记忆条目并存储在向量数据库（Elasticsearch）中（service/moer/components/memory/).

**任务调度**：使用 BullMQ 和 Redis 调度延时任务（lib/schedule, service/moer/components/schedule).



**外部 API 集成**：与各种第三方服务交互：

**Juzi Wecom**：用于接收和发送企业微信消息（lib/juzi/）。

**Moer API**：墨尔提供的API，用于获取墨尔方课程信息、客户数据、支付状态等（model/moer\_api/moer.ts）

**Polyv**：用于获取直播数据，如弹幕（model/polyv/polyv.ts）。

**Yiwise**：用于AI 语音电话/SMS（lib/yiwise/yiwise\_api.ts）。

**Aliyun Services**：OSS 用于文件存储（model/oss, lib/oss），SLS 用于 ASR（model/nls），以及 Elasticsearch.

**Xunfei**：用于 ASR 能力，语音转文字（model/nls/xunfei.ts）。

**SiliconFlow**：用于 RAG 结果重新排序（lib/sliconflow/siliconflow\.ts).



**配置管理**：针对不同环境（开发/生产）的配置，包括 API 密钥、数据库 URI 以及工作流参数（config/)

**工具库**：为 HTTP 请求、文件处理、日期操作、错误处理、缓存、锁定等常见任务提供可重用的组件（lib/).



“Moer” 项目特定的逻辑主要位于 service/moer 目录中，涵盖数据库交互、业务组件（调度、记忆、RAG、脚本）以及核心对话流程逻辑.
主要逻辑分为两部分，1. 定时向客户发送固定话术的 SOP 2. 跟客户对话时的 AI 被动回复

***

**2. Architecture Diagram**

```mermaid 
graph LR
    subgraph External Interfaces
        direction LR
        User(User via Wechat Work)
        JuziAPI(Juzi Wecom API)
    end
    subgraph Bot Core
        direction TB
        MessageMerge(Message Merging Service<br>/lib/juzi/api.ts<br>/service/message/message_merge.ts)
        WorkflowEngine(Workflow Engine<br>/service/moer/components/flow/flow.ts)
        Router(Node Router<br>/service/moer/components/flow/nodes/router.ts)
        NodeExecutor(Node Executor<br>/service/moer/components/flow/nodes/*)
        StateStore(Chat State Store<br>/service/moer/storage/chat_state_store.ts)
        HistoryService(Chat History Service<br>/service/moer/components/chat_history/chat_history.ts)
        MessageSender(Message Sender<br>/service/moer/components/message/message_send.ts)
        TaskScheduler(Task Scheduler<br>/service/moer/components/schedule/schedule.ts)
    end
    subgraph "Backend Services & Models"
        direction TB
        LLMService(LLM Service<br>/lib/ai/llm/LLM.ts)
        RAGService(RAG Service<br>/service/moer/components/rag)
        MemoryService(Memory Service<br>/service/moer/components/memory)
        ScriptService(Script Service<br>/service/moer/components/script)
        DBService(Database Service<br>/service/moer/database)
        OtherModels(Other Models<br>/model/*)
    end
    subgraph "Data Stores & External APIs"
        direction TB
        MongoDB(MongoDB / Prisma<br>/model/mongodb)
        Redis(Redis / BullMQ<br>/model/redis)
        Elasticsearch(Elasticsearch<br>/model/elastic_search)
        LLMAPIs(LLM APIs<br>OpenAI, Azure, Qwen)
        OtherAPIs(Other APIs<br>Moer, Polyv, Yiwise, Aliyun)
    end
    User -- Wechat Message --> JuziAPI
    JuziAPI -- Incoming Message --> MessageMerge
    MessageMerge -- Aggregated Messages --> WorkflowEngine
    WorkflowEngine -- Loads/Saves State --> StateStore
    WorkflowEngine -- Selects Node --> Router
    Router -- Selected Node --> NodeExecutor
    NodeExecutor -- Executes Logic --> WorkflowEngine
    NodeExecutor -- Calls --> LLMService
    NodeExecutor -- Calls --> RAGService
    NodeExecutor -- Calls --> MemoryService
    NodeExecutor -- Calls --> ScriptService
    NodeExecutor -- Calls --> DBService
    NodeExecutor -- Calls --> OtherModels
    NodeExecutor -- Updates --> StateStore
    NodeExecutor -- Records --> HistoryService
    WorkflowEngine -- Sends Reply --> MessageSender
    MessageSender -- Outgoing Message --> JuziAPI
    JuziAPI -- Wechat Message --> User
    StateStore -- Uses --> MongoDB
    StateStore -- Uses --> Redis
    HistoryService -- Uses --> MongoDB
    DBService -- Uses --> MongoDB
    RAGService -- Uses --> Elasticsearch
    RAGService -- Uses --> LLMService
    MemoryService -- Uses --> Elasticsearch
    MemoryService -- Uses --> LLMService
    LLMService -- Calls --> LLMAPIs
    OtherModels -- Calls --> OtherAPIs
    TaskScheduler -- Uses --> Redis
    %% Optional: Add scheduling flow
    NodeExecutor -- Schedules Task --> TaskScheduler
    TaskScheduler -- Triggers --> WorkflowEngine
    %% Styling (optional)
    classDef external fill:#f9f,stroke:#333,stroke-width:2px;
    classDef core fill:#ccf,stroke:#333,stroke-width:2px;
    classDef backend fill:#cfc,stroke:#333,stroke-width:2px;
    classDef datastore fill:#ffc,stroke:#333,stroke-width:2px;
    class User,JuziAPI external;
    class MessageMerge,WorkflowEngine,Router,NodeExecutor,StateStore,HistoryService,MessageSender,TaskScheduler core;
    class LLMService,RAGService,MemoryService,ScriptService,DBService,OtherModels backend;
    class MongoDB,Redis,Elasticsearch,LLMAPIs,OtherAPIs datastore;
```


**Diagram Explanation**

•**External Interfaces**: 客户通过 Wechat 与机器人交互，该过程由 Juzi Wecom API 协调。

•**Message Handling**: 接收到的消息通过 Juzi API 进入 Message Merging Service（消息合并服务），可能进行排队或合并后再传递给核心工作流。

•**Bot Core**:

•**Workflow Engine (Workflow\.step)**是核心调度器。

•从 Chat State Store 加载当前客户状态。

•**Node Router**根据当前状态和客户输入确定下一个逻辑步骤（节点）。

•**Node Executor**执行所选节点内定义的逻辑。

•各节点与 Backend Services & Models 交互，处理复杂任务（如 LLM 调用、RAG、数据库查询）。

•对话状态的变化会更新至 Chat State Store。

•对话历史由 Chat History Service 管理。

•调度的任务由 Task Scheduler 管理。

•最终响应由 Message Sender 通过 Juzi API 发送回客户。

•**Backend Services & Models**: 封装了核心功能，如 LLM 交互、RAG、记忆、数据库访问及与特定模型（OCR、ASR 等）的交互。

•**Data Stores & External APIs**: 提供持久化存储（MongoDB、Redis、Elasticsearch）以及机器人依赖的外部服务 API（LLMs、Moer API 等）。

***

**3. Project Directory Structure**

```
bot                      # Root directory for the bot project
├── types                # Global TypeScript type definitions
│   └── index.d.ts       # Declaration files (e.g., for modules without native types)
├── config               # Configuration files
│   ├── script.ts        # Defines reusable scripts/templates (e.g., sales scripts)
│   ├── prod.ts          # Production environment configuration
│   ├── develop.ts       # Development environment configuration
│   ├── interface.ts     # Interfaces defining the structure of configuration objects
│   ├── config.ts        # Main configuration class, loads appropriate env config
│   └── chat_id.ts       # Utility for generating unique chat IDs
├── model                # Data models and integrations with backend services/APIs
│   ├── web_search       # Web search functionality (Bing API)
│   ├── moer_api         # Interaction logic with the specific Moer backend API
│   ├── logger           # Custom logging setup (Pino, MongoDB/Feishu streams)
│   ├── nls              # Natural Language Services (ASR - Aliyun SLS, Xunfei)
│   ├── redis            # Redis client, caching, and rate limiting logic
│   ├── feishu           # Feishu API interaction logic
│   ├── ocr              # OCR service integration
│   ├── a_btest          # A/B testing utilities
│   ├── rag              # RAG helper functions and core logic
│   ├── mongodb          # Prisma client setup for MongoDB interaction
│   ├── polyv            # Polyv API interaction (live streaming data)
│   ├── oss              # Aliyun OSS interaction helper
│   ├── git_commit_search # (Likely unused or specific feature) Git commit search
│   └── elastic_search   # Elasticsearch client and service logic
├── lib                  # Reusable utility libraries and helper modules
│   ├── tree             # Tree data structure utilities (Node, Walker)
│   ├── auchuang         # (Likely deprecated/alternative) AoChuang Wechat interaction
│   ├── schedule         # Task scheduling utilities (sleep, delayed tasks)
│   ├── cache            # Caching utilities (LRU cache decorator)
│   ├── retry            # Retry logic helper
│   ├── file             # File system utilities (walker, helpers)
│   ├── hash             # Hashing utilities (MD5, SHA)
│   ├── sliconflow       # SiliconFlow API client (for reranking)
│   ├── path             # Path manipulation utilities
│   ├── date             # Date/time utilities and helpers
│   ├── juzi             # Juzi Wecom API client and types
│   ├── yiwise           # Yiwise API client (for automated calls/SMS)
│   ├── cer              # Credentials management (e.g., Aliyun)
│   ├── lock             # Distributed locking using Redlock/Redis
│   ├── wx_biz_api       # Wechat Work Business API interaction
│   ├── regex            # Regular expression helpers
│   ├── url              # URL manipulation utilities
│   ├── mongodb          # Low-level Mongoose connection helpers (potentially replaceable by Prisma in /model)
│   ├── xml              # XML parsing utilities
│   ├── silk             # SILK audio codec utilities (encode/decode)
│   ├── json             # JSON parsing/repair utilities
│   ├── object           # Object manipulation utilities (deep clone, comparison)
│   ├── http             # HTTP request helper (Axios wrapper)
│   ├── ai               # Core AI components
│   │   ├── llm          # LLM clients and base LLM wrapper class
│   │   ├── prompt       # Prompt templates (e.g., MetaPrompt for prompt generation)
│   │   └── tool         # Function calling/tool usage definitions
│   ├── csv              # CSV parsing utilities
│   ├── oss              # Low-level OSS client wrapper (potentially replaceable by /model/oss)
│   ├── string           # String manipulation utilities
│   ├── text             # Text similarity calculation utilities
│   ├── stack            # Stack data structures (e.g., limited stack)
│   ├── random           # Random selection/generation utilities
│   ├── stream_media     # Parsers for streaming media URLs (Bilibili, Douyin)
│   ├── uuid             # UUID generation utilities
│   └── error            # Error handling utilities (e.g., catchError)
└── service              # Business logic specific to different services/projects
    ├── moer             # Business logic specific to the Moer project
    │   ├── database     # Data access layer for Moer-specific collections/tables
    │   ├── getter       # Utilities for fetching Moer-specific data (wraps APIs/DB)
    │   ├── notification # Notification logic (e.g., group notifications)
    │   ├── mock         # Mock data/functions for testing Moer features
    │   ├── storage      # In-memory and persistent state storage logic
    │   ├── components   # Core components of the Moer service
    │   │   ├── schedule # Moer-specific task scheduling logic and tasks
    │   │   ├── memory   # Memory storage and recall components for Moer
    │   │   ├── danmu    # Danmu processing and analysis logic
    │   │   ├── chat_history # Chat history management specific to Moer context
    │   │   ├── message  # Message sending and handling components
    │   │   ├── rag      # Moer-specific RAG implementation (e.g., Sales RAG)
    │   │   ├── script   # Moer-specific conversation scripts/templates
    │   │   ├── human_transfer # Logic for transferring conversations to human agents
    │   │   └── flow     # The core workflow engine logic for Moer
    │   │       ├── schedule # Task definitions and processors for the Moer workflow
    │   │       ├── nodes    # Individual state nodes within the Moer workflow
    │   │       ├── type.ts  # Type definitions for the Moer workflow
    │   │       ├── flow.ts  # Main workflow orchestration logic
    │   │       └── helper   # Helper functions specific to the Moer workflow
    │   └── prompt       # Moer-specific prompt templates, organized by sub-project (moer/baoshu)
    └── message          # General message handling services (potentially overlapping with moer/message)
        ├── chat_history.ts # (Potentially redundant) Chat history related logic
        ├── historys     # Folder likely for storing chat history files (debugging/backup)
        ├── message_merge.ts # Message merging logic
        ├── message.ts   # General message type definitions
        └── message_reply.ts # Core message reply service logic
```