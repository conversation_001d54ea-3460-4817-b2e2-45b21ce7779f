# 客户画像提取与使用技术文档

## 概述
客户画像是对于对话非常重要的，一方面可以用于记忆，记录客户的喜好。另一方面可以用于个性化对话，在 大模型输出的时候进行参考进行个性化输出。
`ExtractUserSlots` 类负责从聊天历史中提取客户画像信息。
`PromptBuilder` 中的 getUserSlots 使用了提取后的客户画像槽位信息，用于组合并作为 Prompt 提示词的一部分。


## 核心组件

### 主要功能

1. **客户槽位提取**：通过 LLM（大型语言模型）处理从聊天历史中提取客户信息
2. **槽位合并**：智能地将新的客户信息与现有数据合并
3. **特殊情况处理**：包含针对某些客户属性（如购买犹豫点）的专门逻辑
4. **数据验证**：确保提取的数据符合预期的模式

### 关键方法

#### `extractUserSlots(chatHistory, chat_id, logInfo)`

处理聊天历史以提取客户画像信息的主要入口点。

- 有条件地提取购买犹豫信息
- 将新提取的数据与现有客户槽位合并
- 保存更新后的聊天信息
- 返回更新后的客户槽位

#### `extract(prompt, schema, logInfo)`

执行核心提取逻辑：
- 向 LLM 发送提示
- 从 XML 响应中提取结构化数据
- 对提取的数据进行架构验证
- 修复并返回提取的信息

#### `mergeUserSlots(prevUserSlots, currentUserSlots)`

智能地组合以前和当前的客户槽位信息：
- 如果值是数组类型，使用 LLM 进行去重后合并
- 如果值是其他类型，则采用新的替换旧的方式

#### `mergeSlotArray(prevValue, value, key)`

用于合并数组类型槽位值的专门方法：
- 使用 LLM 智能地组合数组值
- 包含上下文信息以进行适当的合并
- 如果 LLM 合并失败，则回退到简单的去重后合并

#### `repair(slots)`

清理和验证提取的槽位数据：
- 修剪字符串值
- 过滤掉无效或空值
- 将某些字符串字段转换为数组
- 在某些字段中过滤掉特定的不需要的数据

#### `getUserSlotsText(chat_id)`

检索格式化的客户槽位信息：
- 通过移除不必要的字段来清理数据
- 过滤掉未定义或空值
- 返回客户画像的 JSON 字符串表示

## 客户画像字段

代码处理几个客户画像属性：

- `meditation_experience`：客户以前的冥想经验
- `goals_and_needs`：客户冥想的目标
- `pain_points`：客户希望通过冥想解决的问题和情绪
- `purchase_hesitation`：客户关于购买课程的顾虑
- `meditation_practice_experience`：客户在冥想过程中的感受

## 特殊考虑

### 购买犹豫点提取

系统包含专门的逻辑来提取购买犹豫数据：
- 仅在课程进展到一定程度后提取此信息
- 使用带有特定提示的单独提取方法
- 将提取的犹豫点存储为数组

### 数据验证和修复

代码包含广泛的验证以确保数据质量：
- 过滤掉无效值，如空字符串、"未知"等
- 处理特定字段的数组转换
- 移除特定不需要的数据模式

### 数据遗留问题
- 期待的数据格式为单层结构的对象，比如 {年龄: 19, 爱好: ["唱", "跳"]}, 避免多层嵌套的对象
- 数据解析的时候，必须特别注意处理复杂的嵌套结构: 简单处理方式是将超过2层的嵌套，使用 修正，将 {年龄: 19, 爱好: {唱: 1, 跳: 1}} 修改为 {年龄: 19, 爱好: ["唱", "跳"]} 进行扁平化处理


## 使用示例

```javascript
// 从聊天历史中提取客户槽位
const userSlots = await ExtractUserSlots.extractUserSlots(chatHistory, chat_id);

// 获取格式化的客户槽位文本
const userSlotsText = ExtractUserSlots.getUserSlotsText(chat_id);

// 在个性化交互中使用提取的信息
const personalizedPrompt = ContextBuilder.buildPrompt(userSlotsText);
```

## 依赖项

代码依赖于几个辅助类和服务：
- `LLM`：用于语言模型调用
- `XMLHelper`：用于解析 XML 响应
- `JSONHelper`：用于 从字符串中解析 JSON
- `ChatStateStore`：用于存储聊天状态
- `DataService`：用于数据操作

