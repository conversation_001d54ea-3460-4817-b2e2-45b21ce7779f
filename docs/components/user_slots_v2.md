# 客户画像提取与使用技术文档

### 总体功能
`ExtractUserSlots` 是一个用于智能提取并管理客户信息槽位的模块，主要负责从对话记录中提取客户信息，维护客户的记忆与偏好，支持信息的提取、修复、合并和持久化。

### 主要组成结构
- **UserSlot**
  - 存储单个客户槽位，包括主题（topic）、子主题（subTopic）和具体内容（content）。
  - 提供方法从字符串中创建槽位实例，包括格式验证和修复功能。

- **UserSlots**
  - 管理多个`UserSlot`对象，存储于嵌套字典中（topic→subTopic→content）。
  - 提供槽位合并功能：如果新旧槽位存在相同主题与子主题，会通过语言模型智能合并。

- **ExtractUserSlotsV2**
  - 核心类，负责整体提取流程。
  - 包含`extractUserSlots`方法，输入为对话历史和聊天ID，输出为智能提取后的客户槽位集合。

### 核心流程
1. **提取（extract）**：
   - 从对话历史生成字符串格式的对话记录。
   - 结合预定义的Prompt，通过语言模型提取客户相关信息，输出`UserSlot`列表。
   - 提示词包含以下三大背景信息，确保模型理解对话的上下文：

    1. 公司介绍：帮助LLM了解企业的背景、业务领域及需求。
    2. 训练营信息：包括当前训练营的内容及目标，便于模型理解具体的客户需求。
    3. 阶段信息：对话所处阶段，如售前咨询、产品体验阶段等，确保信息提取精准。

2. **格式验证与修复**：
   - 通过正则表达式验证每个槽位是否符合规定的格式。
   - 若格式不对，则调用语言模型再次修复槽位格式。

3. **槽位合并（merge）**：
   - 将新提取的槽位与已有的客户槽位进行合并。
   - 当新旧槽位主题、子主题相同时，通过语言模型自动整合新旧信息，避免重复或冲突。

4. **数据持久化**：
   - 提取和合并后的槽位数据被持久化保存到存储系统（例如`ChatStateStore`和`DataService`），用于未来的对话上下文与状态管理。
   - 对于客户未直接向AI助手表达，而是通过弹幕等方式间接提出的信息，采取单独处理：弹幕中的客户信息或偏好单独存储于 secret_custom_slot 中，不与公开对话的槽位混合，以便单独分析和管理。

### Prompt设计
- **提取Prompt**：明确规定从对话历史中如何推理和提取客户信息，包括已明确提及的和隐含的偏好与事实。
- **合并Prompt**：定义如何智能合并新旧备忘录信息，详细规范了信息冲突与信息合并的操作原则。

### 功能目标
- **准确性**：通过语言模型的推理与修复功能，确保信息槽位提取和合并的高质量。
- **效率性**：通过模块化封装，支持灵活的客户记忆信息管理。
- **易维护性**：通过清晰的职责划分（如提取、修复、合并、持久化），降低维护成本。

### 使用场景
主要用于需要长期管理客户信息的智能对话系统或智能助理，通过提取客户兴趣、偏好、痛点与需求等信息，精细化管理客户画像并提高交互质量。
