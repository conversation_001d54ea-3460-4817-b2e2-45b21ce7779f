
# 消息处理系统概述

从 webhook 中获取到消息后，需要确保同一个人快速连续发送的多条消息会被分组一起处理，所以会合并一定时间内的客户的消息，进行统一处理。

## 架构组件

### 1. 消息存储

消息分层存储：

- **内存中的 LRU 缓存**：
    - 用于暂时缓存消息 ID，以防止重复处理。
    - 配置最大容量为 3000 个条目。
    - 用于快速查找，以确定消息是否已处理。

- **Redis 缓存**：
    - 消息存储在 Redis 中，使用以下键模式：
        - `user-message-store_{chatId}`：存储客户的待处理消息。
        - `{messageId}`：缓存完整的消息内容，存储 3 小时，用于后续通过 messageId 对消息处理（消息撤回）。

### 2. 消息队列系统

该系统使用 BullMQ（基于 Redis）来管理消息处理：

- 每个账户都有一个自己的队列，命名为 `user-message-queue_{accountId}`。
- 专用的工作者处理消息，最大并发量为 20。
- 消息根据配置的合并时间进行处理。

## 消息流

### 1. 消息接收

当接收到消息时，系统会执行以下步骤：

- 客户消息 → LRU 缓存检查是否重复 → Redis 存储 → BullMQ 队列在设定延迟后取出处理。

### 2. 延迟处理

- 消息按配置的延迟时间添加到队列：
    - **标准账户**：默认延迟时间由配置（`Config.setting.waitingTime.messageMerge`）决定。
    - **测试账户**：延迟 15 秒。
    - **系统自发消息**：无延迟（0 秒）。

- 延迟任务包含以下信息：
    - `userId`：发送者的 ID。
    - `messageId`：消息的 ID。

### 3. 消息批量处理

延迟时间到期后，系统会执行以下操作：

1. 检查该消息是否仍为该客户的最新消息。
2. 如果是最新消息，则将客户在延迟期间发送的所有消息一起处理。如果不是，则等待最新消息的延迟处理。
3. 如果上一个 AI 响应非常接近（5 秒内），系统会添加额外的延迟。

## 消息处理逻辑

### 1. 消息检索与排序

```javascript
// 获取该客户在延迟期间的所有消息
const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
const userMessages = await messageStore.getSetMembers()

// 按时间戳对消息进行排序，以确保正确的顺序
userMessages.sort((msgA, msgB) => {
  return msgA.timestamp - msgB.timestamp
})
```

特别注意事项

1. 消息去重和排序
  - 消息通过唯一的 messageId 进行标识。
  - LRU 缓存提供快速查找，防止重复处理。
  - 消息不一定是按照接收顺序达到的，要按照时间戳进行排序

2. 限流与自然对话流程 
  - 如果最新的 AI 回复是在 5 秒内发送的，系统会添加 5 秒的延迟，以避免当前回复与上条消息回复时间间隔过短。
  - 这样可以避免回复显得过于迅速，创造更加自然的对话流。

3. 清理
  - 处理完的消息会从 Redis 消息存储中移除。
  - 完成的任务会自动从 BullMQ 中删除。