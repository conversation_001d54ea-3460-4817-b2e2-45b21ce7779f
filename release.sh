#!/bin/bash

# 确保脚本在执行时抛出错误立即停止
set -e

# 定义分支名称
DEV_BRANCH="moer_dev_planner"
#PRD_BRANCH="moer10.1"

echo "切换到开发分支 ($DEV_BRANCH)..."
git checkout $DEV_BRANCH

echo "更新开发分支 ($DEV_BRANCH)..."
git pull origin $DEV_BRANCH

#echo "切换到生产分支 ($PRD_BRANCH)..."
#git checkout $PRD_BRANCH
#
#echo "更新生产分支 ($PRD_BRANCH)..."
#git pull origin $PRD_BRANCH
#
#echo "将开发分支 ($DEV_BRANCH) 合并到生产分支 ($PRD_BRANCH)..."
#git merge $DEV_BRANCH

## 解决冲突后（如有），可以取消注释以下行进行推送
## echo "推送合并后的主分支到远程仓库..."
#git push origin $PRD_BRANCH
#
#git checkout $DEV_BRANCH
#
#echo "生产分支已更新"

# 运行部署脚本
npm run deploy