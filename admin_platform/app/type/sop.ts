import { Action, TimeAnchorType } from '../../../bot/service/moer/components/visualized_sop/visualized_sop_type'

export interface Sop {
  id: string
  title: string
  time_anchor: TimeAnchorType
  week: number
  day: number
  time: string
  enable: boolean
  tag: string
  topic: string
  situations: ({
    action: Action[]
  } & {
    conditions: {
      isOrNotIs: boolean
      type: 'fixed' | 'dynamic'
      condition: string
    }[];
  })[];
}