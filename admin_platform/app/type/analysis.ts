export interface AnalysisData {
  chatId:string
  name: string,
  assistant: string,
  chatNumber: number
  courseNo:number
  isPaid: boolean
  phone: string

  isAttendCourseDay1: boolean
  isAttendCourseDay2: boolean
  isAttendCourseDay3: boolean
  isAttendCourseDay4: boolean

  isCompleteCourseDay1: boolean
  isCompleteCourseDay2: boolean
  isCompleteCourseDay3: boolean
  isCompleteCourseDay4: boolean

  isCompleteHomework1: boolean
  isCompleteWealthOrchard: boolean
  isCompleteEnergyTestAnalyze: boolean

  isFillAnyUserSlots: boolean

  isPayBeforeDay4:boolean
  isPayAtDay4:boolean
  isPayAfterDay4:boolean
}