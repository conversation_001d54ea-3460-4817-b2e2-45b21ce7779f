import { SopAnalysisConfig } from '@/app/component/sop_analysis/config'
import { analysisSop } from '../../api/analysis_sop'
import { DataService } from '../../../../bot/service/moer/getter/getData'

export default function Page() {
  const currentCourseNo = DataService.getCurrentWeekCourseNo()
  return <SopAnalysisConfig analysisSop={analysisSop} defaultStartCourseNo={currentCourseNo} defaultEndCourseNo={currentCourseNo}/>
}