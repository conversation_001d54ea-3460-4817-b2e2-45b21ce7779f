import { SopAnalysis } from '@/app/component/sop_analysis'
import { getSopRecordsByCourseNo } from '../api/analysis_sop'
import { queryAllSop } from '../api/sop'
import { DataService } from '../../../bot/service/moer/getter/getData'

export default function Page() {
  const currentCourseNo = DataService.getCurrentWeekCourseNo()
  return <SopAnalysis getSopRecordsByCourseNo={getSopRecordsByCourseNo} queryAllSop={queryAllSop} defaultStartCourseNo={currentCourseNo} defaultEndCourseNo={currentCourseNo}/>
}