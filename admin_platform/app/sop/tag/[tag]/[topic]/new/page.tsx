'use client'

import { use } from 'react'
import { NewSop } from '@/app/component/sop/newSop'
import { getConditionJudgeKeys, getCustomKeys, getLinkSourceVariableTagKeys, getVariableMapKeys, SaveSop } from '@/app/api/sop'
export default function Page({ params }:{params: Promise<{ tag: string, topic:string }>}) {
  const param = use(params)
  const tag = decodeURIComponent(param.tag)
  const topic = decodeURIComponent(param.topic)
  return <NewSop
    tag={tag}
    topic={topic}
    getConditionJudgeKeys={getConditionJudgeKeys}
    getCustomKeys={getCustomKeys}
    getLinkSourceVariableTagKeys={getLinkSourceVariableTagKeys}
    getVariableMapKeys={getVariableMapKeys}
    saveSop={SaveSop}
  />
}
