'use client'

import { use } from 'react'
import { copySop, querySopById, testAction, testSop } from '../../api/sop'
import { SopDetail } from '@/app/component/sop/sopDetail'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const param = use(params)
  return <SopDetail id={param.id} testSop={testSop} querySopById={querySopById} copySop={copySop} testAction={testAction}/>
}
