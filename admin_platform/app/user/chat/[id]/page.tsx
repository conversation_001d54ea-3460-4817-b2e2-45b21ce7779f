'use client'
import { use } from 'react'
import { queryChatHistoryByChatId } from '../../../api/chat_history'
import { queryLogByChatId } from '../../../api/log_store'
import { ChatHistory } from '@/app/component/user/chat_history'
export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory id={id} queryChatHistoryByChatId={queryChatHistoryByChatId} queryLogByChatId={queryLogByChatId} langsmithProjectId='6f6fbd77-99a2-4c1f-9a9c-c76fbc092375'/>
}