'use server'
import { Chat<PERSON><PERSON><PERSON> } from '@/app/type/chat_history'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { chat_history } from '@prisma/client'
import { contentWithFrequency } from '../../../bot/service/moer/storage/chat_state_store'
import { ChatHistoryWithRoleAndDate } from '../type/experience'
import { ChatHistoryService } from '../../../bot/service/moer/components/chat_history/chat_history'
import dayjs from 'dayjs'

export async function queryChatHistoryByChatId(chatId:string):Promise<ChatHistory[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const history = await mongoClient.chat_history.findMany({ where:{ chat_id:chatId }, orderBy:[{ created_at:'asc' }] })
  return history.map((item) => transferChatHistory(item))
}

function transferChatHistory(chatHistory:chat_history):ChatHistory {
  return {
    id: chatHistory.id,
    chat_id: chatHistory.chat_id,
    content: chatHistory.content,
    created_at: chatHistory.created_at,
    role: chatHistory.role,
    is_send_by_human: chatHistory.is_send_by_human,
    short_description: chatHistory.short_description,
    round_id: chatHistory.round_id,
    is_recalled: chatHistory.is_recalled,
    message_id: chatHistory.message_id,
    chat_state: {
      nodeInvokeCount: chatHistory.chat_state?.nodeInvokeCount as Record<string, number>,
      state: chatHistory.chat_state?.state as Record<string, boolean | undefined>,
      nextStage: chatHistory.chat_state?.nextStage ?? '',
      userSlots: chatHistory.chat_state?.moreUserSlots as Record<string, contentWithFrequency>
    }
  }
}

export async function queryChatHistory(
  chatId: string,
): Promise<ChatHistoryWithRoleAndDate[]> {
  const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)
  return chatHistory.map((item) => ({
    id: item.id,
    role: item.role,
    date: dayjs(item.created_at).format('YYYY/MM/DD HH:mm:ss'),
    message: item.content,
  }))
}