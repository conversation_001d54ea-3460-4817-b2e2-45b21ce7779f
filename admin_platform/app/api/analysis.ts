'use server'

import dayjs from 'dayjs'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { AnalysisData } from '../type/analysis'
import { UserSlots } from '../../../bot/service/moer/components/flow/helper/slotsExtract'
import { contentWithFrequency, IChattingState, IUserSlot } from '../../../bot/service/moer/storage/chat_state_store'
import { DataService } from '../../../bot/service/moer/getter/getData'

export async function getAnalysisData(startCourseNo:number, endCourseNo:number):Promise<AnalysisData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const getChatInfo = async(startCourseNo:number, endCourseNo:number) => {
    return await mongoClient.chat.findMany({ where:{  course_no:{ gte:startCourseNo, lte:endCourseNo } }, select:{ id:true, wx_id:true, contact:true, chat_state:true, course_no:true, pay_time:true } })
  }
  const getAccounts = async() => {
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    return await mongoConfigClient.config.findMany({ where:{ enterpriseName:'moer' }, select:{ accountName:true, wechatId:true } })
  }

  const [chatInfo, accounts] = await Promise.all([getChatInfo(startCourseNo, endCourseNo), getAccounts()])
  const accountMap = new Map<string, string>()
  for (const account of accounts) {
    accountMap.set(account.wechatId, account.accountName)
  }
  const filtedChatInfo = chatInfo.filter((item) => item.wx_id != '****************')

  const courseSet = [...new Set<number>(filtedChatInfo.filter((user) => user.course_no != null).map((user) => user.course_no) as number[])]
  const courseStartTimeMap:Record<number, dayjs.Dayjs> = {}

  for (const courseNo of courseSet) {
    courseStartTimeMap[courseNo] = dayjs(await DataService.getCourseStartTimeByCourseNo(courseNo))
  }

  return filtedChatInfo.filter((chat) => !['****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************'].includes(chat.contact.wx_id)).map((chat) => {
    const state = chat.chat_state.state as IChattingState
    const userSlots = UserSlots.fromRecord(chat.chat_state.moreUserSlots as Record<string, contentWithFrequency>)
    return {
      chatId:chat.id,
      name: chat.contact.wx_name,
      phone: (chat.chat_state.userSlots as IUserSlot).phoneNumber ?? '',
      assistant: accountMap.get(chat.wx_id) ?? chat.wx_id,
      chatNumber: (chat.chat_state.nodeInvokeCount as Record<string, number>).UserMessage ?? 0,
      isPaid:state.is_complete_payment ?? false,
      courseNo: chat.course_no ?? 0,
      isCompleteDouyinAnalysis:state.is_complete_douyin_analysis ?? false,
      isCompleteHomework1: state.is_complete_day1_homework_feedback ?? false,
      isAttendCourseDay1: state.is_in_day1_class ?? false,
      isAttendCourseDay2: state.is_in_day2_class ?? false,
      isAttendCourseDay3: state.is_in_day3_class ?? false,
      isAttendCourseDay4: state.is_in_day4_class ?? false,
      isCompleteCourseDay1: state.is_complete_day1_course || state.is_complete_day1_course_recording || false,
      isCompleteCourseDay2: state.is_complete_day2_course || state.is_complete_day2_course_recording || false,
      isCompleteCourseDay3: state.is_complete_day3_course || state.is_complete_day3_course_recording || false,
      isCompleteCourseDay4: state.is_complete_day4_course || state.is_complete_day4_course_recording || false,
      isCompleteWealthOrchard: state.is_complete_wealth_orchard_analyze ?? false,
      isCompleteEnergyTestAnalyze: state.is_complete_energy_test_analyze ?? false,
      isFillAnyUserSlots:userSlots.isTopicSubTopicExist('基本信息', '生活角色') || userSlots.isTopicExist('过往冥想经验') || userSlots.isTopicExist('痛点') || userSlots.isTopicExist('冥想目标'),
      isPayBeforeDay4: Boolean(chat.pay_time && chat.course_no && dayjs(chat.pay_time).isBefore(courseStartTimeMap[chat.course_no].add(3, 'day'))),
      isPayAtDay4:Boolean(chat.pay_time && chat.course_no && dayjs(chat.pay_time).isAfter(courseStartTimeMap[chat.course_no].add(3, 'day')) && dayjs(chat.pay_time).isBefore(courseStartTimeMap[chat.course_no].add(4, 'day'))),
      isPayAfterDay4:Boolean(chat.pay_time && chat.course_no && dayjs(chat.pay_time).isAfter(courseStartTimeMap[chat.course_no].add(4, 'day')))
    }
  })
}

export async function updateAttendAndCompleteCourseData() {
  const threeDaysAgo = Number(dayjs().subtract(3, 'day').format('YYYYMMDD'))
  const mongoClient = PrismaMongoClient.getInstance()
  const users = (await mongoClient.chat.findMany({ where:{ course_no:{ gte:threeDaysAgo } } }))
  for (let i = 0; i < users.length; i += 10) {
    await Promise.allSettled(users.slice(i, i + 10).map((user) => Promise.allSettled([
      DataService.isInClass(user.id, { day:1 }),
      DataService.isInClass(user.id, { day:1, is_recording:true }),
      DataService.isInClass(user.id, { day:2 }),
      DataService.isInClass(user.id, { day:2, is_recording:true }),
      DataService.isInClass(user.id, { day:3 }),
      DataService.isInClass(user.id, { day:3, is_recording:true }),
      DataService.isInClass(user.id, { day:4 }),
      DataService.isInClass(user.id, { day:4, is_recording:true }),
      DataService.isCompletedCourse(user.id, { day:1 }),
      DataService.isCompletedCourse(user.id, { day:1, is_recording:true }),
      DataService.isCompletedCourse(user.id, { day:2 }),
      DataService.isCompletedCourse(user.id, { day:2, is_recording:true }),
      DataService.isCompletedCourse(user.id, { day:3 }),
      DataService.isCompletedCourse(user.id, { day:3, is_recording:true }),
      DataService.isCompletedCourse(user.id, { day:4 }),
      DataService.isCompletedCourse(user.id, { day:4, is_recording:true }),
    ])))
  }
}
