import { CsvHelper } from '../../../bot/lib/csv/csv_parse'
import {
  ActionType,
  Condition,
  Sop,
  TextType
} from '../../../bot/service/moer/components/visualized_sop/visualized_sop_type'


export class SopCsv {
  public static async parseCsv(filePath: string) {
    // 读取并解析CSV文件
    const csvData = await CsvHelper.read(filePath)

    //拆出表头和数据列
    const [headers, ...rows] = csvData

    this.checkCsvTitle(headers)

    const res: Sop[] = []
    for (const row in rows) {
      const sop = this.extractRowData(rows[row])
      res.push(sop)
    }

    return res
  }



  private static checkCsvTitle(headers: string[]) {

    // 检查表头是否符合要求
    const requiredHeaders = ['周', '日', '时间', '标题', '是否一', '条件一', '是否二', '条件二', '是否三', '条件三', '内容']
    const missingHeaders = requiredHeaders.filter((header) => !headers.includes(header))
    if (missingHeaders.length > 0) {
      throw new Error(`CSV文件缺少以下列：${missingHeaders.join(', ')}`)
    }
  }

  private static extractRowData(row: string[]): Sop {

    const conditions: Condition[] = []
    const indexes = [5, 7, 9]
    indexes.forEach((index) => {
      if (row[index] !== '') {
        conditions.push({
          isOrNotIs: row[index - 1] == '是',
          type:'fixed',
          condition: row[index],
        })
      }
    })
    console.log(row[3])

    return {
      week: parseInt(row[0], 10),
      day: parseInt(row[1], 10),
      time_anchor:'course',
      title: row[2],
      time: row[3],
      situations: [
        {
          conditions: conditions,
          action: [
            {

              type: ActionType.text,
              description:row[2],
              textList: [
                {
                  type: TextType.fixed,
                  text: row[10],
                },
              ],
            },
          ],
        },
      ],
      enable: true,
      tag: 'moer',
      topic: 'default'
    }
  }

}
