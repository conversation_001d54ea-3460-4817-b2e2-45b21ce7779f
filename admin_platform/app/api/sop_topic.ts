'use server'
import { SopTopic } from '@/app/type/sop_topic'
import { Sop } from '@/app/type/sop'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'

export async function querySopTopicByTag(tag:string): Promise<SopTopic[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.sop_topic.findMany({ where:{ tag } })
  return result
}

export async function changeSopTopicEnable(id:string, enable:boolean): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.sop_topic.update({ where:{ id }, data:{ enable } })
}

export async function createTopic(tag:string, topic:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.sop_topic.create({ data:{
    name:topic,
    enable:false,
    tag
  } })
}

export async function deleteTopic(id: string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const topicInfo = await mongoClient.sop_topic.findFirst({ where:{ id } })
  if (!topicInfo) {
    throw (`没有找到topic ${id}`)
  }
  await mongoClient.sop_topic.delete({ where:{ id } })
  await mongoClient.sop.deleteMany({ where:{ tag:topicInfo.tag, topic:topicInfo.name } })
}

export async function queryAllSopTopics(): Promise<SopTopic[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  return await mongoClient.sop_topic.findMany()
}

export async function copyTopicToTag(topicId:string, tagName:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()

  // copy topic
  const topicInfo = await mongoClient.sop_topic.findFirst({ where:{ id:topicId } })
  if (!topicInfo) {
    throw (`没有找到对应的topic, topic_id:${topicId}`)
  }
  const newTopicName = `${topicInfo.name}_copy`
  await mongoClient.sop_topic.create({ data:{ tag: tagName, name:newTopicName, enable:false } })
  const sopInfo = await mongoClient.sop.findMany({ where: { tag:topicInfo.tag, topic:topicInfo.name } })
  await mongoClient.sop.createMany({ data:sopInfo.map((item) => ({ ...item, id:undefined, tag:tagName, topic:newTopicName } as unknown as Sop)) })
}

export async function renameTopic(topicId:string, name:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const topicInfo = await mongoClient.sop_topic.findFirst({ where:{ id:topicId } })
  if (!topicInfo) {
    throw (`没有找到对应的topic, topic_id:${topicId}`)
  }

  await mongoClient.sop_topic.update({ where:{ id:topicId }, data:{ name:name } })
  await mongoClient.sop.updateMany({ where:{ tag: topicInfo.tag, topic: topicInfo.name }, data:{ topic: name } })
}

export async function importSop(tag:string, topic:string, sop:Omit<Sop, 'id'>[]):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const sopWithNewTagAndTopic = sop.map((item) => ({ ...item, id:undefined, tag, topic }))
  await mongoClient.sop.createMany({ data:sopWithNewTagAndTopic })
}