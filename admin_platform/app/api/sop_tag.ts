'use server'

import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { SopTag } from '../type/sop_tag'

export async function queryTags({ page, pageSize }:{ page:number, pageSize:number}):Promise<SopTag[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const tags = await mongoClient.sop_tag.findMany({ take:pageSize, skip:pageSize * (page - 1) })
  return tags
}
export async function createTag(name:string, enableAccounts:string[]) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.sop_tag.create({ data:{ name, enable:false, enable_account:enableAccounts } })
}

export async function deleteTag(id: string) {
  const mongoClient = PrismaMongoClient.getInstance()
  const tag = await mongoClient.sop_tag.findFirst({ where:{ id } })
  if (!tag) {
    throw (`没有找到${id}`)
  }
  await mongoClient.sop_tag.delete({ where:{ id } })
  await mongoClient.sop_topic.deleteMany({ where:{ tag:tag.name } })
  await mongoClient.sop.deleteMany({ where:{ tag:tag.name } })
}

export async function changeTagEnable(tag_id: string, enable:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.sop_tag.update({ where:{ id:tag_id }, data:{ enable } })
}

export async function updateTagEnableAccount(tag_id:string, enableAccounts:string[]) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.sop_tag.update({ where:{ id:tag_id }, data:{ enable_account:enableAccounts } })
}

export async function queryAllTags():Promise<SopTag[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const tags = await mongoClient.sop_tag.findMany()
  return tags
}