'use server'
import { Document } from 'langchain/document'
import ElasticSearchService from '../../../bot/model/elastic_search/elastic_search'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { RAGHelper } from '../../../bot/model/rag/rag'
import { Experience, ExperienceRecall } from '../type/experience'

const ragIndexExperiencePain = 'experience_pain'
const ragIndexExperienceGoal = 'experience_goal'
const ragIndexExperienceMessage = 'experience_message'

export async function queryExperience(page:number, pageSize:number): Promise<Experience[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const experiences = await mongoClient.experience.findMany({ orderBy:{ created_at:'desc' }, take:pageSize, skip:(page - 1) * pageSize })
  return experiences
}

export async function insertExperience(experienceId: string):Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  const experience = await mongoClient.experience.findFirst({ where:{ id:experienceId } })
  if (!experience) {
    throw (`没有找到经验 id: ${experienceId}`)
  }
  const {
    job, course_feeling, course_status, goal, pain, user_message, strategy
  } = experience
  const tag = `${job}_${course_status}_${course_feeling}`
  const searchPainResults = await ElasticSearchService.embeddingSearch(ragIndexExperiencePain, pain, 1, 0.8, [{ 'term':{ 'metadata.tag':tag } }])
  if (searchPainResults.length > 0) {
    const prePain = searchPainResults[0].pageContent
    const searchGoalResults = await ElasticSearchService.embeddingSearch(ragIndexExperiencePain, goal, 1, 0.8, [{ 'term':{ 'metadata.tag':tag } }, { 'term':{ 'metadata.pain':prePain } }])
    if (searchGoalResults.length > 0) {
      const preGoal = searchGoalResults[0].pageContent
      await RAGHelper.addDocuments(ragIndexExperienceMessage, [new Document({ pageContent:user_message, metadata:{ tag:tag, pain:prePain, goal:preGoal, strategy:strategy } })])
    } else {
      await RAGHelper.addDocuments(ragIndexExperienceGoal, [new Document({ pageContent:goal, metadata:{ tag:tag, pain:prePain } })])
      await RAGHelper.addDocuments(ragIndexExperienceMessage, [new Document({ pageContent:user_message, metadata:{ tag:tag, pain:prePain, goal:goal, strategy:strategy } })])
    }
  } else {
    await RAGHelper.addDocuments(ragIndexExperiencePain, [new Document({ pageContent:pain, metadata:{ tag:tag } })])
    await RAGHelper.addDocuments(ragIndexExperienceGoal, [new Document({ pageContent:goal, metadata:{ tag:tag, pain:pain } })])
    await RAGHelper.addDocuments(ragIndexExperienceMessage, [new Document({ pageContent:user_message, metadata:{ tag:tag, pain:pain, goal:goal, strategy:strategy } })])
  }
  await mongoClient.experience.update({ where:{ id:experienceId }, data:{ is_add:true, is_handled:true } })
}

export async function searchExperience(job:string, courseStatus:string, courseFeeling:string, pain:string, goal:string, userMessage:string): Promise<ExperienceRecall[]> {
  const tag = `${job}_${courseStatus}_${courseFeeling}`
  const searchPainResults = await ElasticSearchService.embeddingSearch(ragIndexExperiencePain, pain, 1, 0.8, [{ 'term':{ 'metadata.tag':tag } }])
  console.log('pain', searchPainResults)
  if (searchPainResults.length <= 0) {
    return []
  }
  const prePain = searchPainResults[0].pageContent
  const searchGoalResults = await ElasticSearchService.embeddingSearch(ragIndexExperienceGoal, goal, 1, 0, { 'bool':{ 'should':[{ 'term':{ 'metadata.tag':tag } }, { 'term':{ 'metadata.pain':prePain } }] } })
  if (searchGoalResults.length <= 0) {
    return []
  }
  const preGoal = searchGoalResults[0].pageContent
  const searchMessageResults = await ElasticSearchService.embeddingSearch(ragIndexExperienceMessage, userMessage, 5, 0.8, { 'bool':{ 'should':[{ 'term':{ 'metadata.tag':tag } }, { 'term':{ 'metadata.pain':prePain } }, { 'term':{ 'metadata.goal':preGoal } }] } })

  return searchMessageResults.map((item) => ({
    job:job,
    course_feeling:courseFeeling,
    course_status:courseStatus,
    user_message: item.pageContent,
    pain: item.metadata.pain,
    goal: item.metadata.goal,
    strategy: item.metadata.strategy,
    message_score:item.score,
    pain_score: searchPainResults[0].score,
    goal_score: searchGoalResults[0].score
  }))
}

export async function dropExperience(experienceId:string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.experience.update({ where:{ id:experienceId }, data:{
    is_add:false,
    is_handled:true
  } })
}

export async function updateExperienceStrategy(experienceId:string, strategy: string): Promise<void> {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.experience.update({ where:{ id:experienceId }, data:{ strategy:strategy } })
}