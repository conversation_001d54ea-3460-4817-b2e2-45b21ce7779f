'use client'

import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { AiOutlineFolderOpen } from 'react-icons/ai'
import { BsBoxSeamFill } from 'react-icons/bs'
import { FaDatabase } from 'react-icons/fa'
import { MdOutlineViewTimeline } from 'react-icons/md'
export interface MenuItem {
  name: string;
  link: string;
  icon: React.ReactNode;
  children?: MenuItem[];
}

function SideBarMenuItem({ menuItem }: { menuItem: MenuItem }) {
  const pathname = usePathname()
  if (menuItem.children) {
    return (
      <li>
        <details open={pathname.startsWith(menuItem.link)}>
          <summary>
            {menuItem.icon}
            {menuItem.name}
          </summary>
          <ul>
            {menuItem.children &&
              menuItem.children.map((child, index) => (
                <SideBarMenuItem key={index} menuItem={child} />
              ))}
          </ul>
        </details>
      </li>
    )
  } else {
    return (
      <li>
        <a
          href={menuItem.link}
          className={pathname == menuItem.link ? 'menu-active' : ''}
        >
          {menuItem.icon}
          {menuItem.name}
        </a>
      </li>
    )
  }
}

export function Menu({ menu }: { menu: MenuItem[] }) {
  return (
    <ul className="menu sticky top-[calc(3rem)] min-h-[calc(100dvh-3rem)] w-56 flex-none self-start border border-t-0 border-l-0 border-base-200 bg-base-200 pt-[1rem]">
      {menu.map((item, index) => (
        <SideBarMenuItem menuItem={item} key={index} />
      ))}
    </ul>
  )
}
const menu: MenuItem[] = [
  {
    name: 'RAG',
    link: '/rag',
    icon: <AiOutlineFolderOpen />,
  },
  {
    name: 'userslot simulator',
    link: '/userslot_simulator',
    icon: <BsBoxSeamFill />,
    children: [
      {
        name: 'userslot simulator',
        link: '/userslot_simulator',
        icon: <BsBoxSeamFill />,
      },
      {
        name: 'model contrast',
        link: '/userslot_simulator/model_contrast',
        icon: <BsBoxSeamFill />,
      },
    ],
  },
  {
    name: 'experience',
    link: '/experience',
    icon: <FaDatabase />,
    children: [
      {
        name: 'presentation',
        link: '/experience',
        icon: <FaDatabase />,
      },
      {
        name: 'search',
        link: '/experience/search',
        icon: <FaDatabase />,
      },
    ],
  },
  {
    name: '数据采集',
    link: '/collect',
    icon: <FaDatabase />,
    children: [
      {
        name: 'presentation',
        link: '/experience',
        icon: <FaDatabase />,
      },
      {
        name: 'search',
        link: '/experience/search',
        icon: <FaDatabase />,
      },
      {
        name: 'collect',
        link: '/experience/collect',
        icon: <FaDatabase />,
      },
    ],
  },
  {
    name: 'sop',
    link: '/sop',
    icon: <MdOutlineViewTimeline />,
  },
  {
    name: 'user',
    link: '/user',
    icon: <MdOutlineViewTimeline />,
  },
  {
    name: 'analysis',
    link: '/analysis',
    icon: <MdOutlineViewTimeline />,
  },
  {
    name: 'sop_analysis',
    link: '/sop_analysis',
    icon: <MdOutlineViewTimeline />,
  },
]
const visitorMenu: MenuItem[] = [
  {
    name: 'user',
    link: '/user',
    icon: <MdOutlineViewTimeline />,
  },
]

export function MyMenu() {
  'use client'
  const pathname = usePathname()
  const { data:session } = useSession()
  if (pathname != '/login') {
    if (session?.user?.name == 'freespirit') {
      return <Menu menu={menu}/>
    } else {
      return <Menu menu={visitorMenu}/>
    }
  } else {
    return <></>
  }
}