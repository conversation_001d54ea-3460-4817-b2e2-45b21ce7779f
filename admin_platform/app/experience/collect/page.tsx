'use client'

import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { motion } from 'motion/react'
import { RiRocket2Fill } from 'react-icons/ri'
import { FaDeleteLeft } from 'react-icons/fa6'
import { useSearchParams } from 'next/navigation'
import { ChatHistoryWithRoleAndDate } from '../../type/experience'
import { queryChatHistory } from '../../api/chat_history'

type ChatHistoryWithIsChecked = {
  chatHistory: ChatHistoryWithRoleAndDate;
  isChecked: boolean;
};

export default function Page() {
  const [chatHistory, setChatHistory] = useState<ChatHistoryWithIsChecked[]>(
    [],
  )
  const [chatId, setChatId] = useState<string>('')
  const [job, setJob] = useState<string>('')
  const [courseStatus, setCourseStatus] = useState<string>('')
  const [courseFeeling, setCourseFeeling] = useState<string>('')
  const [pain, setPain] = useState<string>('')
  const [goal, setGoal] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false)
  const [openForm, setOpenForm] = useState<boolean>(false)
  const [focusMessageId, setFocusMessageId] = useState<string>('')
  const searchParams = useSearchParams()
  useEffect(() => {
    const chatId = searchParams.get('chat_id')
    const content = searchParams.get('content')
    if (!chatId) return
    if (content) {
      query(chatId, decodeURI(content))
    } else {
      query(chatId)
    }
  }, [searchParams])
  useEffect(() => {
    document
      .getElementById(focusMessageId)
      ?.scrollIntoView({ block: 'center', behavior: 'smooth' })
  }, [focusMessageId])
  const query = async (chatId: string, content?: string) => {
    setChatId(chatId)
    toast
      .promise(queryChatHistory(chatId), {
        pending: 'query is pending',
        success: 'query resolved 👌',
        error: 'query rejected 🤯',
      })
      .then((res) => {
        let maxSimilarity = 0
        let maxSimilarityMessageId: string = ''
        if (content) {
          for (let i = 0; i < res.length; i++) {
            const similar = similarity(res[i].message, content)
            if (similar > maxSimilarity) {
              maxSimilarity = similar
              maxSimilarityMessageId = res[i].id
            }
          }
        }
        setChatHistory(
          res.map((item) => {
            if (
              content &&
              item.role == 'user' &&
              maxSimilarityMessageId == item.id
            ) {
              setFocusMessageId(item.id)
              setOpenForm(true)
              return { isChecked: true, chatHistory: item }
            }
            return { isChecked: false, chatHistory: item }
          }),
        )
      })
      .finally(() => {
        setLoading(false)
      })
  }
  return (
    <div className="mx-auto max-w-[90rem] p-8">
      <form
        onSubmit={() => {
          setLoading(true)
          setOpenForm(false)
        }}
        action={(form) => {
          const chatId = form.get('chat_id') as string
          query(chatId)
        }}
        className="flex w-96 items-center gap-4 self-start"
      >
        <input
          type="text"
          className="input validator focus-within:outline-0"
          name="chat_id"
          placeholder="Type chat_id here"
          disabled={loading}
          required
        />
        <button
          type="submit"
          disabled={loading}
          className="btn btn-neutral disabled:btn-disabled focus-within:outline-0"
        >
          query
        </button>
      </form>
      <div className="divider"></div>
      <div className="mt-4 flex flex-col gap-4">
        {chatHistory.length >= 1 && (
          <div>
            {chatHistory.map((item) => {
              return (
                <div
                  key={item.chatHistory.id}
                  className="flex items-center"
                  id={item.chatHistory.id}
                >
                  {item.chatHistory.role == 'user' && (
                    <input
                      type="radio"
                      name="chat_history_id"
                      className="radio"
                      value={item.chatHistory.id}
                      checked={item.isChecked}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setChatHistory((chatHistory) => {
                            return chatHistory.map((info) => {
                              return {
                                ...info,
                                isChecked:
                                  info.chatHistory.id == item.chatHistory.id,
                              }
                            })
                          })
                          setOpenForm(true)
                        }
                      }}
                    />
                  )}
                  <div
                    className={`chat flex-1 ${item.chatHistory.role == 'user' ? 'chat-start' : 'chat-end'}`}
                  >
                    <div className="chat-header">{item.chatHistory.date}</div>
                    <div className="chat-bubble max-w-7/12">
                      {item.chatHistory.message}
                    </div>
                  </div>
                </div>
              )
            })}
            <div className="fixed right-20 bottom-30 flex gap-4">
              <motion.div
                initial={false}
                animate={{
                  opacity: openForm ? [null, 1] : [null, 0],
                  scaleX: openForm ? [null, 1] : [null, 0],
                  scaleY: openForm ? [null, 1] : [null, 0],
                  transition: {
                    duration: 0.2,
                  },
                  transformOrigin: 'bottom right',
                }}
                className="top-0 left-0 rounded-2xl"
              >
                <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
                  <span className="text-lg"> Case collect</span>
                  <label className='label'>工作</label>
                  <select className='select focus-within:outline-0' value={job} onChange={(e) => { setJob(e.currentTarget.value) }}>
                    <option disabled={true}></option>
                    <option>退休精进者</option>
                    <option>家庭管理者</option>
                    <option>职场奋斗者</option>
                    <option>未知</option>
                  </select>
                  <label className='label'>课程完成情况</label>
                  <select className='select focus-within:outline-0' value={courseStatus} onChange={(e) => { setCourseStatus(e.currentTarget.value) }}>
                    <option disabled={true}></option>
                    <option>未完成任何课程</option>
                    <option>完成了小部分课程</option>
                    <option>完成了大部分课程</option>
                  </select>
                  <label className='label'>课程练习感受</label>
                  <select className='select focus-within:outline-0' value={courseFeeling} onChange={(e) => { setCourseFeeling(e.currentTarget.value) }}>
                    <option disabled={true}></option>
                    <option>感受很好</option>
                    <option>感觉一般</option>
                    <option>感受很差</option>
                    <option>未知</option>
                  </select>

                  <label className='label'>pain</label>
                  <input type="text" className='input' value={pain} onChange={(e) => { setPain(e.currentTarget.value) }}/>

                  <label className='label'>goal</label>
                  <input type="text" className='input' value={goal} onChange={(e) => { setGoal(e.currentTarget.value) }}/>

                  <button
                    type="submit"
                    className="btn btn-neutral disabled:btn-disabled mt-4"
                    disabled={loading}
                    onClick={() => {
                      console.log(chatId)
                    }}
                  >
                    反思
                  </button>
                  <button
                    type="submit"
                    className="btn btn-neutral disabled:btn-disabled mt-4"
                    disabled={loading}
                  >
                    提交
                  </button>
                </fieldset>
              </motion.div>
              <label className="btn btn-neutral btn-circle swap swap-rotate self-end">
                <input
                  type="checkbox"
                  onChange={(e) => {
                    const value = e.target.checked
                    setOpenForm(value)
                  }}
                  checked={openForm}
                />

                <svg
                  className="swap-off fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 512 512"
                >
                  <path d="M64,384H448V341.33H64Zm0-106.67H448V234.67H64ZM64,128v42.67H448V128Z" />
                </svg>

                <svg
                  className="swap-on fill-current"
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  viewBox="0 0 512 512"
                >
                  <polygon points="400 145.49 366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49" />
                </svg>
              </label>
            </div>
          </div>
        )}
        <button
          className="btn btn-neutral btn-circle fixed right-10 bottom-30"
          onClick={() => {
            window.scrollTo({ top: 0, behavior: 'smooth' })
          }}
        >
          <RiRocket2Fill />
        </button>
      </div>
    </div>
  )
}

function levenshteinDistance(s1: string, s2: string): number {
  const len1 = s1.length
  const len2 = s2.length
  const dp: number[][] = Array.from({ length: len1 + 1 }, () =>
    Array(len2 + 1).fill(0),
  )

  for (let i = 0; i <= len1; i++) dp[i][0] = i
  for (let j = 0; j <= len2; j++) dp[0][j] = j

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      if (s1[i - 1] === s2[j - 1]) {
        dp[i][j] = dp[i - 1][j - 1]
      } else {
        dp[i][j] = Math.min(
          dp[i - 1][j] + 1,
          dp[i][j - 1] + 1,
          dp[i - 1][j - 1] + 1,
        )
      }
    }
  }

  return dp[len1][len2]
}

function similarity(s1: string, s2: string): number {
  const distance = levenshteinDistance(s1, s2)
  const maxLength = Math.max(s1.length, s2.length)
  return maxLength === 0 ? 1 : 1 - distance / maxLength
}
