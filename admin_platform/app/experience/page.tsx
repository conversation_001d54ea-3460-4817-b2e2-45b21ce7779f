import { dropExperience, queryExperience, updateExperienceStrategy } from '../api/experience'
import { ExperiencePresentation } from '../component/experience/experience_presentation'

export default async function Page({ searchParams }:{
  searchParams: Promise<{ page?:string, pageSize?: string }>;
}) {
  const searchParam = await searchParams
  const page = Number(searchParam.page ?? 1)
  const pageSize = Number(searchParam.pageSize ?? 20)
  return <ExperiencePresentation page={page} pageSize={pageSize} queryExperience={queryExperience} dropExperience={dropExperience} updateExperienceStrategy={updateExperienceStrategy}/>
}