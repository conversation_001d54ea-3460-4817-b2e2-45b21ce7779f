'use client'
import Image from 'next/image'
import logo from '../favicon.ico'
import { signIn } from 'next-auth/react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

export default function Login() {
  const [loading, setLoading] = useState<boolean>(false)
  const router = useRouter()
  return (
    <div className="bg-[#C9C9E1] h-screen flex flex-col">
      <div className="flex justify-center items-center flex-1">
        <div className="bg-white flex justify-center items-center rounded-xl overflow-hidden">
          <div className="w-96 h-[35rem] bg-[#E7E7F0] flex justify-center items-center relative">
            <div className="flex flex-col justify-between items-center">
              <Image
                src={logo.src}
                width={logo.width / 2}
                height={logo.height / 2}
                alt="logo"
              />
              <div className="text-4xl font-semibold font-mono">
                admin_platform
              </div>
            </div>
          </div>
          <form className="w-96 h-full flex justify-center items-center" action={async (form) => {
            const account = form.get('account')
            const password = form.get('password')
            setLoading(true)
            const id = toast.loading('loading')
            const res = await signIn('credentials', {
              account,
              password,
              redirect:false
            })
            if (res?.ok && !res?.error) {
              toast.update(id, { render: 'login success', type: 'success', isLoading: false, autoClose:2000 })
              window.location.assign('/')
            } else {
              toast.update(id, { render: 'account or password wrong', type: 'error', isLoading: false, autoClose:2000 })
            }
            setLoading(false)
          }}>
            <fieldset className="fieldset w-80 m-2">
              <legend className="fieldset-legend">What is your account?</legend>
              <input type="text" className="input" required minLength={3} name='account' placeholder="Type here" />
              <legend className="fieldset-legend">What is your password?</legend>
              <input type="text" className="input" required minLength={3} name='password' placeholder="Type here" />
              <button className='btn mt-4 btn-neutral disabled:btn-disabled' type='submit' disabled={loading}>login</button>
            </fieldset>
          </form>
        </div>
      </div>
    </div>
  )
}
