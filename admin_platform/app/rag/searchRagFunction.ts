'use server'
import { Document } from 'langchain/document'
import { AliyunCredentials } from '../../../bot/lib/cer'
import ElasticSearchService, { ElasticSearchClient, } from '../../../bot/model/elastic_search/elastic_search'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { FreeSpiritOss } from '../../../bot/model/oss/oss'
import { RAGHelper } from '../../../bot/model/rag/rag'
import { MoerGeneralRAG } from '../../../bot/service/moer/components/rag/moer_general'

const index = MoerGeneralRAG.index
const ossPrefix = 'http://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/'

AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})
export async function searchRag({
  question,
  number,
  minScore,
}: {
  question: string
  number: number
  minScore: number
  docFilter: string[]
}) {
  'use server'
  const res = await ElasticSearchService.embeddingSearch(
    index,
    question,
    number,
    minScore,
  )
  return res
}

export async function deleteRag(id: string, resourceNames: string[]) {
  'use server'
  if (resourceNames.length > 0) {
    const res = await PrismaMongoClient.getInstance().rag_map_oss.findMany({
      where: {
        rag_resource_name: {
          in: resourceNames,
        },
      },
    })
    const bucket = new FreeSpiritOss('static')
    const ossNameList = res.map((item) =>
      item.oss_url.substring(ossPrefix.length),
    )
    if (ossNameList.length > 0) {
      await bucket.deleteObjects(ossNameList)
    }
    await PrismaMongoClient.getInstance().rag_map_oss.deleteMany({
      where: {
        rag_resource_name: {
          in: resourceNames,
        },
      },
    })
  }
  await ElasticSearchClient.getInstance().delete({
    id,
    index,
  })
}

export async function updateRag({
  question,
  answer,
  doc,
  supportVersion,
  id,
}: {
  question: string
  answer: string
  doc: string
  supportVersion: string
  id: string
}) {
  const document = new Document({
    metadata: {
      q: question,
      a: answer,
      supportVersion: supportVersion,
      doc: doc,
    },
    pageContent: question,
  })

  await RAGHelper.addDocumentsWithIds(index, [document], [id])
}
