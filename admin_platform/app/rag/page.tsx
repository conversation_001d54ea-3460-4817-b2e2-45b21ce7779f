'use server'
import AddRag from './addRag'
import { add } from './addRagFunction'
import SearchRag from './searchRag'

export default async function Page() {
  return (
    <div className="px-16 py-16 pt-8">
      <h3 className="text-3xl leading-16">ElasticSearch QA 管理</h3>
      <div className="tabs tabs-box mt-6 pt-[1rem] pr-[1rem] pb-[1rem] pl-[1rem]">
        <input
          type="radio"
          name="tabs"
          className="tab"
          aria-label="搜索"
          defaultChecked
        />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <SearchRag />
        </div>

        <input type="radio" name="tabs" className="tab" aria-label="添加" />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <AddRag add={add} />
        </div>
      </div>
    </div>
  )
}
