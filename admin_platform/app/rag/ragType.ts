export enum RagType {
  text = 0,
  file = 1,
}

export type Rag = {
  type: RagType;
  key: string;
};

export type RagContent = {
  question: string;
  answer: string;
  doc: string;
};

export const ragDoc: Record<string, string> = {
  '通用文档': '常规问题全局.xlsx',
  '通用文档（海外）': '常规问题全局(海外).xlsx',
  '课程前一周': '开营班会',
  '常规问题周三八点前': '常规问题周三八点前.xlsx',
  '常规问题周三八点前（海外）': '常规问题周三八点前(海外).xlsx',
  'day1': '第一天课程-情绪减压.docx',
  'day2': '第二天课程-财富果园.docx',
  'day3': '第三天课程-效能提升.docx',
  '系统班全通班相关':'系统班全通班逐字稿',
  '冥想问题':'冥想问题.xlsx',
  '销售期': '销售问题.xlsx',
  '销售期（海外）': '销售问题(海外).xlsx',
}

export const supportVersionDoc: Record<string, string> = {
  '通用': '',
  '国内版本': '国内版本',
  '小红书版本': '小红书版本'
}