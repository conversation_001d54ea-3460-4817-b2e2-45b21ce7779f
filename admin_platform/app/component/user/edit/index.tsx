'use client'
import { UserData } from '@/app/type/user'
import { useState, useEffect } from 'react'
import { toast } from 'react-toastify'
import { ChatEdit } from './edit'

export default function UserEdit<T>({
  id,
  changeCourseNo,
  changeNextStage,
  changePhone,
  queryChatById,
  stageOption
}: {
    id:string
    queryChatById(id: string): Promise<UserData | null>
    changeNextStage(chatId: string, stage: T): Promise<void>
    changePhone(chatId: string, phone: string): Promise<void>
    changeCourseNo(chatId: string, courseNo: number): Promise<void>
    stageOption:string[]
}) {
  const [chat, setChat] = useState<UserData | null>(null)
  useEffect(() => {
    toast.promise(queryChatById(id).then((result) => {
      setChat(result)
    }), {
      pending:'query pending',
      error: 'query error',
      success: 'query success'
    })
  }, [id])
  if (chat == null) {
    return <span className="loading loading-dots loading-xl"></span>
  } else {
    return <ChatEdit chat={chat} changeCourseNo={changeCourseNo} changeNextStage={changeNextStage} changePhone={changePhone} stageOption={stageOption} />
  }
}