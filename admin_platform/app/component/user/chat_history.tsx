'use client'

import { ChatHistory as ChatHistoryData } from '@/app/type/chat_history'
import { LogStore } from '@/app/type/log_store'
import dayjs from 'dayjs'
import Link from 'next/link'
import { useState, useEffect, ReactNode } from 'react'
import { RiRocket2Fill } from 'react-icons/ri'
import { toast } from 'react-toastify'
import { IoMdArrowDropdown } from 'react-icons/io'
import { motion } from 'motion/react'

export function ChatHistory(
  {
    id,
    langsmithProjectId,
    queryChatHistoryByChatId,
    queryLogByChatId,
  }:
  {
    id:string,
    queryChatHistoryByChatId(chatId: string): Promise<ChatHistoryData[]>
    queryLogByChatId(chatId: string): Promise<LogStore[]>
    langsmithProjectId: string
  }) {
  const [chatHistory, setChatHistory] = useState<ChatHistoryData[]>([])
  const [chatLog, setChatLog] = useState<LogStore[]>([])
  const [firstLoad, setFirstLoad] = useState<boolean>(false)
  useEffect(() => {
    toast.promise(Promise.all([queryChatHistoryByChatId(id), queryLogByChatId(id)]), {
      pending:'query pending',
      success: 'query success',
      error: {
        render:(e) => {
          return `${e.data}`
        }
      }
    }).then(([history, logs]) => {
      setChatHistory(history)
      setChatLog(logs)
    }).finally(() => {
      setFirstLoad(true)
    })
  }, [id])
  useEffect(() => {

    if (window.location.hash) {
      const anchor = document.getElementById(window.location.hash.substring(1))
      if (anchor) {
        anchor.scrollIntoView({ block:'center' })
      }
    } else {
      window.scrollTo(0, document.body.scrollHeight)
    }

  }, [chatHistory, chatLog])
  if (!firstLoad) {
    return <span className="loading loading-dots loading-xl"></span>
  }
  let i = 0
  let j = 0
  const arr = []
  while (i < chatHistory.length || j < chatLog.length) {
    let type: 'chatHistory' | 'chatLog' = 'chatHistory'
    if (j >= chatLog.length) {
      type = 'chatHistory'
    } else if (i >= chatHistory.length) {
      type = 'chatLog'
    } else if (dayjs(chatHistory[i].created_at).isBefore(dayjs(chatLog[j].timestamp))) {
      type = 'chatHistory'
    } else {
      type = 'chatLog'
    }
    if (type == 'chatHistory') {
      const detail = chatHistory[i]
      arr.push(
        <li
          key={detail.id}
        >
          <hr />
          <div className='timeline-start timeline-box w-full border-0 shadow-none'>
            <div
              className={`chat ${detail.role == 'user' ? 'chat-start' : 'chat-end'}`}
            >
              <div className="chat-header items-center">
                {detail.round_id &&
                <a href={`./${detail.chat_id}#${detail.round_id}`} id={detail.round_id ?? undefined}>#</a>
                }
                <div>{dayjs(detail.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
                {detail.round_id && <Link target='_blank' href={`https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/${langsmithProjectId}?columnVisibilityModel=%7B%22feedback_stats%22%3Atrue%2C%22reference_example%22%3Afalse%7D&timeModel=%7B%22duration%22%3A%227d%22%7D&searchModel=%7B%22filter%22%3A%22and%28eq%28run_type%2C+%5C%22llm%5C%22%29%2C+and%28eq%28metadata_key%2C+%5C%22round_id%5C%22%29%2C+eq%28metadata_value%2C+%5C%22${detail.round_id}%5C%22%29%29%29%22%7D`} className='btn btn-xs btn-success btn-soft'>lang</Link>}
                {detail.chat_state?.state && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.state).map(([k, v], index) => {
                      return <div key={index}>{k}:{v == true ? 'true' : 'false'}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">state</button>
                </div>}
                {detail.chat_state?.userSlots && <div className="tooltip tooltip-left">
                  <div className='tooltip-content'>
                    {Object.entries(detail.chat_state.userSlots).map(([k, v], index) => {
                      return <div key={index}>{k}:{JSON.stringify(v)}</div>
                    })}
                  </div>
                  <button className="btn btn-xs btn-info btn-soft">slots</button>
                </div>}
              </div>
              <div className="chat-bubble max-w-7/12 whitespace-pre-line text-base">
                {detail.content}
              </div>
            </div>
          </div>
          <hr />
        </li>
      )
      i++
    } else {
      const detail = chatLog[j]
      arr.push(
        <div
          key={`log${detail.id}`}
          className={'chat chat-start'}
        >
          <div className="chat-header">{dayjs(detail.timestamp).format('YYYY-MM-DD HH:mm:ss')}</div>
          <div className="chat-bubble whitespace-pre-line">
            {detail.msg}
          </div>
        </div>
      )
      j++
    }
  }
  const minimized = []
  let stack = []
  for (const talk of arr) {
    if (talk.key?.includes('log')) {
      stack.push(talk)
    } else {
      if (stack.length > 0) {
        const group = <Minimize arr={[...stack]} key={stack[0].key}/>
        minimized.push(group)
        stack = []
      }
      minimized.push(talk)
    }
  }

  return (
    <div className='flex p-2 gap-2'>
      <ul className="timeline timeline-vertical">
        {...minimized}
      </ul>
      <button
        className="btn btn-neutral btn-circle fixed right-20 bottom-30"
        onClick={() => {
          window.scrollTo({ top: 0, behavior: 'smooth' })
        }}
      >
        <RiRocket2Fill size={24}/>
      </button>
    </div>)

}

function Minimize({ arr }:{arr:ReactNode[]}) {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  return <li>
    <hr />
    <div className='timeline-end timeline-box border-0 shadow-none'>
      <button className='btn btn-soft btn-accent h-4 ' onClick={() => {
        setIsOpen((open) => !open)
      }}>
        <motion.div animate={{ rotate:isOpen ? 180 : 0 }}>
          <IoMdArrowDropdown/>
        </motion.div>
      </button>
      <motion.div animate={{ height:isOpen ? 'auto' : 0 }} initial={{ height:0 }} className='overflow-hidden'>
      {...arr}
      </motion.div>

    </div>
    <hr />
  </li>
}