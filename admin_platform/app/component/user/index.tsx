'use client'
import { AccountData } from '@/app/type/account'
import { UserData } from '@/app/type/user'
import Link from 'next/link'
import { useState, useEffect, Dispatch, SetStateAction } from 'react'
import { toast } from 'react-toastify'
enum ToolType {
  queryWithoutAi = 'query_without_ai',
  queryWithoutPhone = 'query_without_phone'
}

export function User({
  name_or_phone,
  course_no,
  tool,
  queryChats,
  queryChatsWithoutAi,
  queryChatsWithoutPhone,
  queryDefaultChats,
  queryAccounts,
  changeIsHumanInvolved,
  changeIsStopGroupPush
}: {
  name_or_phone:string,
  course_no:string,
  tool?:string,
  queryChats:(nameOrPhone:string, courseNo?:number)=>Promise<UserData[]>,
  queryChatsWithoutAi:(courseNo?:number)=> Promise<UserData[]>,
  queryChatsWithoutPhone:(courseNo?:number)=> Promise<UserData[]>,
  queryDefaultChats:() => Promise<UserData[]>,
  queryAccounts:() => Promise<AccountData[]>,
  changeIsHumanInvolved:(chatId:string, isHumanInvolved:boolean) => Promise<void>,
  changeIsStopGroupPush:(chatId:string, isStopGroupPush:boolean) => Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [nameOrPhone, setNameOrPhone] = useState<string>(name_or_phone ?? '')
  const courseNo = Number(course_no) ? Number(course_no) : undefined
  const [course, setCourse] = useState<number|undefined>(courseNo)
  const [chats, setChats] = useState<UserData[]>([])
  const [alreadyQuery, setAlreadyQuery] = useState<boolean>(false)
  useEffect(() => {
    if (name_or_phone) {
      query(name_or_phone, course)
    } else if (tool == ToolType.queryWithoutAi) {
      queryWithoutAi(course)
    } else if (tool == ToolType.queryWithoutPhone) {
      queryWithoutPhone(course)
    } else {
      toast.promise(getDefaultUsers(), {
        pending: 'query pending',
        success: 'query success',
        error: 'query error'
      })
    }
  }, [name_or_phone, course_no])
  const queryWithoutAi = (courseNo?:number) => {
    toast.promise(queryChatsWithoutAi(courseNo), {
      pending: 'query pending',
      success: 'query success',
      error: 'query error'
    }).then((results) => {
      setChats(results)
      setAlreadyQuery(true)
      let path = '?tool=query_without_ai'
      if (courseNo) {
        path += `&course_no=${courseNo}`
      }
      history.pushState(null, '', path)
    }).finally(() => {
      setLoading(false)
    })
  }
  const queryWithoutPhone = (courseNo?:number) => {
    toast.promise(queryChatsWithoutPhone(courseNo), {
      pending: 'query pending',
      success: 'query success',
      error: 'query error'
    }).then((results) => {
      setChats(results)
      setAlreadyQuery(true)
      let path = '?tool=query_without_phone'
      if (courseNo) {
        path += `&course_no=${courseNo}`
      }
      history.pushState(null, '', path)
    }).finally(() => {
      setLoading(false)
    })
  }
  const query = (nameOrPhone:string, courseNo?:number) => {
    toast.promise(queryChats(nameOrPhone, courseNo), {
      pending: 'query pending',
      success: 'query success',
      error: 'query error'
    }).then((results) => {
      setChats(results)
      setAlreadyQuery(true)
      let path = `?name_or_phone=${nameOrPhone}`
      if (courseNo) {
        path += `&course_no=${courseNo}`
      }
      history.pushState(null, '', path)
    }).finally(() => {
      setLoading(false)
    })
  }
  const getDefaultUsers = async () => {
    const results = await queryDefaultChats()
    setChats(results)
    setAlreadyQuery(true)
  }
  return <div className="m-2">
    <div className="text-4xl font-bold">总通讯录</div>
    <form onSubmit={(e) => {
      e.preventDefault()
      setLoading(true)
      query(nameOrPhone, course)
    }}>
      <fieldset className="fieldset">
        <legend className="fieldset-legend">手机号或者昵称</legend>
        <div className='flex gap-2'>
          <input type="text" required className="input focus-within:outline-0" placeholder="Type here" value={nameOrPhone} disabled={loading} onChange={(e) => {
            setNameOrPhone(e.currentTarget.value)
          }}/>
          <button type="submit" className="btn disabled:btn-disabled" disabled={loading}>搜索</button>
        </div>
        <legend className="fieldset-legend">期数</legend>
        <input type="text" className="input focus-within:outline-0" placeholder="course_no" value={course ?? ''} disabled={loading} onChange={(e) => {
          setCourse(Number(e.currentTarget.value) ? Number(e.currentTarget.value) : undefined)
        }}/>
      </fieldset>
    </form>
    <div>
      <h2 className='text-lg font-semibold'>Tools</h2>
      <div className='flex gap-2'>
        <button className='btn btn-info btn-soft' onClick={() => {
          queryWithoutAi(course)
        }}>查询关闭ai人群</button>
        <button className='btn btn-accent btn-soft' onClick={() => {
          queryWithoutPhone(course)
        }}>查询没有手机人群</button>
      </div>
    </div>
    <div className='divider'/>
    {alreadyQuery && <UsersList
      changeIsHumanInvolved={changeIsHumanInvolved}
      changeIsStopGroupPush={changeIsStopGroupPush}
      queryAccounts={queryAccounts}
      userList={chats}
      setChats={setChats}
    />}
  </div>
}

function UsersList({
  userList,
  setChats,
  queryAccounts,
  changeIsHumanInvolved,
  changeIsStopGroupPush,
}:{
  userList:UserData[],
  setChats:Dispatch<SetStateAction<UserData[]>>
  queryAccounts:() => Promise<AccountData[]>
  changeIsHumanInvolved:(chatId:string, isHumanInvolved:boolean) => Promise<void>,
  changeIsStopGroupPush:(chatId:string, isStopGroupPush:boolean) => Promise<void>
}) {
  const [accountInfo, setAccountInfo] = useState<AccountData[]>([])
  useEffect(() => {
    queryAccounts().then((res) => setAccountInfo(res))
  }, [])
  if (userList.length == 0) {
    return <div className='text-gray-500'>暂未查询到相关用户</div>
  }
  return <ul className='list'>
    {userList.map((chat) => {
      return <ChatInfo key={chat.id}
        changeIsHumanInvolved={changeIsHumanInvolved}
        changeIsStopGroupPush={changeIsStopGroupPush}
        accounts={accountInfo} chat={chat} setChat={(chat) => {
          const newChatList = [...userList]
          for (let i = 0; i < newChatList.length; i++) {
            if (newChatList[i].id == chat.id) {
              newChatList[i] = chat
            }
          }
          setChats(newChatList)
        }}/>
    })}
  </ul>
}

function ChatInfo({
  chat,
  setChat,
  accounts,
  changeIsHumanInvolved,
  changeIsStopGroupPush
}:
{
  chat:UserData,
  setChat:(chat:UserData) => void,
  accounts:AccountData[],
  changeIsHumanInvolved:(chatId:string, isHumanInvolved:boolean) => Promise<void>,
  changeIsStopGroupPush:(chatId:string, isStopGroupPush:boolean) => Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  let assistant = '未知'
  const filteredTeacher = accounts.filter((item) => item.wechatId == chat.wx_id)
  if (filteredTeacher.length > 0) {
    assistant = filteredTeacher[0].accountName
  }
  return <li key={chat.id} className='list-row items-center'>
    <div className='flex flex-col gap-2 w-96'>
      <div className='font-bold text-2xl'>{chat.contact.wx_name}</div>
      <div className="badge badge-soft badge-primary">id: {chat.id}</div>
      <div className="badge badge-soft badge-accent">courseNo: {chat.course_no}</div>
      <div className="badge badge-soft badge-info">phone: {chat.phone}</div>
      <div className="badge badge-soft badge-secondary">assistant: {assistant}</div>
    </div>
    <div className='w-30 flex flex-col gap-2'>
      <div className='flex gap-4 justify-between'>
        <label className='label '>AI 接入</label>
        <input type="checkbox" disabled={loading} className="toggle toggle-success" checked={chat.is_human_involved === null ? true : !chat.is_human_involved} onChange={(e) => {
          setLoading(true)
          const value = e.currentTarget.checked
          toast.promise(changeIsHumanInvolved(chat.id, !value), {
            pending:'change pending',
            success:'change success',
            error: 'change error'
          }).then(() => {
            chat.is_human_involved = !value
            setChat(chat)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
      <div className='flex gap-4 justify-between'>
        <label className='label '>sop</label>
        <input type="checkbox" className="toggle toggle-success" disabled={loading} checked={chat.is_stop_group_push === null ? true : !chat.is_stop_group_push} onChange={(e) => {
          setLoading(true)
          const value = e.currentTarget.checked
          toast.promise(changeIsStopGroupPush(chat.id, !value), {
            pending:'change pending',
            success:'change success',
            error: 'change error'
          }).then(() => {
            chat.is_stop_group_push = !value
            setChat(chat)
          }).finally(() => {
            setLoading(false)
          })
        }}/>
      </div>
    </div>
    <div className='flex flex-col gap-2'>
      <Link className='btn' href={`./user/edit/${chat.id}`}>配置</Link>
      <Link className='btn' href={`./user/chat/${chat.id}`}>聊天记录</Link>
      <Link className='btn' href={`./user/sop/${chat.id}`}>sop</Link>
    </div>
  </li>

}