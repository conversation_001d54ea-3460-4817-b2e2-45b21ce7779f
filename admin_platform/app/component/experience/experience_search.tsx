'use client'

import { ExperienceRecall } from '@/app/type/experience'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function ExperienceSearch({
  searchExperience
}:{
  searchExperience(job:string, courseStatus:string, courseFeeling:string, pain:string, goal:string, userMessage:string): Promise<ExperienceRecall[]>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [exp, setExp] = useState<ExperienceRecall[]>([])
  return <div>
    <form action={(form) => {
      const job = form.get('job') as string
      const courseStatus = form.get('course_status') as string
      const courseFeeling = form.get('course_feeling') as string
      const pain = form.get('pain') as string
      const goal = form.get('goal') as string
      const userMessage = form.get('user_message') as string
      setLoading(true)
      toast.promise(searchExperience(job, courseStatus, courseFeeling, pain, goal, userMessage), {
        pending:'search pending',
        success:'search success',
        error: 'search error'
      }).then((res) => {
        setExp(res)
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">search</legend>

        <label className="label">job</label>
        <select defaultValue="Pick a job" className="select" name='job' required>
          <option disabled={true}>Pick a job</option>
          <option>退休精进者</option>
          <option>家庭管理者</option>
          <option>职场奋斗者</option>
          <option>未知</option>
        </select>

        <label className="label">课程完成情况</label>
        <select defaultValue="Pick one" className="select" name='course_status' required>
          <option disabled={true}>Pick one</option>
          <option>未完成任何课程</option>
          <option>完成了小部分课程</option>
          <option>完成了大部分课程</option>
        </select>
        <label className="label">课程完成感受</label>
        <select defaultValue="Pick one" className="select" name='course_feeling' required>
          <option disabled={true}>Pick one</option>
          <option>感受很好</option>
          <option>感觉一般</option>
          <option>感受很差</option>
          <option>未知</option>
        </select>

        <label className="label">痛点</label>
        <input type="text" placeholder="Type here" className="input focus-within:outline-0" name='pain' required/>
        <label className="label">目标</label>
        <input type="text" placeholder="Type here" className="input focus-within:outline-0" name='goal' required/>
        <label className="label">用户消息</label>
        <input type="text" placeholder="Type here" className="input focus-within:outline-0" name='user_message' required/>

        <button className="btn btn-neutral mt-4 disabled:btn-disabled" disabled={loading}>search</button>
      </fieldset>
    </form>
    <ExperienceRecallShow experienceRecall={exp}/>
  </div>
}

function ExperienceRecallShow({ experienceRecall }:{experienceRecall:ExperienceRecall[]}) {
  return <table className="table-sm table">
    <thead>
      <tr>
        <th></th>
        <th>用户消息</th>
        <th>工作</th>
        <th>痛点</th>
        <th>目标</th>
        <th>课程完成状态</th>
        <th>课程感受</th>
        <th>策略</th>
        <th>pain_score</th>
        <th>goal_score</th>
        <th>user_message_score</th>
      </tr>
    </thead>
    <tbody>
      {experienceRecall.map((item, index) => {
        return (
          <tr key={index}>
            <td>{index + 1}</td>
            <td>{item.user_message}</td>
            <td>{item.job}</td>
            <td>{item.pain}</td>
            <td>{item.goal}</td>
            <td>{item.course_status}</td>
            <td>{item.course_feeling}</td>
            <td>{item.strategy}</td>
            <td>{item.pain_score}</td>
            <td>{item.goal_score}</td>
            <td>{item.message_score}</td>
          </tr>
        )
      })}
    </tbody>
  </table>
}