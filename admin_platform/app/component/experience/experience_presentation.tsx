import { Experience } from '@/app/type/experience'
import { ExperienceList } from './experience_list'
import { insertExperience } from '@/app/api/experience'

export async function ExperiencePresentation({
  page,
  pageSize,
  queryExperience,
  dropExperience,
  updateExperienceStrategy
}:{
  page:number
  pageSize:number
  queryExperience(page:number, pageSize:number): Promise<Experience[]>
  dropExperience(experienceId:string): Promise<void>
  updateExperienceStrategy(experienceId:string, strategy: string): Promise<void>

}) {
  const experiences = await queryExperience(page, pageSize)
  return <div>
    <ExperienceList dropExperience={dropExperience} initialExperiences={experiences} insertExperience={insertExperience} updateExperienceStrategy={updateExperienceStrategy}/>
  </div>
}