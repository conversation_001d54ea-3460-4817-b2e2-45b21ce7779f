'use client'

import { Experience } from '@/app/type/experience'
import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'
import { FaPen } from 'react-icons/fa'
import { RxCross2 } from 'react-icons/rx'
import { TiTick } from 'react-icons/ti'
import { toast } from 'react-toastify'
import { text } from 'stream/consumers'

export function ExperienceList({
  initialExperiences,
  insertExperience,
  dropExperience,
  updateExperienceStrategy
}:{
  initialExperiences: Experience[]
  insertExperience(experienceId: string):Promise<void>
  dropExperience(experienceId:string): Promise<void>
  updateExperienceStrategy(experienceId:string, strategy: string): Promise<void>
}) {
  const [experiences, setExperiences] = useState<Experience[]>(initialExperiences)
  return <div>
    <table className="table-sm table">
      <thead>
        <tr>
          <th></th>
          <th>用户消息</th>
          <th>工作</th>
          <th>痛点</th>
          <th>目标</th>
          <th>课程完成状态</th>
          <th>课程感受</th>
          <th className='text-center'>策略</th>
          <th>modify strategy</th>
          <th>locate</th>
          <th>action</th>
        </tr>
      </thead>
      <tbody>
        {experiences.map((item, index) => {
          return (
            <tr key={item.id}>
              <td>{index + 1}</td>
              <ExperienceDetail dropExperience={dropExperience} experience={item} insertExperience={insertExperience} setExperience={(experience:Experience) => {
                setExperiences([...experiences.map((exp) => (item.id == exp.id ? experience : exp))])
              }}
              updateExperienceStrategy={updateExperienceStrategy}
              />
            </tr>
          )
        })}
      </tbody>
    </table>
  </div>
}

function ExperienceDetail({
  experience,
  insertExperience,
  setExperience,
  dropExperience,
  updateExperienceStrategy
}:{
  experience:Experience
  insertExperience(experienceId: string):Promise<void>
  dropExperience(experienceId:string): Promise<void>
  setExperience:(experience:Experience) => void
  updateExperienceStrategy(experienceId:string, strategy: string): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [modifying, setModifying] = useState<boolean>(false)
  const [modifiedStrategy, setModifyedStrategy] = useState<string>(experience.strategy)
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  useEffect(() => {
    if (textAreaRef.current != null) {
      textAreaRef.current.style.height = 'auto'
      textAreaRef.current.style.height = `${textAreaRef.current!.scrollHeight  }px`
    }
  }, [modifying])
  const insert = () => {
    setLoading(true)
    toast.promise(
      insertExperience(experience.id),
      {
        pending:'insert pending',
        success:'insert success',
        error: 'insert error'
      }
    ).then(() => {
      setExperience({
        ...experience,
        is_handled:true,
        is_add:true
      })
    }).finally(() => {
      setLoading(false)
    })
  }
  const drop = () => {
    setLoading(true)
    toast.promise(dropExperience(experience.id), {
      pending:'drop pending',
      success:'drop success',
      error: 'drop error'
    }).then(() => {
      setExperience({
        ...experience,
        is_handled:true,
        is_add:false
      })
    }).finally(() => {
      setLoading(false)
    })
  }
  const modifyStrategy = () => {
    setLoading(true)
    toast.promise(updateExperienceStrategy(experience.id, modifiedStrategy), {
      pending: 'update pending',
      success: 'update success',
      error: 'update error'
    }).then(() => {
      setExperience({
        ...experience,
        strategy:modifiedStrategy
      })
    }).finally(() => {
      setLoading(false)
    })
  }
  return <>
    <td className='w-[10rem] whitespace-pre-wrap'>{experience.user_message}</td>
    <td>{experience.job}</td>
    <td className='w-[10rem]'>{experience.pain}</td>
    <td className='w-[10rem]'>{experience.goal}</td>
    <td>{experience.course_status}</td>
    <td>{experience.course_feeling}</td>
    <td className='w-[24rem] whitespace-pre-wrap'>
      {modifying ?
        <div>
          <textarea ref={textAreaRef} className='textarea focus-within:outline-0' value={modifiedStrategy} onChange={(e) => {
            setModifyedStrategy(e.currentTarget.value)
            if (textAreaRef.current != null) {
              textAreaRef.current.style.height = 'auto'
              textAreaRef.current.style.height = `${textAreaRef.current!.scrollHeight  }px`
            }
          }} />
        </div> :
        experience.strategy}
    </td>
    <td>
      {modifying ?
        <div>
          <button className='btn btn-soft btn-success btn-circle mr-2 disabled:btn-disabled' onClick={modifyStrategy} disabled={loading}><TiTick/></button>
          <button className='btn btn-soft btn-error btn-circle' onClick={() => {
            setModifying(false)
            setModifyedStrategy(experience.strategy)
          }}><RxCross2/></button>
        </div> :
        <div>
          <button className='btn btn-soft btn-info' onClick={() => {
            setModifying(true)
          }}><FaPen /></button>
        </div>
      }
    </td>
    <td>
      <Link href={`/user/chat/${experience.chat_id}#${experience.round_id}`}><button className='btn btn-soft btn-info'>goto</button></Link>
    </td>
    <td>
      {experience.is_handled ?
        <>
          handled
        </> :
        <>
          <button className='btn btn-soft btn-success btn-circle mr-2 disabled:btn-disabled' onClick={insert} disabled={loading}><TiTick/></button>
          <button className='btn btn-soft btn-error btn-circle disabled:btn-disabled' onClick={drop} disabled={loading}><RxCross2/></button>
        </>
      }
    </td>
  </>

}