'use client'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function NewTopic({ tag, createTopic }:{
  tag:string
  createTopic(tag: string, topic: string): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const router = useRouter()
  return <div className="flex min-h-screen justify-center pt-30">
    <form action={(form) => {
      const topic = (form.get('topic') ?? '') as string
      setLoading(true)
      toast.promise(createTopic(tag, topic), {
        pending:'create pending',
        success: 'create success',
        error: 'create error'
      }).then(() => {
        router.push('.')
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">new tags</legend>

        <label className="label">topic</label>
        <input type="text" className="input" name='topic' required placeholder="topic" disabled={loading} />

        <button className="btn btn-neutral mt-4 disabled:btn-disabled" disabled={loading}>create</button>
      </fieldset>
    </form>
  </div>
}