'use client'
import { AccountData } from '@/app/type/account'
import Link from 'next/link'
import { FaPlus } from 'react-icons/fa6'
import { TagShow } from './tagShow'
import Tools from './tools'
import { SopTag } from '@/app/type/sop_tag'

export function SopTags({
  tags,
  accounts,
  tagLinkPrefix,
  validateSops,
  updateMq,
  updateRedisSop,
  changeTagEnable,
  deleteTag,
  updateTagEnableAccount
}:{
  tags:SopTag[]
  accounts: AccountData[]
  validateSops(): Promise<string>
  updateMq(): Promise<void>
  updateRedisSop(): Promise<void>
  tagLinkPrefix:string
  changeTagEnable(tag_id: string, enable: boolean): Promise<void>,
  deleteTag(id: string): Promise<void>
  updateTagEnableAccount(tag_id: string, enableAccounts: string[]): Promise<void>
}) {
  return (
    <div className='p-4'>
      <div className='flex justify-between mb-2'>
        <Tools updateMq={updateMq} updateRedisSop={updateRedisSop} validateSops={validateSops}/>
        <Link href="./sop/new_tag"><button className='btn btn-square btn-primary'><FaPlus/></button></Link>
      </div>
      <div className="grid grid-cols-3 gap-8">
        {tags.map((item) => {
          return <TagShow
            key={item.id}
            tag={item}
            tagLinkPrefix={tagLinkPrefix}
            accounts={accounts}
            changeTagEnable={changeTagEnable}
            deleteTag={deleteTag}
            updateTagEnableAccount={updateTagEnableAccount}
          />
        })}
      </div>
    </div>
  )
}