'use client'
import { Sop } from '@/app/type/sop'
import { useState, useEffect } from 'react'
import { SopEdit } from './sopEdit'
import { Situation, TimeAnchorType } from '../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'

export function NewSop({
  tag,
  topic,
  getConditionJudgeKeys,
  getCustomKeys,
  getLinkSourceVariableTagKeys,
  getVariableMapKeys,
  saveSop
}: {
  tag:string
  topic:string
  getConditionJudgeKeys(): Promise<string[]>
  getCustomKeys(): Promise<string[]>
  getVariableMapKeys(): Promise<string[]>
  getLinkSourceVariableTagKeys(): Promise<string[]>
  saveSop(sop: Omit<Sop, 'id'>): Promise<void>
}) {
  const [situations, setSituations] = useState<Situation[]>([
    {
      conditions: [],
      action: [],
    },
  ])
  const [conditionJudgeKeys, setConditionJudgeKeys] = useState<string[]>([])
  const [customKeys, setCustomKeys] = useState<string[]>([])
  const [variableKeys, setVariableKeys] = useState<string[]>([])
  const [linkSourceVariableTagKeys, setLinkSourceVariableTagKeys] = useState<
    string[]
  >([])
  const [title, setTitle] = useState<string>('')
  const [timeAnchor, setTimeAnchor] = useState<TimeAnchorType>('course')
  const [week, setWeek] = useState<number>(0)
  const [day, setDay] = useState<number>(1)
  const [time, setTime] = useState<string>('')
  useEffect(() => {
    getConditionJudgeKeys().then((keys) => {
      setConditionJudgeKeys(keys)
    })
    getCustomKeys().then((keys) => {
      setCustomKeys(keys)
    })
    getVariableMapKeys().then((keys) => {
      setVariableKeys(keys)
    })
    getLinkSourceVariableTagKeys().then((keys) => {
      setLinkSourceVariableTagKeys(keys)
    })
  }, [])
  return (
    <div className="m-2">
      <SopEdit
        situations={situations}
        setSituations={setSituations}
        conditionJudgeKeys={conditionJudgeKeys}
        customKeys={customKeys}
        variableKeys={variableKeys}
        linkSourceVariableTagKeys={linkSourceVariableTagKeys}
        title={title}
        setTitle={setTitle}
        timeAnchor={timeAnchor}
        setTimeAnchor={setTimeAnchor}
        week={week}
        setWeek={setWeek}
        day={day}
        setDay={setDay}
        time={time}
        setTime={setTime}
        tag={tag}
        topic={topic}
        saveSop={saveSop}
        hint={{
          success: 'create success',
          pending: 'create pending',
          error: 'create error',
        }}
        reset={true}
      />
    </div>
  )

}