'use client'

import { Sop } from '@/app/type/sop'
import { useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'
import  { SopShow, testSopChatIdKey } from './sopShow'
import Link from 'next/link'
import { FaPlus } from 'react-icons/fa6'

export function TagPagination({
  page,
  pageSize,
  tag,
  topic,
  deleteSop,
  querySop,
  querySopCount,
  testAction,
  testAllSop,
  updateSop,
  importSop
}:{
  page: number,
  pageSize:number
  tag:string
  topic:string
  deleteSop(id: string): Promise<void>
  querySop({ page, pageSize, tag }: {
    page: number;
    pageSize: number;
    tag: string;
    topic: string;
  }):Promise<Sop[]>
  querySopCount({ tag }: {
      tag: string
      topic: string
  }): Promise<number>
  testAction(chatId: string, id: string): Promise<void>
  testAllSop(tag: string, chatId: string, topic:string): Promise<void>
  updateSop(sop_id: string, sop: Partial<Sop>): Promise<void>
  importSop(tag:string, topic:string, sop:Omit<Sop, 'id'>[]):Promise<void>
}) {
  const [sops, setSops] = useState<Sop[]>([])
  const [sopCount, setSopCount] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(false)
  const [selectedSop, setSelectedSop] = useState<string[]>([])
  useEffect(() => {
    toast
      .promise(
        Promise.all([
          querySop({ page, pageSize, tag, topic }),
          querySopCount({ tag, topic }),
        ]),
        {
          pending: 'query pending',
          error: 'query error',
          success: 'query success',
        }
      )
      .then(([resultSops, resultSopCount]) => {
        setSops(resultSops as unknown as Sop[])
        setSopCount(resultSopCount)
      })
  }, [page, pageSize, tag])
  return (
    <div>
      <div className="m-2 flex justify-between gap-2">
        <div className='flex gap-2'>
          <button className='btn btn-neutral disabled:btn-disabled' disabled={loading} onClick={() => {
            const chatId = localStorage.getItem(testSopChatIdKey)
            if (!chatId) {
              toast.error('chat id is empty')
              return
            }
            setLoading(true)
            toast.promise(testAllSop(tag, chatId, topic), {
              pending:'test pending',
              success:'test success',
              error:'test error'
            }).finally(() => {
              setLoading(false)
            })
          }}>测试</button>
          {selectedSop.length > 0 &&
          <button className='btn btn-info btn-soft' onClick={() => {
            const exportSop = sops.filter((item) => selectedSop.includes(item.id))
            const exportStr = JSON.stringify(exportSop)
            toast.promise(navigator.clipboard.writeText(exportStr), {
              pending:'export pending',
              success:'success copy into your clipboard',
              error: 'export error'
            })
          }}>export</button>}
        </div>
        <div className='flex gap-2'>
          <ImportSopButtonAndDialog tag={tag} topic={topic} importSop={importSop}/>
          <Link href={`./${topic}/new`}>
            <button className="btn btn-square btn-neutral">
              <FaPlus />
            </button>
          </Link>
        </div>
      </div>
      <div className="p-8">
        <div className="mr-8 text-right text-sm text-gray-400">
          总数:{sopCount}
        </div>
        <SopShow
          sops={sops}
          setSops={setSops}
          deleteSop={deleteSop}
          testAction={testAction}
          updateSop={updateSop}
          selectedSop={selectedSop}
          setSelectedSop={setSelectedSop}
        />
        <div className="join mx-auto mt-10 grid w-96 grid-cols-2">
          <Link
            href={`./${topic}?page=${page - 1}`}
            className={
              `join-item btn btn-outline${  page == 1 && ' btn-disabled'}`
            }
          >
            Previous page
          </Link>
          <Link
            href={`./${topic}?page=${page + 1}`}
            className={
              `join-item btn btn-outline${
                sopCount <= page * pageSize && ' btn-disabled'}`
            }
          >
            Next
          </Link>
        </div>
      </div>
    </div>
  )
}
function ImportSopButtonAndDialog({
  importSop,
  tag,
  topic
}:{
  tag: string
  topic: string
  importSop(tag:string, topic:string, sop:Omit<Sop, 'id'>[]): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const importSopDialogRef = useRef<HTMLDialogElement>(null)
  const [sopStr, setSopStr] = useState<string>('')
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  return <>
    <dialog ref={importSopDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">import sop?</h3>
        <fieldset className="fieldset">
          <legend className="fieldset-legend">imoprt sop</legend>
          <textarea className='textarea focus-within:outline-0 w-auto min-h-96' value={sopStr} onChange={(e) => {
            const el = textareaRef.current
            if (el) {
              el.style.height = 'auto'
              el.style.height = `${el.scrollHeight}px`
            }
            setSopStr(e.currentTarget.value)
          }}
          ref={textareaRef}
          />
        </fieldset>
        <div className="modal-action">
          <button className="btn btn-error btn-soft disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            const sops = JSON.parse(sopStr) as Sop[]
            toast.promise(importSop(tag, topic, sops), {
              pending:'import pending',
              success:'import success',
              error: 'import error'
            }).then(() => {
              window.location.reload()
            }).finally(() => {
              setLoading(false)
            })
          }}>import</button>
          <button className="btn" onClick={() => {
            importSopDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <button className='btn btn-soft btn-accent' onClick={() => {
      importSopDialogRef.current?.showModal()
    }}>import</button>
  </>
}
