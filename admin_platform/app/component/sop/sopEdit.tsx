'use client'
import { Dispatch, SetStateAction, useRef, useEffect } from 'react'
import { FaDeleteLeft, FaPlus } from 'react-icons/fa6'
import { MdDeleteForever } from 'react-icons/md'
import { toast } from 'react-toastify'
import { ActionType, ContentCustom, ContentDynamicPrompt, ContentFile, ContentImage, ContentLink, ContentTextPlain, ContentVideo, ContentVideoChannel, ContentVoice, LinkSourceType, Situation, Sop, TextFixed, TextType, TextVariable, TimeAnchorType } from '../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'

export function SopEdit({
  situations,
  setSituations,
  conditionJudgeKeys,
  customKeys,
  variableKeys,
  linkSourceVariableTagKeys,
  title,
  setTitle,
  timeAnchor,
  setTimeAnchor,
  week,
  setWeek,
  day,
  setDay,
  time,
  setTime,
  tag,
  topic,
  saveSop,
  hint,
  reset,
}: {
  situations: Situation[];
  setSituations: Dispatch<SetStateAction<Situation[]>>;
  conditionJudgeKeys: string[];
  customKeys: string[];
  variableKeys: string[];
  linkSourceVariableTagKeys: string[];
  title: string;
  setTitle: Dispatch<SetStateAction<string>>;
  timeAnchor: TimeAnchorType
  setTimeAnchor: Dispatch<SetStateAction<TimeAnchorType>>
  week: number
  setWeek: Dispatch<SetStateAction<number>>;
  day: number;
  setDay: Dispatch<SetStateAction<number>>;
  time: string;
  setTime: Dispatch<SetStateAction<string>>;
  tag: string
  topic:string
  saveSop: (sop: Omit<Sop, 'id'>) => Promise<void>;
  hint: { success: string; error: string; pending: string };
  reset: boolean;
}) {
  return (
    <form
      action={() => {
        toast
          .promise(saveSop({ day, title, time_anchor:timeAnchor, week, time, situations, tag, enable:false, topic }), hint)
          .then(() => {
            if (reset) {
              setSituations([
                {
                  conditions: [],
                  action: [],
                },
              ])
            }
          })
      }}
    >
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">基础信息</legend>

        <label className="fieldset-label">标题</label>
        <input
          type="text"
          className="input focus-within:outline-0"
          required
          name="title"
          value={title}
          onChange={(e) => {
            setTitle(e.currentTarget.value)
          }}
        />
        <label className="fieldset-label">时间锚点</label>
        <select
          value={timeAnchor}
          className="select focus-within:outline-0"
          onChange={(e) => {
            const value = e.currentTarget.value as 'course' | 'register'
            setTimeAnchor(value)
            setWeek(0)
            if (value == 'course') {
              setDay(1)
            } else {
              setDay(0)
            }
          }}
        >
          <option disabled={true}>Please select the time anchor</option>
          <option value={'course'}>上课时间</option>
          <option value={'register'}>注册时间</option>
        </select>
        {timeAnchor == 'course' &&
        <>
          <label className="fieldset-label">第几周</label>
          <select
            value={week}
            className="select focus-within:outline-0"
            name="week"
            onChange={(e) => {
              setWeek(Number(e.currentTarget.value))
            }}
          >
            <option disabled={true}>Please select the week</option>
            <option value={-1}>课前周</option>
            <option value={0}>上课周</option>
            <option value={1}>课后第一周</option>
            <option value={2}>课后第二周</option>
            <option value={3}>课后第三周</option>
            <option value={4}>课后第四周</option>
          </select>
          <label className="fieldset-label">第几天</label>
          <select
            className="select focus-within:outline-0"
            name="day"
            value={day}
            onChange={(e) => {
              setDay(Number(e.currentTarget.value))
            }}
          >
            <option disabled={true}>Please select the day</option>
            <option value={1}>周一</option>
            <option value={2}>周二</option>
            <option value={3}>周三</option>
            <option value={4}>周四</option>
            <option value={5}>周五</option>
            <option value={6}>周六</option>
            <option value={7}>周日</option>
          </select>
        </>
        }
        {
          timeAnchor == 'register' && <>
            <label className="fieldset-label">第几天</label>
            <select
              className="select focus-within:outline-0"
              name="day"
              value={day}
              onChange={(e) => {
                setDay(Number(e.currentTarget.value))
              }}
            >
              <option disabled={true}>Please select the day</option>
              <option value={0}>报名当天</option>
              <option value={1}>第一天</option>
              <option value={2}>第二天</option>
              <option value={3}>第三天</option>
              <option value={4}>第四天</option>
              <option value={5}>第五天</option>
              <option value={6}>第六天</option>
              <option value={7}>第七天</option>
              <option value={8}>第八天</option>
              <option value={9}>第九天</option>
              <option value={10}>第十天</option>
              <option value={11}>第十一天</option>
              <option value={12}>第十二天</option>
              <option value={13}>第十三天</option>
              <option value={14}>第十四天</option>
              <option value={15}>第十五天</option>
            </select>
          </>
        }
        <label className="fieldset-label">时间</label>
        <input
          required
          type="time"
          name="time"
          className="input focus-within:outline-0"
          step={1}
          value={time}
          onChange={(e) => {
            setTime(e.currentTarget.value)
          }}
        />
      </fieldset>
      <div className="divider"></div>
      {situations.map((situation, index) => {
        return (
          <div key={index}>
            <div className="text-2xl">情况{index + 1}</div>
            <button
              className="btn btn-neutral"
              onClick={(e) => {
                e.preventDefault()
                setSituations(
                  situations.filter(
                    (_, situationIndex) => situationIndex != index,
                  ),
                )
              }}
            >
              delete
            </button>
            <Segment
              situation={situation}
              setSituation={(newSituation) => {
                situations[index] = newSituation
                setSituations([...situations])
              }}
              conditionJudgeKeys={conditionJudgeKeys}
              customKeys={customKeys}
              variableKeys={variableKeys}
              linkSourceVariableTagKeys={linkSourceVariableTagKeys}
            />
            <div className="divider"></div>
          </div>
        )
      })}
      <div>
        <button
          className="btn btn-neutral"
          onClick={(e) => {
            e.preventDefault()
            setSituations((prev) => [
              ...prev,
              {
                conditions: [],
                action: [],
              },
            ])
          }}
        >
          添加情况
        </button>
      </div>
      <div>
        <button type="submit" className="btn btn-neutral">
          提交
        </button>
      </div>
    </form>
  )
}

function Segment({
  situation,
  setSituation,
  customKeys,
  variableKeys,
  conditionJudgeKeys,
  linkSourceVariableTagKeys,
}: {
  situation: Situation;
  setSituation: (situations: Situation) => void;
  customKeys: string[];
  variableKeys: string[];
  conditionJudgeKeys: string[];
  linkSourceVariableTagKeys: string[];
}) {
  return (
    <div className="">
      <div className="flex items-center gap-2">
        <div>条件：</div>
        <div className="flex flex-wrap items-center gap-2">
          {situation.conditions.map((condition, conditionIndex) => {
            return (
              <div key={conditionIndex} className="flex items-center gap-2">
                {conditionIndex != 0 && <div>&&</div>}
                <div className="join">
                  <select
                    className="select w-16 focus-within:outline-0"
                    value={condition.isOrNotIs ? 'yes' : 'no'}
                    onChange={(e) => {
                      e.preventDefault()
                      const newSituation: Situation = {
                        conditions: [...situation.conditions],
                        action: [...situation.action],
                      }
                      newSituation.conditions[conditionIndex].isOrNotIs =
                        e.currentTarget.value == 'yes'
                      setSituation(newSituation)
                    }}
                  >
                    <option disabled={true}>请选择逻辑正负</option>
                    <option value={'no'}>非</option>
                    <option value={'yes'}>是</option>
                  </select>
                  <select
                    className="select w-20 focus-within:outline-0"
                    value={condition.type}
                    onChange={(e) => {
                      e.preventDefault()
                      if (e.currentTarget.value == condition.type) {
                        return
                      }
                      const newSituation: Situation = {
                        conditions: [...situation.conditions],
                        action: [...situation.action],
                      }
                      newSituation.conditions[conditionIndex].type = e.currentTarget.value as 'dynamic' | 'fixed'
                      if (e.currentTarget.value == 'dynamic') {
                        newSituation.conditions[conditionIndex].condition = ''
                      } else {
                        newSituation.conditions[conditionIndex].condition = conditionJudgeKeys[0] ?? ''
                      }
                      setSituation(newSituation)
                    }}
                  >
                    <option disabled={true}>请选择逻辑正负</option>
                    <option value={'fixed'}>固定</option>
                    <option value={'dynamic'}>动态</option>
                  </select>
                  {condition.type == 'fixed' &&
                  <select
                    className="select focus-within:outline-0"
                    required
                    value={condition.condition}
                    onChange={(e) => {
                      const newSituation: Situation = {
                        conditions: [...situation.conditions],
                        action: [...situation.action],
                      }
                      newSituation.conditions[conditionIndex].condition =
                        e.currentTarget.value
                      setSituation(newSituation)
                    }}
                  >
                    <option disabled={true}>请选择条件</option>
                    {conditionJudgeKeys.map((item, index) => {
                      return <option key={index}>{item}</option>
                    })}
                  </select>}
                  {condition.type == 'dynamic' &&
                  <input
                    className="input focus-within:outline-0"
                    required
                    value={condition.condition}
                    onChange={(e) => {
                      const newSituation: Situation = {
                        conditions: [...situation.conditions],
                        action: [...situation.action],
                      }
                      newSituation.conditions[conditionIndex].condition =
                        e.currentTarget.value
                      setSituation(newSituation)
                    }}
                  />}
                  <div
                    className="btn btn-neutral btn-soft"
                    onClick={() => {
                      const newSituation: Situation = {
                        conditions: [...situation.conditions].filter(
                          (_item, index) => index != conditionIndex,
                        ),
                        action: [...situation.action],
                      }
                      setSituation(newSituation)
                    }}
                  >
                    <FaDeleteLeft />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        <div
          className="btn btn-square btn-neutral"
          onClick={() => {
            const newSituation: Situation = {
              conditions: [
                ...situation.conditions,
                { condition: conditionJudgeKeys[0] ?? '', type:'fixed', isOrNotIs: true },
              ],
              action: [...situation.action],
            }
            setSituation(newSituation)
          }}
        >
          <FaPlus />
        </div>
      </div>
      {situation.action.map((sopAction, index) => {
        let content = <div className="text-error">unknow type error</div>
        if (sopAction.type == ActionType.text) {
          content = (
            <SopText
              contentText={sopAction}
              setContentText={(contentText) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = contentText
                setSituation(newSituation)
              }}
              variableKeys={variableKeys}
            />
          )
        } else if (sopAction.type == ActionType.image) {
          content = (
            <SopImage
              contentImage={sopAction}
              setContentImage={(imageInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = imageInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.video) {
          content = (
            <SopVideo
              contentVideo={sopAction}
              setContentVideo={(videoInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = videoInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.file) {
          content = (
            <SopFile
              contentFile={sopAction}
              setContentFile={(fileInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = fileInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.voice) {
          content = (
            <SopVoice
              contentVoice={sopAction}
              setContentVoice={(voiceInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = voiceInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.custom) {
          content = (
            <SopCustom
              contentCustom={sopAction}
              customKeys={customKeys}
              setContentCustom={(customInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = customInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.link) {
          content = (
            <SopLink
              contentLink={sopAction}
              setContentLink={(linkInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = linkInfo
                setSituation(newSituation)
              }}
              linkSourceVariableTagKeys={linkSourceVariableTagKeys}
            />
          )
        } else if (sopAction.type == ActionType.videoChannel) {
          content = (
            <SopVideoChannel
              contentVideoChannel={sopAction}
              setContentVideoChannel={(videoChannelInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = videoChannelInfo
                setSituation(newSituation)
              }}
            />
          )
        } else if (sopAction.type == ActionType.dynamicPrompt) {
          content = (
            <SopDynamicPrompt
              contentDynamicPrompt={sopAction}
              setContentDynamicPrompt={(dynamicPromptInfo) => {
                const newSituation: Situation = {
                  conditions: [...situation.conditions],
                  action: [...situation.action],
                }
                newSituation.action[index] = dynamicPromptInfo
                setSituation(newSituation)
              }}
            />
          )
        }
        return (
          <div key={index}>
            <div>行动{index + 1}</div>
            <div className="flex items-center">
              <Selector
                index={index}
                situation={situation}
                setSituation={setSituation}
                customKeys={customKeys}
              />
              <DeleteActionButton
                index={index}
                situation={situation}
                setSituation={setSituation}
              />
            </div>
            {content}
          </div>
        )
      })}
      <button
        className="btn btn-neutral btn-square"
        onClick={(e) => {
          e.preventDefault()
          const newSituation: Situation = {
            conditions: [...situation.conditions],
            action: [
              ...situation.action,
              { type: ActionType.text, textList: [], description:'' },
            ],
          }
          setSituation(newSituation)
        }}
      >
        <FaPlus />
      </button>
    </div>
  )
}

function SopText({
  contentText,
  setContentText,
  variableKeys,
}: {
  contentText: ContentTextPlain;
  setContentText: (contentText: ContentTextPlain) => void;
  variableKeys: string[];
}) {
  const divRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    const divElement = divRef.current
    if (!divElement) {
      toast.error('div element is not found')
      return
    }
    for (const text of contentText.textList) {
      if (text.type == TextType.fixed) {
        divElement.innerHTML += text.text
      } else {
        const node = document.createElement('select')
        node.className = 'select focus-within:outline-0'
        node.innerHTML = variableKeys
          .map((key) => {
            if (key == text.tag) {
              return `<option selected>${key}</option>`
            } else {
              return `<option>${key}</option>`
            }
          })
          .join('\n')
        divElement.appendChild(node)
      }
    }
    return () => {
      divElement.innerHTML = ''
    }
  }, [variableKeys])
  const getTextList: () => (TextFixed | TextVariable)[] = () => {
    const divElement = divRef.current
    if (!divElement) {
      toast.error('div element is not found')
      return []
    } else {
      const textList: (TextFixed | TextVariable)[] = []
      for (const child of divElement.childNodes) {
        if (child.nodeName == '#text') {
          if (
            textList.length == 0 ||
            textList[textList.length - 1].type != TextType.fixed
          ) {
            textList.push({
              type: TextType.fixed,
              text: child.nodeValue ?? '',
            })
          } else {
            (textList[textList.length - 1] as TextFixed).text +=
              child.nodeValue ?? ''
          }
        } else if (child.nodeName == 'SELECT') {
          textList.push({
            type: TextType.variable,
            tag: (child as HTMLSelectElement).value,
          })
        } else if (child.nodeName == 'BR') {
          toast.warn('出现br')
          continue
        } else {
          toast.error(`unknow nodeName:${child.nodeName}`)
          continue
        }
      }
      return textList
    }
  }
  return (
    <div>
      <button
        className="btn btn-neutral"
        onClick={(e) => {
          e.preventDefault()
          const rootDiv = divRef.current
          const node = document.createElement('select')
          node.className = 'select focus-within:outline-0'
          node.innerHTML = variableKeys
            .map((key) => {
              return `<option>${key}</option>`
            })
            .join('\n')
          const selection = document.getSelection()
          if (selection) {
            const anchorNode = selection.anchorNode
            if (rootDiv?.contains(anchorNode)) {
              selection.getRangeAt(0).insertNode(node)
            }
          }
          const textList = getTextList()
          setContentText({
            ...contentText,
            textList,
          })
        }}
      >
        添加变量
      </button>
      <div
        ref={divRef}
        contentEditable="plaintext-only"
        className="min-h-96 border focus-within:outline-0"
        onInput={() => {
          const textList = getTextList()
          setContentText({
            ...contentText,
            textList,
          })
        }}
      ></div>
      <label className='label'>description</label>
      <input type="text" required className='input focus-within:outline-0' value={contentText.description}
        onChange={(e) => {
          setContentText({
            ...contentText,
            description:e.currentTarget.value
          })
        }}
      />
    </div>
  )
}
function SopLink({
  contentLink,
  setContentLink,
  linkSourceVariableTagKeys,
}: {
  contentLink: ContentLink;
  linkSourceVariableTagKeys: string[];

  setContentLink: (contentLink: ContentLink) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">link details</legend>

        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentLink.description}
          required
          onChange={(e) => {
            setContentLink({
              ...contentLink,
              description: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">title</label>
        <input
          className="input focus-within:outline-0"
          value={contentLink.title}
          required
          onChange={(e) => {
            setContentLink({
              ...contentLink,
              title: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">summary</label>
        <input
          className="input focus-within:outline-0"
          value={contentLink.summary}
          required
          onChange={(e) => {
            setContentLink({
              ...contentLink,
              summary: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">imageUrl</label>
        <input
          className="input focus-within:outline-0"
          value={contentLink.imageUrl}
          required
          onChange={(e) => {
            setContentLink({
              ...contentLink,
              imageUrl: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">sourceType</label>
        <select
          className="select focus-within:outline-0"
          value={contentLink.source.type}
          required
          onChange={(e) => {
            const value = e.currentTarget.value
            if (value == LinkSourceType.variable) {
              setContentLink({
                ...contentLink,
                source: {
                  type: value as LinkSourceType.variable,
                  tag: linkSourceVariableTagKeys[0] ?? '',
                },
              })
            } else if (value == LinkSourceType.fixed) {
              setContentLink({
                ...contentLink,
                source: { type: LinkSourceType.fixed, url: '' },
              })
            } else {
              toast.error('unknow link type')
            }
          }}
        >
          <option value={LinkSourceType.fixed}>固定链接</option>
          <option value={LinkSourceType.variable}>变量</option>
        </select>
        {contentLink.source.type == LinkSourceType.fixed && (
          <>
            <label className="fieldset-label">链接地址</label>
            <input
              className="input focus-within:outline-0"
              value={contentLink.source.url}
              required
              onChange={(e) => {
                setContentLink({
                  ...contentLink,
                  source: {
                    type: LinkSourceType.fixed,
                    url: e.currentTarget.value,
                  },
                })
              }}
            />
          </>
        )}
        {contentLink.source.type == LinkSourceType.variable && (
          <>
            <label className="fieldset-label">变量标签</label>
            <select
              className="select focus-within:outline-0"
              value={contentLink.source.tag}
              required
              onChange={(e) => {
                setContentLink({
                  ...contentLink,
                  source: {
                    type: LinkSourceType.variable,
                    tag: e.currentTarget.value,
                  },
                })
              }}
            >
              {linkSourceVariableTagKeys.map((tag, index) => {
                return <option key={index}>{tag}</option>
              })}
            </select>
          </>
        )}
      </fieldset>
    </div>
  )
}
function SopVideo({
  contentVideo,
  setContentVideo,
}: {
  contentVideo: ContentVideo;
  setContentVideo: (contentVideo: ContentVideo) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">video details</legend>

        <label className="fieldset-label">url</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideo.url}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVideo({
              ...contentVideo,
              url: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideo.description}
          required
          onChange={(e) => {
            setContentVideo({
              ...contentVideo,
              description: e.currentTarget.value,
            })
          }}
        />
      </fieldset>
    </div>
  )
}
function SopFile({
  contentFile,
  setContentFile,
}: {
  contentFile: ContentFile;
  setContentFile: (contentFile: ContentFile) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">file details</legend>

        <label className="fieldset-label">name</label>
        <input
          className="input focus-within:outline-0"
          value={contentFile.name}
          required
          onChange={(e) => {
            setContentFile({
              ...contentFile,
              name: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">url</label>
        <input
          className="input focus-within:outline-0"
          value={contentFile.url}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentFile({
              ...contentFile,
              url: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentFile.description}
          required
          onChange={(e) => {
            setContentFile({
              ...contentFile,
              description: e.currentTarget.value,
            })
          }}
        />
      </fieldset>
    </div>
  )
}
function SopVoice({
  contentVoice,
  setContentVoice,
}: {
  contentVoice: ContentVoice;
  setContentVoice: (contentVoice: ContentVoice) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">file details</legend>

        <label className="fieldset-label">url</label>
        <input
          className="input focus-within:outline-0"
          value={contentVoice.url}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVoice({
              ...contentVoice,
              url: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">duration</label>
        <input
          className="input focus-within:outline-0"
          type="number"
          step={1}
          value={contentVoice.duration}
          required
          onChange={(e) => {
            setContentVoice({
              ...contentVoice,
              duration: Number(e.currentTarget.value),
            })
          }}
        />
        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentVoice.description}
          required
          onChange={(e) => {
            setContentVoice({
              ...contentVoice,
              description: e.currentTarget.value,
            })
          }}
        />
      </fieldset>
    </div>
  )
}

function SopDynamicPrompt({
  contentDynamicPrompt,
  setContentDynamicPrompt
}: {
  contentDynamicPrompt:ContentDynamicPrompt;
  setContentDynamicPrompt: (contentDynamicPrompt:ContentDynamicPrompt) => void;
}) {
  return <div>
    <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
      <legend className="fieldset-legend">image details</legend>

      <label className="fieldset-label">description</label>
      <input
        className="input focus-within:outline-0"
        value={contentDynamicPrompt.description}
        required
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            description: e.currentTarget.value,
          })
        }}
      />
      <label className="fieldset-label">custom prompt</label>
      <textarea
        className="textarea focus-within:outline-0"
        value={contentDynamicPrompt.customPrompt}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            customPrompt: e.currentTarget.value,
          })
        }}
      />
      <label className="fieldset-label">dynamic prompt</label>
      <textarea
        className="textarea focus-within:outline-0"
        value={contentDynamicPrompt.dynamicPrompt}
        required
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            dynamicPrompt: e.currentTarget.value,
          })
        }}
      />
      <label className="fieldset-label">chat history rounds</label>
      <input
        type='number'
        className="input focus-within:outline-0"
        value={contentDynamicPrompt.chatHistoryRounds}
        required
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            chatHistoryRounds: Number(e.currentTarget.value),
          })
        }}
      />
      {/* 还没效果 */}
      {/* <label className="fieldset-label">include RAG</label>
      <input
        type='checkbox'
        className="toggle focus-within:outline-0"
        checked={contentDynamicPrompt.includeRAG}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            includeRAG: e.currentTarget.checked
          })
        }}
      /> */}
      <label className="fieldset-label">split</label>
      <input
        type='checkbox'
        className="toggle focus-within:outline-0"
        checked={!contentDynamicPrompt.noSplit}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            noSplit: !e.currentTarget.checked
          })
        }}
      />
      <label className="fieldset-label">include memory</label>
      <input
        type='checkbox'
        className="toggle focus-within:outline-0"
        checked={contentDynamicPrompt.includeMemory}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            includeMemory: e.currentTarget.checked
          })
        }}
      />
      <label className="fieldset-label">include user slots</label>
      <input
        type='checkbox'
        className="toggle focus-within:outline-0"
        checked={contentDynamicPrompt.includeUserSlots}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            includeUserSlots: e.currentTarget.checked
          })
        }}
      />
      <label className="fieldset-label">include time info</label>
      <input
        type='checkbox'
        className="toggle focus-within:outline-0"
        checked={contentDynamicPrompt.includeTimeInfo}
        onChange={(e) => {
          setContentDynamicPrompt({
            ...contentDynamicPrompt,
            includeTimeInfo: e.currentTarget.checked
          })
        }}
      />
    </fieldset>
  </div>
}

function SopImage({
  contentImage,
  setContentImage,
}: {
  contentImage: ContentImage;
  setContentImage: (contentImage: ContentImage) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">image details</legend>

        <label className="fieldset-label">url</label>
        <input
          className="input focus-within:outline-0"
          value={contentImage.url}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentImage({
              ...contentImage,
              url: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentImage.description}
          required
          onChange={(e) => {
            setContentImage({
              ...contentImage,
              description: e.currentTarget.value,
            })
          }}
        />
      </fieldset>
    </div>
  )
}
function SopCustom({
  contentCustom,
  setContentCustom,
  customKeys,
}: {
  contentCustom: ContentCustom;
  customKeys: string[];
  setContentCustom: (contentCustom: ContentCustom) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <label className="fieldset-label">tag</label>
        <select
          required
          className="select focus-within:outline-0"
          value={contentCustom.tag}
          onChange={(e) => {
            setContentCustom({
              ...contentCustom,
              tag: e.currentTarget.value,
            })
          }}
        >
          {customKeys.map((item, index) => (
            <option key={index}>{item}</option>
          ))}
        </select>
      </fieldset>
    </div>
  )
}
function SopVideoChannel({
  contentVideoChannel,
  setContentVideoChannel,
}: {
  contentVideoChannel: ContentVideoChannel;
  setContentVideoChannel: (contentVideoChannel: ContentVideoChannel) => void;
}) {
  return (
    <div>
      <fieldset className="fieldset bg-base-200 border-base-300 rounded-box w-xs border p-4">
        <legend className="fieldset-legend">video channel details</legend>

        <label className="fieldset-label">description</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.description}
          required
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              description: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">avatarUrl</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.avatarUrl}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              avatarUrl: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">coverUrl</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.coverUrl}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              coverUrl: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">contentDescription</label>
        <input
          className="input focus-within:outline-0"
          required
          value={contentVideoChannel.contentDescription}
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              contentDescription: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">nickname</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.nickname}
          required
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              nickname: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">thumbUrl</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.thumbUrl}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              thumbUrl: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">url</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.url}
          required
          pattern="^(https?://)?([a-zA-Z0-9]([a-zA-Z0-9\-].*[a-zA-Z0-9])?\.)+[a-zA-Z].*$"
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              url: e.currentTarget.value,
            })
          }}
        />
        <label className="fieldset-label">extras</label>
        <input
          className="input focus-within:outline-0"
          value={contentVideoChannel.extras}
          required
          onChange={(e) => {
            setContentVideoChannel({
              ...contentVideoChannel,
              extras: e.currentTarget.value,
            })
          }}
        />
      </fieldset>
    </div>
  )
}

function Selector({
  situation,
  setSituation,
  index,
  customKeys,
}: {
  situation: Situation;
  setSituation: (sop: Situation) => void;
  index: number;
  customKeys: string[];
}) {
  return (
    <select
      className="select focus-within:outline-0"
      value={situation.action[index].type}
      onChange={(e) => {
        const newSituation: Situation = {
          conditions: [...situation.conditions],
          action: [...situation.action],
        }
        const type = e.currentTarget.value
        if (type == ActionType.text) {
          newSituation.action[index] = {
            type,
            textList: [],
            description:''
          }
        } else if (type == ActionType.custom) {
          newSituation.action[index] = {
            type,
            tag: customKeys[0] ?? '',
          }
        } else if (type == ActionType.file) {
          newSituation.action[index] = {
            type,
            url: '',
            description: '',
            name: '',
          }
        } else if (type == ActionType.video) {
          newSituation.action[index] = {
            type,
            url: '',
            description: '',
          }
        } else if (type == ActionType.voice) {
          newSituation.action[index] = {
            type,
            url: '',
            duration: 0,
            description: '',
          }
        } else if (type == ActionType.image) {
          newSituation.action[index] = {
            type,
            url: '',
            description: '',
          }
        } else if (type == ActionType.link) {
          newSituation.action[index] = {
            type,
            description: '',
            title: '',
            summary: '',
            imageUrl: '',
            source: {
              type: LinkSourceType.fixed,
              url: '',
            },
          }
        } else if (type == ActionType.dynamicPrompt) {
          newSituation.action[index] = {
            type,
            description:'',
            customPrompt:'',
            dynamicPrompt:'',
            noSplit:false,
            chatHistoryRounds:0,
            includeMemory:false,
            includeRAG:false,
            includeTimeInfo:false,
            includeUserSlots:false,
            includeUserBehavior: false
          }
        } else if (type == ActionType.videoChannel) {
          newSituation.action[index] = {
            type,
            avatarUrl: '',
            coverUrl:'',
            description: '',
            contentDescription:'',
            nickname: '',
            thumbUrl:'',
            url: '',
            extras: ''
          }
        } else {
          toast.error(`unknow type:${e.currentTarget.value}`)
          return
        }
        setSituation(newSituation)
      }}
    >
      <option value={ActionType.text}>text</option>
      <option value={ActionType.image}>image</option>
      <option value={ActionType.video}>video</option>
      <option value={ActionType.file}>file</option>
      <option value={ActionType.voice}>voice</option>
      <option value={ActionType.link}>link</option>
      <option value={ActionType.custom}>custom</option>
      <option value={ActionType.videoChannel}>video channel</option>
      <option value={ActionType.dynamicPrompt}>dynamic prompt</option>
    </select>
  )
}

function DeleteActionButton({
  index,
  situation,
  setSituation,
}: {
  situation: Situation;
  setSituation: (sop: Situation) => void;
  index: number;
}) {
  return (
    <button
      className="btn btn-square btn-neutral"
      onClick={(e) => {
        e.preventDefault()
        const newSituation: Situation = {
          conditions: [...situation.conditions],
          action: [...situation.action].filter(
            (_item, itemIndex) => index != itemIndex,
          ),
        }
        setSituation(newSituation)
      }}
    >
      <MdDeleteForever size={24} />
    </button>
  )
}