'use client'

import Link from 'next/link'
import { useRef, useState } from 'react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import { IoSettingsSharp } from 'react-icons/io5'
import { AccountData } from '@/app/type/account'
import { SopTag } from '@/app/type/sop_tag'

export function TagShow({
  tag,
  accounts,
  tagLinkPrefix,
  changeTagEnable,
  deleteTag,
  updateTagEnableAccount,
}:{
  tag:SopTag
  accounts:AccountData[]
  tagLinkPrefix:string
  changeTagEnable(tag_id: string, enable: boolean): Promise<void>,
  deleteTag(id: string): Promise<void>
  updateTagEnableAccount(tag_id: string, enableAccounts: string[]): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  const [enable, setEnable] = useState<boolean>(tag.enable)
  const [name, setName] = useState<string>(tag.name)
  const [enableAccount, setEnableAccount] = useState<string[]>([...tag.enable_account])
  const [willUpdateName, setWillUpdateName] = useState<string>(name)
  const [willUpdateEnableAccount, setWillUpdateEnableAccount] = useState<string[]>([...enableAccount])
  const ensureDeleteDialogRef = useRef<HTMLDialogElement>(null)
  const modifyEnableAccountsDialogRef = useRef<HTMLDialogElement>(null)
  const accountsMap = new Map<string, string>()
  const router = useRouter()
  for (const account of accounts) {
    accountsMap.set(account.id, account.accountName)
  }
  return <div className='border border-base-300 p-4 shadow-sm rounded-md flex flex-col min-h-56'>
    <div className='text-lg font-semibold'>{name}</div>
    <div className='mb-2'>
      <span>enable accounts:</span>
      <button className='btn btn-circle btn-ghost btn-xs btn-neutral' onClick={() => {
        modifyEnableAccountsDialogRef.current?.showModal()
      }}>
        <IoSettingsSharp size={16}/>
      </button>
    </div>
    <div className='flex gap-1'>
      {enableAccount.map((item, index) => {
        return <span key={index} className='badge badge-outline badge-info'>{accountsMap.get(item)}</span>
      })}
    </div>
    <div className='flex flex-1 items-end gap-2 justify-between'>
      <input type="checkbox" checked={enable} disabled={loading} className="toggle toggle-success" onChange={(e) => {
        setLoading(true)
        const value = e.currentTarget.checked
        toast.promise(async() => {
          await changeTagEnable(tag.id, value)
        }, {
          pending:'update pending',
          success:'update success',
          error: 'update error'
        }).then(() => {
          setEnable(value)
        }).finally(() => {
          setLoading(false)
        })
      }}/>
      <div className='flex gap-2'>
        <button className='btn btn-error disabled:btn-disabled' disabled={loading} onClick={() => {
          ensureDeleteDialogRef.current?.showModal()
        }}>delete</button>
        <Link href={`${tagLinkPrefix}${tag.name}`}><button className='btn btn-info'>detail</button></Link>
      </div>
    </div>
    <dialog ref={ensureDeleteDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">Are you sure delete {tag.name} ?</h3>
        <p className="py-4">warning!!!</p>
        <div className="modal-action">
          <button className="btn btn-error disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(async () => {
              await deleteTag(tag.id)
            }, {
              pending:'delete pending',
              success:'delete success',
              error: 'delete error'
            }).then(() => {
              router.refresh()
            }).finally(() => {
              setLoading(false)
            })
          }}>delete!!</button>
          <button className="btn" onClick={() => {
            ensureDeleteDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <dialog ref={modifyEnableAccountsDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">修改启用账号</h3>
        <div className='flex flex-col gap-1'>
          {accounts.map((item) => {
            return <div className='flex gap-2 items-center' key={item.id}>
              <input type='checkbox' className='checkbox' checked={willUpdateEnableAccount.filter((account) => account == item.id).length > 0} disabled={loading} value={item.id} onChange={(e) => {
                const checked = e.currentTarget.checked
                if (checked) {
                  setWillUpdateEnableAccount([...willUpdateEnableAccount.filter((account) => account != item.id), item.id])
                } else {
                  setWillUpdateEnableAccount([...willUpdateEnableAccount.filter((account) => account != item.id)])
                }
              }}/><label>{item.accountName}</label>
            </div>
          })}
        </div>
        <div className="modal-action">
          <button className="btn disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(async () => {
              await updateTagEnableAccount(tag.id, willUpdateEnableAccount)
            }, {
              pending:'update account pending',
              success: 'update account success',
              error: 'update account error'
            }).then(() => {
              setEnableAccount([...willUpdateEnableAccount])
              modifyEnableAccountsDialogRef.current?.close()
            }).finally(() => {
              setLoading(false)
            })
          }}>save</button>
          <button className="btn" onClick={() => {
            modifyEnableAccountsDialogRef.current?.close()
          }}>Close</button>
        </div>
      </div>
    </dialog>
  </div>
}