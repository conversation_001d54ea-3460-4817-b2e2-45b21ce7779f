import { SopTopic } from '@/app/type/sop_topic'
import { SopTopics } from './sopTopics'
import Link from 'next/link'
import { FaPlus } from 'react-icons/fa6'
import { SopTag } from '@/app/type/sop_tag'

export async function SopTopicShow({ tag,
  querySopTopicByTag,
  changeSopTopicEnable,
  copyTopicToTag,
  renameTopic,
  queryAllTags,
  deleteTopic
}:{
  tag:string,
  querySopTopicByTag(tag:string): Promise<SopTopic[]>
  changeSopTopicEnable(id:string, enable:boolean): Promise<void>
  copyTopicToTag(topicId:string, tagName:string): Promise<void>
  renameTopic(topicId:string, name:string): Promise<void>
  queryAllTags(): Promise<SopTag[]>
  deleteTopic(id: string): Promise<void>
}) {
  'use server'
  const sopTopics = await querySopTopicByTag(tag)
  return <div className='p-4'>
    <div className='flex items-center justify-between'>
      <div className='text-2xl font-semibold mb-2'>{tag}</div>
      <Link href={`./${tag}/new_topic`}>
        <button className='btn btn-neutral btn-square'>
          <FaPlus/>
        </button>
      </Link>
    </div>
    <SopTopics
      initialTopics={sopTopics}
      changeSopTopicEnable={changeSopTopicEnable}
      tag={tag}
      queryAllTags={queryAllTags}
      copyTopicToTag={copyTopicToTag}
      renameTopic={renameTopic}
      deleteTopic={deleteTopic}
    />
  </div>
}
