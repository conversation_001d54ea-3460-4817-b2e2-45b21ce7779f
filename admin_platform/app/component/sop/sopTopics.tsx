'use client'
import { SopTag } from '@/app/type/sop_tag'
import { SopTopic } from '@/app/type/sop_topic'
import Link from 'next/link'
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react'
import { FaPen } from 'react-icons/fa6'
import { toast } from 'react-toastify'

export function SopTopics({
  tag,
  initialTopics,
  changeSopTopicEnable,
  queryAllTags,
  copyTopicToTag,
  renameTopic,
  deleteTopic
}:{
  tag:string
  initialTopics:SopTopic[]
  changeSopTopicEnable(id:string, enable:boolean): Promise<void>
  queryAllTags():Promise<SopTag[]>
  copyTopicToTag(topicId:string, tagName:string): Promise<void>
  renameTopic(topicId:string, name:string): Promise<void>
  deleteTopic(id: string): Promise<void>
}) {
  'use client'
  const [topics, setTopics] = useState<SopTopic[]>(initialTopics)
  return <div>
    <div className="grid grid-cols-3 gap-8">
      {topics.map((item) => {
        return <SopTopicDetail
          topic={item}
          key={item.id}
          tag={tag}
          setTopic={(topic:SopTopic) => {
            setTopics((pre) => {
              return pre.map((preItem) => {
                if (item.id == preItem.id) {
                  return topic
                } else {
                  return preItem
                }
              })
            })
          }}
          changeSopTopicEnable={changeSopTopicEnable}
          queryAllTags={queryAllTags}
          copyTopicToTag={copyTopicToTag}
          renameTopic={renameTopic}
          deleteTopic={deleteTopic}
        />
      })}
    </div>
  </div>
}

function SopTopicDetail({
  tag,
  topic,
  setTopic,
  changeSopTopicEnable,
  queryAllTags,
  copyTopicToTag,
  renameTopic,
  deleteTopic
}:{
  tag:string
  topic:SopTopic
  setTopic: (topic:SopTopic) => void
  changeSopTopicEnable(id:string, enable:boolean): Promise<void>
  queryAllTags():Promise<SopTag[]>
  copyTopicToTag(topicId:string, tagName:string): Promise<void>
  renameTopic(topicId:string, name:string): Promise<void>
  deleteTopic(id: string): Promise<void>
}) {
  const [loading, setLoading] = useState<boolean>(false)
  return <div className='border border-base-300 p-4 shadow-sm rounded-md flex flex-col min-h-56 justify-between'>
    <div className='flex gap-2'>
      <h4 className='font-medium text-2xl'>{topic.name}</h4>
      <RenameTopicButtonAndDialog topic={topic} loading={loading} setLoading={setLoading} renameTopic={renameTopic}/>
    </div>
    <div className='flex justify-between items-center'>
      <input type="checkbox" checked={topic.enable} className="toggle toggle-success" disabled={loading} onChange={(e) => {
        const checked = e.currentTarget.checked
        setLoading(true)
        toast.promise(changeSopTopicEnable(topic.id, checked), {
          pending:'change enable pending',
          success: 'change enable success',
          error: 'change enable error'
        }).then(() => {
          setTopic({ ...topic, enable:checked })
        }).finally(() => {
          setLoading(false)
        })
      }}/>
      <div className='flex gap-2'>
        <CopyTopicButtonAndDialog topic={topic} loading={loading} setLoading={setLoading} queryAllTags={queryAllTags} copyTopicToTag={copyTopicToTag}/>
        <Link href={`./${tag}/${topic.name}`}>
          <button className='btn btn-info btn-soft'>detail</button>
        </Link>
        <DeleteTopicButtonAndDialog topic={topic} loading={loading} setLoading={setLoading} deleteTopic={deleteTopic}/>
      </div>
    </div>
  </div>
}

function DeleteTopicButtonAndDialog({
  topic,
  loading,
  setLoading,
  deleteTopic
}:{
  loading:boolean
  setLoading: Dispatch<SetStateAction<boolean>>
  topic:SopTopic
  deleteTopic(id: string): Promise<void>
}) {
  const ensureDeleteDialogRef = useRef<HTMLDialogElement>(null)
  return <>
    <dialog ref={ensureDeleteDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">Are you sure delete {topic.name} ?</h3>
        <p className="py-4">warning!!!</p>
        <div className="modal-action">
          <button className="btn btn-error disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(async () => {
              await deleteTopic(topic.id)
            }, {
              pending:'delete pending',
              success:'delete success',
              error: 'delete error'
            }).then(() => {
              window.location.reload()
            }).finally(() => {
              setLoading(false)
            })
          }}>delete!!</button>
          <button className="btn" onClick={() => {
            ensureDeleteDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <button className='btn btn-error' onClick={() => {
      ensureDeleteDialogRef.current?.showModal()
    }}>delete</button>
  </>
}

function CopyTopicButtonAndDialog({
  topic,
  loading,
  setLoading,
  queryAllTags,
  copyTopicToTag
}:{
  loading:boolean
  setLoading: Dispatch<SetStateAction<boolean>>
  topic:SopTopic
  queryAllTags():Promise<SopTag[]>
  copyTopicToTag(topicId:string, tagName:string): Promise<void>
}) {
  const copyDialogRef = useRef<HTMLDialogElement>(null)
  const [selectedTag, setSelectedTag] = useState<string>('')
  const [tagOption, setTagOption] = useState<string[]>([])
  useEffect(() => {
    queryAllTags().then((res) => {
      setTagOption(res.map((item) => item.name))
    })
  }, [])
  return <>
    <dialog ref={copyDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">copy {topic.name} ?</h3>
        <div>选择一个目标</div>
        <div className='my-2 flex flex-col gap-2'>
          {tagOption.map((tag) => {
            return <div
              key={tag}
              className={`w-60 border mx-auto shadow rounded-md border-base-300 p-1 px-2 cursor-pointer font-medium hover:bg-base-300${  selectedTag == tag ? ' bg-base-300' : ''}`}
              onClick={() => {
                setSelectedTag(tag)
              }}
            >
              {tag}
            </div>
          })}
        </div>
        <div className="modal-action">
          <button className="btn btn-error btn-soft disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            if (selectedTag == '') {
              throw ('选择的tag是错的')
            }
            toast.promise(copyTopicToTag(topic.id, selectedTag), {
              pending:'copy pending',
              success:'copy success',
              error: 'copy error'
            }).then(() => {
              window.location.reload()
            }).finally(() => {
              setLoading(false)
            })
          }}>copy!</button>
          <button className="btn" onClick={() => {
            copyDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <button className='btn btn-accent btn-soft' onClick={() => {
      copyDialogRef.current?.showModal()
    }}>copy</button>
  </>
}

function RenameTopicButtonAndDialog({
  topic,
  loading,
  setLoading,
  renameTopic
}:{
  loading:boolean
  setLoading: Dispatch<SetStateAction<boolean>>
  topic:SopTopic
  renameTopic(topicId:string, name:string): Promise<void>
}) {
  const copyDialogRef = useRef<HTMLDialogElement>(null)
  const [newName, setNewName] = useState<string>(topic.name)
  return <>
    <dialog ref={copyDialogRef} className="modal">
      <div className="modal-box">
        <h3 className="font-bold text-lg">rename {topic.name} ?</h3>
        <fieldset className="fieldset">
          <legend className="fieldset-legend">What is new name?</legend>
          <input type="text" className="input focus-within:outline-0" placeholder="Type here" value={newName} onChange={(e) => { setNewName(e.currentTarget.value) }}/>
        </fieldset>
        <div className="modal-action">
          <button className="btn btn-error btn-soft disabled:btn-disabled" disabled={loading} onClick={() => {
            setLoading(true)
            toast.promise(renameTopic(topic.id, newName), {
              pending:'rename pending',
              success:'rename success',
              error: 'rename error'
            }).then(() => {
              window.location.reload()
            }).finally(() => {
              setLoading(false)
            })
          }}>rename</button>
          <button className="btn" onClick={() => {
            copyDialogRef.current?.close()
          }}>cancel</button>
        </div>
      </div>
    </dialog>
    <button className='btn btn-circle btn-ghost' onClick={() => {
      setNewName(topic.name)
      copyDialogRef.current?.showModal()
    }}><FaPen/></button>
  </>
}