'use client'

import dayjs from 'dayjs'
import { useState } from 'react'
import { toast } from 'react-toastify'

export function SopAnalysisConfig({
  analysisSop,
  defaultStartCourseNo,
  defaultEndCourseNo
}:{
  analysisSop(courseNo: number): Promise<void>
  defaultStartCourseNo:number,
  defaultEndCourseNo:number
}) {
  const [loading, setLoading] = useState<boolean>(false)
  return <div>
    <form action={(form) => {
      setLoading(true)
      const startCourseNo = form.get('start_course_no')
      const endCourseNo = form.get('end_course_no')
      toast.promise(async() => {
        for (let now = Number(startCourseNo); now <= Number(endCourseNo); now = Number(dayjs(String(now), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD'))) {
          await analysisSop(now)
        }
      }, {
        pending: 'update pending',
        success: 'update success',
        error: 'update error'
      }).finally(() => {
        setLoading(false)
      })
    }}>
      <div className='flex flex-col gap-2'>
        <div className="flex gap-2">
          <label className="label w-30">start courseNo</label>
          <input name="start_course_no" type="number" disabled={loading} className="input focus-within:outline-0" defaultValue={defaultStartCourseNo}></input>
        </div>
        <div className="flex gap-2">
          <label className="label w-30">end courseNo</label>
          <input name="end_course_no" type="number" disabled={loading} className="input focus-within:outline-0" defaultValue={defaultEndCourseNo}></input>
          <button className="btn disabled:btn-disabled" disabled={loading}>update</button>
        </div>
      </div>
    </form>
  </div>
}