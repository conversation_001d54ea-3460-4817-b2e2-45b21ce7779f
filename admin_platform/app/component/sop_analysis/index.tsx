'use client'

import { Sop } from '@/app/type/sop'
import { SopRecord } from '@/app/type/sop_record'
import dayjs from 'dayjs'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

export function SopAnalysis({
  getSopRecordsByCourseNo,
  queryAllSop,
  defaultStartCourseNo,
  defaultEndCourseNo,
}:{
  getSopRecordsByCourseNo(startCourseNo:number, endCourseNo:number):Promise<SopRecord[]>
  queryAllSop(): Promise<Sop[]>,
  defaultStartCourseNo:number,
  defaultEndCourseNo:number
}) {
  const [initialized, setInitiallized] = useState<boolean>(false)
  const [sopRecord, setSopRecord] = useState<SopRecord[]>([])
  const [startCourseNo, setStartCourseNo] = useState<number>(defaultStartCourseNo)
  const [endCourseNo, setEndCourseNo] = useState<number>(defaultEndCourseNo)
  const [resultStartCourseNo, setResultStartCourseNo] = useState<number>(0)
  const [resultEndCourseNo, setResultEndCourseNo] = useState<number>(0)
  const [sopIdNameMap, setSopIdNameMap] = useState<Record<string, string>>({})
  useEffect(() => {
    Promise.all([
      querySopRecords(startCourseNo, endCourseNo),
      toast.promise(queryAllSop(), {
        pending:'query sop pending',
        success:'query sop sucess',
        error: 'query sop error'
      }).then((sops) => {
        const sopMap:Record<string, string> = {}
        for (const sop of sops) {
          sopMap[sop.id] = sop.title
        }
        setSopIdNameMap(sopMap)
      })
    ]).finally(() => {
      setInitiallized(true)
    })
  }, [])
  const querySopRecords = (startCourseNo:number, endCourseNo:number) => {
    setResultStartCourseNo(startCourseNo)
    setResultEndCourseNo(endCourseNo)
    toast.promise(getSopRecordsByCourseNo(startCourseNo, endCourseNo), {
      pending:'query pending',
      success:'query success',
      error:'query error'
    }).then((res) => {
      setSopRecord(res)
    })
  }
  const dates:number[] = []
  for (let now = resultStartCourseNo; now <= resultEndCourseNo; now = Number(dayjs(String(now), 'YYYYMMDD').add(1, 'day').format('YYYYMMDD'))) {
    dates.push(now)
  }
  const sopTypes = [...new Set([...sopRecord.map((sop) => sop.sop_id)])]
  return <div className="m-2">
    <div className='flex gap-2 mb-4'>
      <h2 className="text-4xl">
      sop analysis
      </h2>
      <Link href='./sop_analysis/config' className='btn btn-neutral'>config</Link>
    </div>
    <p className='mb-2 text-sm'> 每个格子中上方的为30min回复率，下方为60分钟回复率</p>
    <form onSubmit={(e) => {
      e.preventDefault()
    }} className="flex flex-col gap-2">
      <div className="flex gap-2">
        <label htmlFor="start_course_no_input" className="label w-30">start course no:</label>
        <input type='number' id='start_course_no_input' className="input focus-within:outline-0" value={startCourseNo} onChange={(e) => setStartCourseNo(e.currentTarget.valueAsNumber)}/>
        <button type="submit" className="btn btn-neutral" onClick={() => querySopRecords(startCourseNo, endCourseNo)}>query</button>
      </div>
      <div className="flex gap-2">
        <label htmlFor="end_course_no_input" className="label w-30" >end course no:</label>
        <input type='number' id='end_course_no_input' className="input focus-within:outline-0" value={endCourseNo} onChange={(e) => setEndCourseNo(e.currentTarget.valueAsNumber)}/>
      </div>
    </form>
    {initialized && <div className='max-w-[90vw]'>
      <div className="overflow-x-scroll">
        <table className="table">
          <thead>
            <tr>
              <th></th>
              <th>detail</th>
              {dates.map((date) => <th key={date}>{date}</th>)}
              <th>average</th>
            </tr>
          </thead>
          <tbody>
            {sopTypes.map((sopId) => {
              const allSopThisSopId = sopRecord.filter((sop) => sop.sop_id == sopId)
              let totalSentCount = 0
              let totalReplyCount30Min = 0
              let totalReplyCount60Min = 0
              for (const sop of allSopThisSopId) {
                totalSentCount += sop.sent_count
                totalReplyCount30Min += sop.reply_count_30min
                totalReplyCount60Min += sop.reply_count_60min
              }
              return <tr key={sopId}>
                <th>{sopIdNameMap[sopId] ?? sopId}</th>
                <td><Link className='btn btn-neutral' href={`./sop/${sopId}`} target='_blank'>link</Link></td>
                {dates.map((date) => {
                  const singleSopRecord = sopRecord.find((sop) => sop.course_no == date && sop.sop_id == sopId)
                  return <td key={`${sopId}-${date}`}>
                    <SopReplyAnalysis sentCount={singleSopRecord?.sent_count} replyCount={ singleSopRecord?.reply_count_30min }/>
                    ------------
                    <SopReplyAnalysis sentCount={singleSopRecord?.sent_count} replyCount={ singleSopRecord?.reply_count_60min}/>
                  </td> })}
                <td>
                  <SopReplyAnalysis sentCount={totalSentCount} replyCount={totalReplyCount30Min}/>
                  --------------
                  <SopReplyAnalysis sentCount={totalSentCount} replyCount={totalReplyCount60Min}/>
                </td>
              </tr> })}
          </tbody>
        </table>
      </div>
    </div>}
  </div>
}

function SopReplyAnalysis({ sentCount = 0, replyCount = 0 }:{sentCount?:number, replyCount?:number}) {
  return <div>{((replyCount / sentCount * 100) || 0).toFixed(2)}% ({replyCount}/{sentCount})</div>
}