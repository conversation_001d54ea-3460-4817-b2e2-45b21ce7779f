'use client'

import { useState } from 'react'
import { toast } from 'react-toastify'
import { v4 } from 'uuid'
import { MdDeleteForever, MdKeyboardArrowDown } from 'react-icons/md'
import { IoIosAddCircleOutline } from 'react-icons/io'
import { Rag, RagType } from '@/app/type/rag'

export default function addRag({
  add,
  ragDoc
}: {
  add: (param: {
    question: string;
    doc: string;
    rag: Rag[];
    formData: FormData;
  }) => Promise<void>;
  ragDoc: Record<string, string>
}) {
  const [rags, setRags] = useState<Array<Rag>>([
    { type: RagType.text, key: 'default_rag_key' },
  ])

  const resetRags = () => {
    setRags([{ type: RagType.text, key: 'default_rag_key' }])
  }

  const changeType = (key: string, type: RagType) => {
    setRags((rags) =>
      rags.map((value) => {
        if (value.key == key) {
          value.type = type
        }
        return value
      }),
    )
  }

  return (
    <div>
      <form
        action={async (form: FormData) => {
          const question = form.get('question') as string
          const doc = form.get('doc') as string
          await toast
            .promise(add({ question, doc, rag: rags, formData: form }), {
              pending: 'submit is pending',
              success: 'submit resolved 👌',
              error: 'submit rejected 🤯',
            })
            .then(() => {
              resetRags()
            })
        }}
      >
        <div className="flex justify-between">
          <div className="p-2 text-xl">问题:</div>
          <button type="submit" className="btn btn-neutral ml-auto">
            提交
          </button>
        </div>
        <div>
          <select
            defaultValue="Pick one"
            className="select relative left-20 mb-4 block focus-within:outline-0"
            name="doc"
          >
            <option disabled={true}>Pick a doc</option>
            {Object.entries(ragDoc).map(([k, v], index) => (
              <option key={index} value={v}>
                {k}
              </option>
            ))}
          </select>
        </div>
        <textarea
          className="textarea validator relative left-20 w-[48rem] focus-within:outline-0"
          placeholder="问题"
          name="question"
          required
        ></textarea>
        <div className="my-4 border-b border-gray-100"></div>
        <div className="p-2 text-xl">答案:</div>
        <div className="flex flex-col gap-2 pt-4">
          {rags.map(({ type, key }, index) => {
            return (
              <div key={key} className="flex items-center gap-4">
                <Choose
                  type={type}
                  changeType={(type) => {
                    changeType(key, type)
                  }}
                />
                <div className="flex flex-col items-center justify-between gap-2">
                  {type == RagType.text && <AddText name={key} />}
                  {type == RagType.file && <AddFile name={key} />}
                </div>
                <div
                  className={
                    `flex flex-col items-center justify-between gap-2${
                      type == RagType.file ? ' relative -bottom-4' : ''}`
                  }
                >
                  {index == 0 && (
                    <div
                      className="btn btn-ghost btn-circle"
                      onClick={() => {
                        setRags([
                          ...rags.slice(0, index),
                          { type: RagType.text, key: v4() },
                          ...rags.slice(index),
                        ])
                      }}
                    >
                      <IoIosAddCircleOutline size={24} />
                    </div>
                  )}
                  <div
                    className="btn btn-neutral btn-square"
                    onClick={() => {
                      if (rags.length <= 1) return
                      setRags([
                        ...rags.slice(0, index),
                        ...rags.slice(index + 1),
                      ])
                    }}
                  >
                    <MdDeleteForever size={24} />
                  </div>
                  <div
                    className="btn btn-ghost btn-circle"
                    onClick={() => {
                      setRags([
                        ...rags.slice(0, index + 1),
                        { type: type ^ 1, key: v4() },
                        ...rags.slice(index + 1),
                      ])
                    }}
                  >
                    <IoIosAddCircleOutline size={24} />
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </form>
    </div>
  )
}

export function Choose({
  type,
  changeType,
}: {
  type: RagType;
  changeType: (type: RagType) => void;
}) {
  return (
    <div className="dropdown dropdown-hover dropdown-center">
      <div tabIndex={0} role="button" className="btn m-1 w-25">
        {type == RagType.text ? 'Text' : type == RagType.file ? 'File' : ''}
        <MdKeyboardArrowDown size={20} />
      </div>
      <ul
        tabIndex={0}
        className="dropdown-content menu bg-base-100 rounded-box z-1 w-32 p-2 shadow-sm"
      >
        <li>
          <a
            onClick={() => {
              changeType(RagType.text)
            }}
          >
            Text
          </a>
        </li>
        <li>
          <a
            onClick={(event) => {
              changeType(RagType.file)
              event.currentTarget.blur()
            }}
          >
            File
          </a>
        </li>
      </ul>
    </div>
  )
}

function AddText({ name }: { name: string }) {
  return (
    <textarea
      className="textarea validator w-[48rem] focus-within:outline-0"
      placeholder="Bio"
      name={name}
      required
    ></textarea>
  )
}

function AddFile({ name }: { name: string }) {
  return (
    <fieldset className="fieldset flex flex-col gap-4">
      <div>
        <legend className="fieldset-legend">文件描述</legend>
        <input
          type="text"
          className="input validator w-[48rem] focus-within:outline-0"
          placeholder="Type here"
          name={`${name  }_description`}
          required
        />
      </div>
      <input
        type="file"
        className="file-input validator w-[48rem] focus-within:outline-0"
        name={name}
        required
      />
    </fieldset>
  )
}
