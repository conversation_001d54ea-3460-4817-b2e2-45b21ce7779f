import SearchRag from './searchRag'
import { Rag as RagData } from '@/app/type/rag'
import AddRag from './addRag'
import { IElasticEmbeddingRes } from '../../../../bot/model/elastic_search/elastic_search'
import { ragDoc } from '@/app/rag/ragType'

export function Rag({
  addRag,
  searchRag,
  updateRag,
  deleteRag
}:{
  addRag({ question, doc, rag, formData, }: {
    question: string;
    doc: string;
    rag: RagData[];
    formData: FormData;
  }): Promise<void>
  searchRag({ question, number, minScore, }: {
      question: string;
      number: number;
      minScore: number;
      docFilter: string[];
  }): Promise<IElasticEmbeddingRes[]>
  updateRag({ question, answer, tag, doc, id, }: {
      question: string;
      answer: string;
      tag: string | null;
      doc: string;
      id: string;
  }): Promise<void>
  deleteRag(id: string, resourceNames: string[]): Promise<void>

}) {
  return (
    <div className="px-16 py-16 pt-8">
      <h3 className="text-3xl leading-16">ElasticSearch QA 管理</h3>
      <div className="tabs tabs-box mt-6 pt-[1rem] pr-[1rem] pb-[1rem] pl-[1rem]">
        <input
          type="radio"
          name="tabs"
          className="tab"
          aria-label="搜索"
          defaultChecked
        />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <SearchRag ragDoc={ragDoc} searchRag={searchRag} updateRag={updateRag} deleteRag={deleteRag}/>
        </div>

        <input type="radio" name="tabs" className="tab" aria-label="添加" />
        <div className="tab-content bg-base-100 border-base-300 mt-[1rem] p-6">
          <AddRag add={addRag} ragDoc={ragDoc} />
        </div>
      </div>
    </div>
  )

}