import type { Metadata, Viewport } from 'next'
import './globals.css'
import Link from 'next/link'
import Image from 'next/image'
import icon from '@/app/favicon.ico'
import { MyMenu } from './menu'
import { ToastContainer } from 'react-toastify'
import { Suspense } from 'react'
import { SessionProvider } from 'next-auth/react'

export const metadata: Metadata = {
  title: 'admin_platform',
  description: 'Generated by create next app',
}

export const viewport:Viewport = {
  width: '1280px',
  initialScale: 1,
  maximumScale: 1,
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <body>
        <ToastContainer position="bottom-right" />
        <SessionProvider>
          <main className='flex flex-col min-h-screen'>
            <div className="sticky top-0 z-99 flex h-12 items-center gap-2 bg-base-100 overflow-hidden shadow-sm">
              <Link href="/">
                <Image
                  src={icon.src}
                  alt="log"
                  width={icon.height}
                  height={icon.width}
                  className="ml-2 h-8 w-8 dark:invert"
                />
              </Link>
              <div>admin_platform</div>
            </div>
            <div className="flex flex-1">
              <MyMenu/>
              <div className="grow">
                <Suspense>{children}</Suspense>
              </div>
            </div>
          </main>
        </SessionProvider>
      </body>
    </html>
  )
}
