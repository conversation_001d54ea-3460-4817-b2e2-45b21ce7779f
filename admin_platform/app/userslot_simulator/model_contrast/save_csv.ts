'use server'

import path from 'path'
import { CsvHelper } from '../../../../bot/lib/csv/csv_parse'
import { contentWithFrequency } from '../../../../bot/service/moer/storage/chat_state_store'
import { ChatHistoryWithRoleAndDate, chatHistoryWithRoleAndDateListToString, UserSlots } from '../../../../bot/service/moer/components/flow/helper/slotsExtract'

type JsonRow = {
    [key: string]: {
        [col: string]: string;
    };
}

export async function saveCsv(chat_id:string, modelAName:string, modelBName:string, info:{chatHistory:ChatHistoryWithRoleAndDate[], modelA:Record<string, contentWithFrequency>, modelB:Record<string, contentWithFrequency>,}[]) {
  'use server'
  const data:JsonRow = {}
  info.forEach((value, index) => {
    data[index + 1] = {
      'chat_history':chatHistoryWithRoleAndDateListToString(value.chatHistory),
      modelAName:UserSlots.fromRecord(value.modelA).toString(),
      modelBName:UserSlots.fromRecord(value.modelB).toString()
    }
  })
  CsvHelper.write2DJson(path.join(__dirname, `${chat_id}.csv`), data, ['chat_history', modelAName, modelBName])
}