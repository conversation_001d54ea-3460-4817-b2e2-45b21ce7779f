'use client'

import { useState } from 'react'
import { toast } from 'react-toastify'
import { extractUserSlots } from '../extractUserSlot'
import { contentWithFrequency } from '../../../../bot/service/moer/storage/chat_state_store'
import { v4 } from 'uuid'
import Link from 'next/link'
import { saveCsv } from './save_csv'
import { queryChatHistory } from '@/app/api/chat_history'
import { ChatHistoryWithRoleAndDate } from '../../../../bot/service/moer/components/flow/helper/slotsExtract'

type ChatHistoryWithUserSlot = {
  chatHistory: ChatHistoryWithRoleAndDate[];
  userSloto4Mini?: Record<string, contentWithFrequency>;
  thisRoundUserSloto4Mini?: Record<string, contentWithFrequency>;
  userSloto3Mini?: Record<string, contentWithFrequency>;
  thisRoundUserSloto3Mini?: Record<string, contentWithFrequency>;
  traceId?: string;
};

export default function Page() {
  const [chatHistory, setChatHistory] = useState<ChatHistoryWithUserSlot[]>([])
  const [loading, setLoading] = useState<boolean>(false)
  const [chatId, setChatId] = useState<string>('')
  return (
    <div className="m-4 mx-auto max-w-[90rem] p-8">
      <form
        action={(form) => {
          const chatId = form.get('chat_id') as string
          setLoading(true)
          setChatId(chatId)
          toast
            .promise(queryChatHistory(chatId), {
              pending: 'query is pending',
              success: 'query resolved 👌',
              error: 'query rejected 🤯',
            })
            .then((res) => {
              const ans: ChatHistoryWithUserSlot[] = []
              const maxRound = 5
              const maxLength = 20
              let nowRound = 0
              let now: ChatHistoryWithRoleAndDate[] = []
              for (const info of res) {
                now.push(info)
                if (info.role == 'user') {
                  nowRound += 1
                }
                if (
                  info.role == 'user' &&
                  (nowRound == maxRound || now.length == maxLength)
                ) {
                  ans.push({
                    chatHistory: [...now],
                  })
                  nowRound = 0
                  now = []
                }
              }
              if (now.length > 0) {
                ans.push({
                  chatHistory: [...now],
                })
              }
              setChatHistory(ans)
            })
            .finally(() => {
              setLoading(false)
            })
        }}
        className="flex items-center gap-4"
      >
        <input
          type="text"
          className="input validator focus-within:outline-0"
          name="chat_id"
          placeholder="Type chat_id here"
          disabled={loading}
          required
          list="browsers"
        />
        <datalist id="browsers">
          <option value="7881302080029269_1688856297674945">博文</option>
        </datalist>
        <button
          type="submit"
          disabled={loading}
          className="btn btn-neutral disabled:btn-disable focus-within:outline-0"
        >
          query
        </button>
        <button
          className={
            'btn btn-neutral disabled:btn-disable focus-within:outline-0'
          }
          disabled={loading}
          onClick={async (e) => {
            e.preventDefault()
            setChatHistory((chatHistory) =>
              chatHistory.map((item) => {
                item.userSloto4Mini = undefined
                item.userSloto3Mini = undefined
                return item
              }),
            )
            setLoading(true)
            for (let i = 0; i < chatHistory.length; i++) {
              const traceId = v4()
              const [userSlotso4MIni, userSlotso3Mini] = await Promise.all([
                extractUserSlots({
                  model:'o4-mini',
                  chatHistory: chatHistory[i].chatHistory,
                  previousUserSlotsRecord:
                    i == 0 ? {} : (chatHistory[i - 1].userSloto4Mini ?? {}),
                  logInfo: {
                    trace_id: `${traceId  }o4-mini`,
                  },
                }),
                extractUserSlots({
                  model: 'o3-mini',
                  chatHistory: chatHistory[i].chatHistory,
                  previousUserSlotsRecord:
                    i == 0 ? {} : (chatHistory[i - 1].userSloto3Mini ?? {}),
                  logInfo: {
                    trace_id: `${traceId  }o3-mini`,
                  },
                }),
              ])
              chatHistory[i].userSloto4Mini = userSlotso4MIni.userslots
              chatHistory[i].userSloto3Mini = userSlotso3Mini.userslots
              setChatHistory((chatHistory) => [
                ...chatHistory.slice(0, i),
                {
                  chatHistory: chatHistory[i].chatHistory,
                  userSloto4Mini: userSlotso4MIni.userslots,
                  thisRoundUserSloto4Mini: userSlotso4MIni.thisRoundExtract,
                  userSloto3Mini: userSlotso3Mini.userslots,
                  thisRoundUserSloto3Mini: userSlotso3Mini.thisRoundExtract,
                  traceId: traceId,
                },
                ...chatHistory.slice(i + 1),
              ])
            }
            setLoading(false)
          }}
        >
          simulate
        </button>
        <button
          className="btn btn-neutral disabled:btn-disabled"
          disabled={!chatId || loading}
          onClick={(e) => {
            e.preventDefault()
            saveCsv(
              chatId,
              'o4-mini',
              'o3-mini',
              chatHistory.map((item) => {
                return {
                  chatHistory: item.chatHistory,
                  modelA: item.thisRoundUserSloto4Mini ?? {},
                  modelB: item.thisRoundUserSloto3Mini ?? {},
                }
              }),
            )
          }}
        >
          export
        </button>
      </form>
      <div className="divider"></div>
      <div className="flex flex-col gap-4">
        {chatHistory.map((item, index) => {
          return (
            <div
              key={index}
              className="flex items-start justify-between gap-8 p-4"
            >
              <div className="flex basis-1/2 flex-col self-stretch rounded-2xl border border-gray-200 p-4">
                {item.chatHistory.map((detail, index) => {
                  return (
                    <div
                      key={index}
                      className={`chat ${detail.role == 'user' ? 'chat-start' : 'chat-end'}`}
                    >
                      <div className="chat-header">{detail.date}</div>
                      <div className="chat-bubble max-w-7/12">
                        {detail.message}
                      </div>
                    </div>
                  )
                })}
              </div>
              <div
                className={
                  'sticky top-20 max-h-[calc(100dvh-7rem)] basis-1/2 overflow-y-auto rounded-2xl border border-gray-200 p-8'
                }
              >
                <div>o4-mini</div>
                {loading && item.userSloto4Mini === undefined ? (
                  <span className="loading loading-dots loading-xl"></span>
                ) : (
                  <>
                    {item.traceId && (
                      <Link
                        target="_blank"
                        href={`https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/07dcac1a-1164-4b4f-b957-9047cfa4fa94?columnVisibilityModel=%7B%22feedback_stats%22%3Atrue%2C%22reference_example%22%3Afalse%7D&timeModel=%7B%22duration%22%3A%227d%22%7D&searchModel=%7B%22filter%22%3A%22and%28eq%28run_type%2C+%5C%22llm%5C%22%29%2C+and%28eq%28metadata_key%2C+%5C%22trace_id%5C%22%29%2C+eq%28metadata_value%2C+%5C%22${item.traceId}4o%5C%22%29%29%29%22%7D&runtab=2`}
                      >
                        <button className="btn btn-neutral mb-4">
                          langSmith
                        </button>
                      </Link>
                    )}
                    {Object.entries(item.userSloto4Mini ?? {}).map(
                      ([key, value], index) => {
                        return (
                          <div key={index}>
                            {key}：{value.content} times:{value.frequency}
                          </div>
                        )
                      },
                    )}
                  </>
                )}
                <div className="divider"></div>
                <div>o3-mini</div>
                {loading && item.userSloto3Mini === undefined ? (
                  <span className="loading loading-dots loading-xl"></span>
                ) : (
                  <>
                    {item.traceId && (
                      <Link
                        target="_blank"
                        href={`https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/07dcac1a-1164-4b4f-b957-9047cfa4fa94?columnVisibilityModel=%7B%22feedback_stats%22%3Atrue%2C%22reference_example%22%3Afalse%7D&timeModel=%7B%22duration%22%3A%227d%22%7D&searchModel=%7B%22filter%22%3A%22and%28eq%28run_type%2C+%5C%22llm%5C%22%29%2C+and%28eq%28metadata_key%2C+%5C%22trace_id%5C%22%29%2C+eq%28metadata_value%2C+%5C%22${item.traceId}o3-mini%5C%22%29%29%29%22%7D&runtab=2`}
                      >
                        <button className="btn btn-neutral mb-4">
                          langSmith
                        </button>
                      </Link>
                    )}
                    {Object.entries(item.userSloto3Mini ?? {}).map(
                      ([key, value], index) => {
                        return (
                          <div key={index}>
                            {key}：{value.content} times:{value.frequency}
                          </div>
                        )
                      },
                    )}
                  </>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
