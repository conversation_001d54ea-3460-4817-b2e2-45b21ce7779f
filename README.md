# 墨尔冥想

要开始使用此项目，请按照以下步骤进行操作：

## 步骤1：安装依赖项
运行下面的命令来安装所有必要的依赖项：
`npm install` 或 `yarn install`

生成 prisma client 用于数据库交互
`prisma generate` 或 `npx prisma generate`

建议装一下 Prisma ORM 的 JetBrains 或 Vscode 插件

## 步骤2：本地测试
`npm run local`

### 启动现有的客户端
配置中的`syq`客户端目前使用`natapp.cn`进行内网穿透。

从 `https://natapp.cn/#download` 下载对应的客户端，然后使用 `./natapp -authtoken=98992eb2883bc316` 启动内网穿透

使用以下命令启动本地服务：
`npm run client:syq`

## 项目结构：
可以从 WorkFlow.step 作为入口来阅读代码， 然后选择一些节点如 PostSaleNode 来阅读逻辑

## 注意：
1. 每个模块尽量进行单元测试，方法的参数尽可能少，使单元测试变的简单。
2. 尽量使用封装后的 LLM 类进行模型调用，有模型轮转机制，以及配置了 Langsmith 方便查看日志
3. 日志不要使用 console.log, 尽量使用 logger.log 使用，方便日志持久化后，进行错误排查
   1. logger.trace({ chat_id }, 'trace') 打日志的时候加入 { chat_id } , 会将这条日志记录到后台对应的人中
4. 推荐的 Prompt 编写流程 
   1. 首先深入理解业务，弄清自己要编写的内容的运行规则 (去抽象化) （参考实习生原理，假设实习生能一眼看懂，并不需要额外信息补充直接执行）。 
   2. 使用 MetaPrompt 方法调用生成
   3. 编写单元测试，进行测试。
   4. 如果效果不好，可以尝试修改 Prompt 或 使用 PromptPerfect 交互式进行优化
   5. 如修改任务描述不起作用，可以尝试将不好的 Case 加入到 few shots 中
5. 特别注意消息队列，尽量绑定到当前账号服务上消费，所以一定要绑定上账号 id, 如果不绑定会导致到其他账号的 worker 消费可能会有资源抢占的问题
6. ChatStateStore 会将内存中的状态记录到数据库，特别注意在非 step 流程中使用的时候， 要 使用 await ChatStatStoreManager.initState(chatId) 将数据库信息加载到内存，修改之后要使用 saveChat 将状态保存回数据库，防止服务重启后，内存中信息丢失， 或因为没加载数据库的内容，导致内存中数据错误覆盖
7. 账号的消息队列用于 处理账号相关的消息，如账号内客户的 SOP, 检查手机号绑定事件等。消息队列写法，事件尽量使用现有 worker，比如账号的事件 worker, 创建 Worker 的时候注意监听处理 error 事件，避免 worker 处理出现问题导致 Worker 停止工作。

## 文档
获取所有代码
   cd bot  && code2prompt . --exclude="*.json,*.test.ts,**/test/*" --exclude-from-tree --tokens

获取所有文档   
   cd docs && code2prompt . 