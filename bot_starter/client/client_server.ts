import express from 'express'
import { IEventData } from '../../bot/lib/auchuang/event/type'
import { IReceivedMessage, ISendMessageResult } from '../../bot/lib/juzi/type'
import { GlobalMessageHandlerService } from '../../bot/service/message/message_merge'
import { CacheDecorator } from '../../bot/lib/cache/cache'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { MoerEventHandler } from '../handler/moer_event'
import { TestHandler } from './test'
import { JuziEvent } from '../handler/juzi_event'
import { ClientAccountConfig } from '../config/account'
import { initConfig } from './init'
import { exec } from 'child_process'
import logger from '../../bot/model/logger/logger'
import { SendMessageResultHandler } from '../handler/send_result'
import axios from 'axios'
import { catchGlobalError } from '../helper/helper'
import { bindPhone } from '../helper/bindPhone'
import { LRUCache } from 'lru-cache'
import { ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'

export interface IMoerEvent {
  event: string
  [key: string]: any
}

export interface ITimeConfig {
  chatId: string
  jumpTime: string
  isStartPush?: boolean // 是否开始主动发送营销信息
  status?: {
    is_complete_pre_course?: boolean // 是否完成小讲堂
    is_complete_day1_course?: boolean
    is_complete_day1_course_recording?: boolean
    is_complete_day2_course?: boolean
    is_complete_day2_course_recording?: boolean
    is_complete_day3_course?: boolean
    is_complete_day3_course_recording?: boolean
    is_complete_day4_course?: boolean
    is_complete_day4_course_recording?: boolean
  }
}

export interface ITestEvent {
  chatId: string
  name: string // 事件名称
  [key: string]: any
}

const app = express()
app.use(express.json())
app.get('/', (req, res) => {
  logger.log('Hello Client, this is Server!')
  res.send('Hello Client!')
})

// 自动化部署
app.post('/webhook', async (req, res) => {
  const payload = req.body
  console.log(JSON.stringify(payload, null, 4))

  // 检查是否为开发分支推送
  if (payload.ref !== 'refs/heads/moer_dev') {
    return res.status(200).json({ message: 'Not moer branch push, ignoring.' })
  }

  // 执行部署脚本
  try {
    // 使用 Promise 封装异步子进程执行
    const runCommand = (command: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            reject(error)
            return
          }
          resolve(stdout)
        })
      })
    }

    await runCommand('git pull')
    await runCommand('npm install')
    await runCommand('npm run prisma generate')
    try {
      await runCommand('npm run tsc-check')
    } catch (e) {
      // 直接发短信提示报错
      await axios.get(`https://fwalert.com/d675887f-7151-4bb1-9da4-fdb7000c9c23?user=${encodeURIComponent(payload.user.name)}`)
    }
    await runCommand('fuser -k 4096/tcp')

    // 更新后台
    await runCommand('cd admin_platform && npm run build')
    await runCommand('fuser -k 3000/tcp')

    res.status(200).json({ message: 'Deployment script executed successfully.' })
  } catch (error) {
    logger.error(error)
    res.status(500).json({
      message: 'Deployment failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})


app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  GlobalMessageHandlerService.addMessage(msg) // 添加到消息队列
  res.send('ok')
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data: IEventData = req.body

  JuziEvent.handle(data)
  res.send('ok')
})

app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data: ISendMessageResult = req.body

  SendMessageResultHandler.handle(data) // 处理消息发送结果
  res.send('ok')
})

app.post('/moer/event', async (req, res) => {
  // 接收消息
  const data: IMoerEvent = req.body

  MoerEventHandler.handle(data) // 处理 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})

/**
 * 测试接口
 */
app.post('/test/timeConfig', async (req, res) => {
  const data = req.body as ITimeConfig
  TestHandler.handleTimeConfig(data)
  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event', async (req, res) => {
  const data = req.body as ITestEvent
  TestHandler.handleEvent(data)
  res.send({
    code: 200,
    msg: 'ok'
  })
})

interface IClearCache {
  chatId: string
}

app.post('/clear_cache', async(req, res) => {
  const data = req.body as IClearCache

  // 强制从数据库重新读取状态
  await ChatStatStoreManager.initState(data.chatId, true)

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/tags', async (req, res) => {
  // 接收消息
  const data = req.body
  if (data.tag === true) {
    data.tag = 'AI off'
  } else {
    data.tag = 'AI on'
  }

  // await HumanTransfer.updateTags(data.userId, data.tag)
  res.send({
    code: 200,
    msg: 'ok'
  })
})

interface IBindEvent {
  chat_id: string
  phone: string
}

// cache 一下 bindPhone 的请求
const bindPhoneCache =  new LRUCache<string, any>({
  max: 100,
  ttl: 10 * 60 * 1000   // 10 分钟后数据自动删除（单位：毫秒）
})

app.post('/bindPhone', async (req, res) => {
  const data: IBindEvent = req.body
  try {
    if (bindPhoneCache.has(JSON.stringify(data))) {
      res.send({
        code: 200,
        msg: 'ok'
      })

      return
    }

    bindPhoneCache.set(JSON.stringify(data), true) // 10 分钟以内做重复请求的限流

    await bindPhone(data)
    res.send({
      code: 200,
      msg: 'ok'
    })
  } catch (e) {
    logger.error('手机号绑定失败', e)

    res.send({
      code: 500,
      msg: 'error'
    })
  }
})

// 缓存 API，提高性能
JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  const name = process.env.WECHAT_NAME

  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  await initConfig()

  app.listen(account.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${account.port}`)
  })
}