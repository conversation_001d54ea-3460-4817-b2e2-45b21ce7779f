import { ITestEvent, ITimeConfig } from './client_server'
import { SendWelcomeMessage } from '../../bot/service/moer/components/flow/schedule/task/sendWelcomeMessage'
import { TaskName } from '../../bot/service/moer/components/flow/schedule/type'
import { MessageSender } from '../../bot/service/moer/components/message/message_send'
import { ChatHistoryService } from '../../bot/service/moer/components/chat_history/chat_history'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { clearTasks } from '../../bot/service/moer/components/flow/schedule/task_starter'
import { getUserId } from '../../bot/config/chat_id'
import { DateHelper } from '../../bot/lib/date/date'
import { MoerEventHandler } from '../handler/moer_event'
import { DataService } from '../../bot/service/moer/getter/getData'
import logger from '../../bot/model/logger/logger'
import { MoerNode } from '../../bot/service/moer/components/flow/nodes/type'
import { getState } from '../../bot/service/moer/components/flow/schedule/task/baseTask'
import { CourseFeedBackDay3InClass } from '../../bot/service/moer/components/flow/nodes/courseFeedback'
import { LLMNode } from '../../bot/service/moer/components/flow/nodes/llm'
import { sleep } from 'openai/core'
import { jumpDate } from '../../bot/lib/date/jump'
import { HomeworkTemplate } from '../../bot/service/moer/components/flow/helper/homeworkTemplate'
import { ContextBuilder } from '../../bot/service/moer/components/agent/context'
import { LLM } from '../../bot/lib/ai/llm/LLM'
import removeMarkdown from 'markdown-to-text'
import { WealthOrchardRag } from '../../bot/service/moer/components/rag/wealth_orchard'

interface ICompleteEnergyTestEvent {
  name: 'complete_homework_1'
  chatId: string
  score: number
}

interface ICompleteHomework1 {
  name: 'complete_homework_1'
  chatId: string
  input: string
}

interface ICompleteHomework2 {
  name: 'complete_homework_2'
  chatId: string
  input: string
}

/**
 * 用于处理测试
 */
export class TestHandler {
  public static async handleTimeConfig(data : ITimeConfig) {
    try {
      // 设置系统时间为指定日期
      jumpDate(data.jumpTime)
      const chat_id = data.chatId
      const senderId = getUserId(chat_id)

      await MessageSender.sendById({
        user_id: senderId,
        chat_id: chat_id,
        ai_msg: `聊天已重置 当前时间 ${DateHelper.getFormattedDate(new Date(), true)}`
      })
      await ChatHistoryService.clearChatHistory(chat_id)
      await ChatStatStoreManager.clearState(chat_id)
      if (await ChatDB.getById(chat_id)) {
        await ChatDB.setHumanInvolvement(chat_id, false)
      }

      await clearTasks(chat_id)


      // 设置 状态
      if (data.status) {
        ChatStateStore.update(chat_id, {
          state: {
            ...data.status,
            is_in_day1_class: data.status.is_complete_day1_course,
            is_in_day2_class: data.status.is_complete_day2_course,
            is_in_day3_class: data.status.is_complete_day3_course,
            is_in_day4_class: data.status.is_complete_day4_course,
          }
        })
      }

      if (data.isStartPush) {
        await new SendWelcomeMessage().process({
          userId: senderId,
          chatId: chat_id,
          name: TaskName.SendWelcomeMessage
        })
      }
    } catch (e) {
      logger.error('TestHandler handleTimeConfig error', e)
    }
  }

  static async handleEvent(data: ITestEvent) {
    try {
      const moerId = await DataService.getMoerIdByChatId(data.chatId)
      const userId = getUserId(data.chatId)
      if (!moerId) {
        logger.warn('找不到对应的moerId', data.chatId)
        return
      }

      await ChatStatStoreManager.initState(data.chatId)

      switch (data.name) {
        case 'complete_pre_course':
          await MoerEventHandler.handlePreCourseComplete({
            event: '',
            userId:moerId
          })

          break
        case 'complete_energy_test':
          data = data as ICompleteEnergyTestEvent

          await MoerEventHandler.handleCompleteEnergyTest({
            event: '',
            userId: moerId,
            examScore: data.score,
            logid: '',
            mobile: ''
          })
          break
        case 'complete_homework_1':
          data = data as ICompleteHomework1
          await HomeworkTemplate.handleDay1Homework(data.chatId, userId, data.input)
          break
        case 'complete_homework_2':
        {
          data = data as ICompleteHomework2
          const relevanceContext = await WealthOrchardRag.wealthOrchardRag(data.input)

          await LLMNode.invoke({
            state: await getState(data.chatId, getUserId(data.chatId), data.input),
            dynamicPrompt: `客户完成第二课中和财富果园冥想跟练，根据练习指引，客户会看到些画像，我们目标是需要帮助它完成的冥想内容的解读，对课堂内容吸收更好。
（1）根据画面几个纬度（果园大门的颜色/材质/新旧，果树状态和果实品种/数量，果园的围栏，和果树的互动，秋天的行为和收获），秋天行为和四季的景象来进行详细解释。
按照客户提到的部分进行解释就好。
（2）为客户总结下当前反馈出来财富卡点，可以后续多注意练习提升。
（3）询问客户解读是否有启示或者不明白的地方，进一步解释。

参考下面画面对应的解读的文档：
${relevanceContext}

例如: 
例1:客户看到的画面是：没有大门,小冠木围栏，主树不清晰，没有看到结果,四季常青。

"咱们的画面还挺独特的，没有大门，可能意味着在获取财富的道路上没有明显的限制和阻碍，有着较为开放的态势。小冠木围栏为果园提供了一定的边界感，但是预示咱们更倾向于自然的增长方式。
四季常青的景象，显示出财富的发展具有相对稳定和持续的特点。"
"然而，从财富卡点的角度来看，可能存在以下一些情况：
1.缺乏明确方向：主树不清晰且没有看到结果，也许暗示在财富积累的过程中，您对核心的财富来源和成果缺乏明确的认知，可能需要进一步探索和确定。
2.防护力度较弱：小冠木围栏的防护相对较薄弱，可能意味着在财富保护方面存在一定的不足，容易受到外界因素的影响。
3.主动进取不足：没有大门的设定，虽然看似没有限制，但也可能反映出在追求财富时缺乏主动设置目标和规划的意识
总的来说，觉察是改变的开始。看到现状就离目标近一大步，后续慢慢练习，会让我们的果园越来越富足的。"

例2:客户看到画面：灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了[捂脸][捂脸]

“每个画面其实都在反映您和财富关系的一个侧面。咱们得画面充满着丰收的喜悦。
灰色钢铁门是一种坚固和稳定的象征，表明咱们对事业上采取的是坚决和果断的态度，对挑战不屈不挠，在财务决策上也比较有警觉性。个人边界感比较强。
众多的苹果树，尤其是最大的果树在中间且结了很多苹果，这象征着丰富的财富成果。
整个果园只有你一个人在爬树上摘果，显示出你在财富获取上的积极主动和独立性。”

“然而，从财富卡点的角度来看，可能存在以下一些情况：
【1】缺乏防护意识：没有围栏，也许意味着在财富保护方面缺乏一定的警惕性，容易让财富面临潜在的风险。
【2】孤独前行隐患：只有自己在果园劳作，可能反映出在财富积累过程中，缺乏合作和团队支持，可能会限制财富的进一步扩大。
"总的来说，你的冥想画面反映了你对财富、内心成长和秩序的追求。在未来一个月，你可以通过坚持冥想，提升自我的觉知能力，更好地了解"

客户看到的画面是: ${data.input}`,
            noSplit: true,
            noInterrupt: true,
            chatHistoryRounds: 0
          })

          break
        }
        case 'paid':
          if (moerId) {
            await MoerEventHandler.handlePaidCourse({
              event: '',
              userId: moerId,
              sku: ''
            })
          }
          break

        case 'sale':

          await MessageSender.sendById({
            user_id: userId,
            chat_id: data.chatId,
            ai_msg: '进入销售'
          }, {
            notAddBotMessage: true
          })
          break

        case 'day3sale':
          // mock 任务
          ChatStateStore.update(data.chatId, {
            nextStage: MoerNode.CourseFeedBackDay3InClass
          })
          await CourseFeedBackDay3InClass.invoke(await getState(data.chatId, userId))
          break

        case 'day4sale':
        {

          const userSlots = await ContextBuilder.getCustomerPortrait(data.chatId)

          if (userSlots) {
            const llmRes = await LLM.predict(`参考之前客户的需求和痛点：
${userSlots} 
              
根据之前客户的需求和痛点, 提供后续21天系统课程对客户针对性带来的好处。
            
例如:
"到此，我们全部直播课就结束了哦，之前沟通的咱们想 xxx，看咱们课程和作业都认真完成了，不知道咱们这几天学习下来感觉怎么样？"
输出以"到此，我们全部直播课就结束了哦"开头，以"不知道咱们这几天学习下来感觉怎么样？"结束。输出不要超过三句话，简短，清晰。`)

            await MessageSender.sendById({
              chat_id: data.chatId,
              user_id: userId,
              ai_msg: removeMarkdown(llmRes)
            })
          } else {
            await MessageSender.sendById({
              chat_id: data.chatId,
              user_id: userId,
              ai_msg: '到此，我们全部直播课就结束了哦，之前看咱们课程和作业都认真完成了，不知道咱们这几天学习下来感觉怎么样？因为体验课时间有限，如果需要更细化学习，还是需要参加我们的21天系统班课程。在系统班中，我们会有更专业的指导和更完整的课程体系，更有利于帮助您养成良好的习惯，真正实现持久的改变和提升。'
            })
          }

          await sleep(1000)
          await MessageSender.sendById({
            chat_id: data.chatId,
            user_id: userId,
            ai_msg: '咱们现在遇到什么卡点了不，我们可以一起来看看怎么突破？距离咱们唐宁老师专属1000元优惠券过期还有最后2小时'
          })

          break }
      }

      await DataService.saveChat(data.chatId, userId)
    } catch (e) {
      logger.error('TestHandler handleEvent error', e)
    }
  }
}