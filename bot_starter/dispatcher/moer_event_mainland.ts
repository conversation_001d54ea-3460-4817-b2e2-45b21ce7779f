import { IMoerEvent } from '../client/client_server'
import express from 'express'
import { MoerEventForwardHandler } from '../handler/moer_event_forward'
import { CacheDecorator } from '../../bot/lib/cache/cache'
import { MoerAPI } from '../../bot/model/moer_api/moer'
import { DanmuHelper } from '../../bot/service/moer/components/danmu/danmu'
import { catchGlobalError } from '../helper/helper'
import logger from '../../bot/model/logger/logger'
import { Config } from '../../bot/config/config'
import { LRUCache } from 'lru-cache'
import { bindPhone } from '../helper/bindPhone'

const app = express()
app.use(express.json())

/**
 * 分发 Moer 的事件到对应的服务器
 */
app.post('/moer/event', async (req, res) => {
  // 接收消息
  const data: IMoerEvent = req.body

  MoerEventForwardHandler.handle(data) // 处理 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})


interface IBindEvent {
  chat_id: string
  phone: string
}

// cache 一下 bindPhone 的请求
const bindPhoneCache =  new LRUCache<string, any>({
  max: 100,
  ttl: 10 * 60 * 1000   // 10 分钟后数据自动删除（单位：毫秒）
})

app.post('/moer/bindPhone', async (req, res) => {
  const data: IBindEvent = req.body
  try {
    if (bindPhoneCache.has(JSON.stringify(data))) {
      res.send({
        code: 200,
        msg: 'ok'
      })

      return
    }

    bindPhoneCache.set(JSON.stringify(data), true) // 10 分钟以内做重复请求的限流

    await bindPhone(data)
    res.send({
      code: 200,
      msg: 'ok'
    })
  } catch (e) {
    logger.error('手机号绑定失败', e)

    res.send({
      code: 500,
      msg: 'error'
    })
  }
})

const serverPort = 4002


catchGlobalError()

app.listen(serverPort, '0.0.0.0', () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务
Config.setting.bindingPhone = true
MoerAPI.getUserById = CacheDecorator.decorateAsync(MoerAPI.getUserById)

// 开启 弹幕事件监听
DanmuHelper.processPullDanmu()