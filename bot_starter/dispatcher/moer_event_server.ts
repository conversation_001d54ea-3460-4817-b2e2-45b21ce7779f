import { IMoerEvent } from '../client/client_server'
import express from 'express'
import { catchGlobalError } from '../helper/helper'
import { Config } from '../../bot/config/config'
import { MoerEventForwardHandler } from '../handler/moer_event_forward'
import logger from '../../bot/model/logger/logger'

const app = express()
app.use(express.json())

/**
 * 只负责分发 Moer 的事件到 国内事件转发，和 国外事件转发
 */
app.post('/moer/event', async (req, res) => {
  // 接收消息
  const data: IMoerEvent = req.body

  logger.log(JSON.stringify(data, null, 4))

  MoerEventForwardHandler.forwardByRegion(data) // 转发 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})

const serverPort = 4001


catchGlobalError()

app.listen(serverPort, '0.0.0.0', () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务