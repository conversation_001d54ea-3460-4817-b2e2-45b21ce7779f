import { MoerAPI } from '../../bot/model/moer_api/moer'
import { DataService } from '../../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { NewCourseUser } from '../../bot/service/moer/components/flow/helper/newCourseUser'
import { ChatDB } from '../../bot/service/moer/database/chat'


export async function bindPhone(data: {chat_id: string; phone: string }) {
  // 校验下是否已经绑定了手机号，校验下是否为 中国手机号
  const chat = await ChatDB.getById(data.chat_id)

  await updateInfo(chat, data.phone)


  async function updateInfo(chat, mobile: string) {
    const phone = mobile
    const moerUser = await MoerAPI.getUserByPhone(phone)
    const courseNo = DataService.parseCourseNo(moerUser)

    await ChatStatStoreManager.initState(chat.id) // 防止状态丢失

    await NewCourseUser.create(chat.contact.wx_id, chat.id, courseNo, phone, moerUser.id.toString())
  }
}