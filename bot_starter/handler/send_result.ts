import { ISendMessageResult } from '../../bot/lib/juzi/type'
import { ChatHistoryService } from '../../bot/service/moer/components/chat_history/chat_history'

export class SendMessageResultHandler {
  public static async handle(data: ISendMessageResult) {
    // 将 messageId 中的 externalRequestId 替换为真实的 messageId
    const externalId = data.externalRequestId
    if (!externalId) return

    const message = await ChatHistoryService.getMessageByMessageId(externalId)
    if (!message) return

    await ChatHistoryService.updateMessageId(message.id, data.messageId)
  }
}