import { IMoerEvent } from '../client/client_server'
import logger from '../../bot/model/logger/logger'
import {
  CheckPreCourseCompletionTask
} from '../../bot/service/moer/components/flow/schedule/task/checkPreCourseCompletion'
import { TaskName } from '../../bot/service/moer/components/flow/schedule/type'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { HandleEnergyTest } from '../../bot/service/moer/components/flow/schedule/task/handleEnergyTest'
import { getUserId } from '../../bot/config/chat_id'
import { ObjectUtil } from '../../bot/lib/object'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { MessageSender } from '../../bot/service/moer/components/message/message_send'
import { MoerNode } from '../../bot/service/moer/components/flow/nodes/type'
import { sleep } from '../../bot/lib/schedule/schedule'
import { IWecomMsgType } from '../../bot/lib/juzi/type'
import { DataService } from '../../bot/service/moer/getter/getData'
import { HumanTransfer, HumanTransferType } from '../../bot/service/moer/components/human_transfer/human_transfer'
import { LLMNode } from '../../bot/service/moer/components/flow/nodes/llm'
import { getState } from '../../bot/service/moer/components/flow/schedule/task/baseTask'
import { ChatHistoryService } from '../../bot/service/moer/components/chat_history/chat_history'
import { AsyncLock } from '../../bot/lib/lock/lock'
import { FlowTask } from '../../bot/service/moer/components/schedule/silent_requestion'
import { IScheduleTime } from '../../bot/service/moer/components/schedule/creat_schedule_task'
import { ContextBuilder } from '../../bot/service/moer/components/agent/context'
import { LLM } from '../../bot/lib/ai/llm/LLM'
import { Config } from '../../bot/config/config'
import { StringHelper } from '../../bot/lib/string'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { NewCourseUser } from '../../bot/service/moer/components/flow/helper/newCourseUser'
import { catchError } from '../../bot/lib/error/catchError'
import { MoerAPI } from '../../bot/model/moer_api/moer'
import { DateHelper } from '../../bot/lib/date/date'
import { ScheduleTask } from '../../bot/service/moer/components/schedule/schedule'
import { getGeneralSopKey } from '../../bot/service/moer/components/flow/schedule/task_starter'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { FlowTaskType } from '../../bot/service/moer/components/schedule/silent_reask_tasks'


interface ILiveStreamEvent {
  channelId: string          // 频道号
  groupId?: string           // 分组id，非必传
  viewerId: string           // 参会人ID
  nickName?: string          // 客户昵称
  logType: number            // 日志类型
  interactType: string       // 日志类型对应枚举
  logTime: number            // 日志时间戳
  ipAddress?: string         // IP地址，非必传
  userAgent?: string         // UA信息，非必传
  referer?: string           // referer信息 - 请求来源，非必传
  viewerCount: number        // 当前参会人数
  timestamp: number          // 13位毫秒级时间戳
  sessionId: string | null   // 当前场次
  userOrigin?: string        // 客户来源，非必传
  content?: string           // logType为101和102时，表示学员名单的oss地址
  role: 'teacher' | 'guest' | 'viewer' | 'assistant' | 'attendee' | 'listener' // 客户身份信息
  inClass?: number           // 频道是否在直播中，1表示正在直播，2表示不在直播
  status: number             // 状态码（可能表示成功或失败的标志）
  websocketId: string        // websocket连接id，用于计算参与直播时长
  event: string
}

export interface EnergyTestEvent {
  logid: string
  examScore: number
  userId: number | string
  mobile: string
  event: string
}

export interface PaymentEvent {
  sku: string
  userId: number | string
  event: string
}

export interface IXiaoHongshuOrderEvent {
  logid: string // 日志ID
  userId: number // 客户ID
  mobile: string // 手机号
  wxId: string // 微信ID
  event: string // 事件名称
  transferNo: string // 转账编号
  course_type: number // 课程类型 0 (创建客户）1（绑定课程）
  stage?: number // 阶段 (可选)
}

interface IBindWechatUserEvent {
  logid: string
  event: string
  externalUserID: string
  name: string
  unionid: string
  avatar: string
  createdAt: string
}

interface IMergeUserEvent {
  event: string
  oldUserId: number
  oldMobile: string
  newUserId: number
  newMobile: string
  project: string
}

interface ITanglangLiveEnterExitEvent {
  logid: string              // 日志ID
  event: string              // 事件类型，固定值：tanglang_live_enter_exit
  pushType: string           // 推送事件类型，固定值：LIVE_IN_OUT
  messageId: string          // 推送消息id
  actionType: 'ENTER_LIVE' | 'LEAVE_LIVE'  // 进出直播间类型
  time: string               // 发生时间 yyyy-MM-dd HH:mm:ss
  liveNum: number            // 直播间id
  scrmUserCode: number       // 客户id
  unionId: string            // unionId (就是 userId 就是 moerId)
  sessionId: string          // 学员进入直播间的sessionId,单次进入和退出直播间的值相同
  companyId: number          // 公司id
  shareUserId: string | null // 分享人id
  openId: string | null      // openId
  externalUserList: Array<{
    corpId: string           // 企微id
    externalUserId: string   // 外部联系人id
  }>

  project?: 'mainland' | 'international' // 国内/国际
}

export class MoerEventHandler {
  private static moerIdMap: Map<string, string> | undefined
  public static async handle(event: IMoerEvent) {
    console.log(JSON.stringify(event, null, 4))

    if (ObjectUtil.isEmptyObject(event)) {
      // 忽略空事件
      return
    }

    try {
      switch (event.event) {
        // 小课堂完课
        case 'course_study_guide':
          this.handlePreCourseComplete(event)
          break

        // 能量测评完成
        case 'jinshuju_user_exam_score':
          this.handleCompleteEnergyTest(event as EnergyTestEvent)
          break

        // 录播课完课
        case 'course_study_review':
          this.handleCourseComplete(event)
          break

        // 付款
        case 'course_pay_paid':
          logger.log(JSON.stringify(event, null, 4))
          this.handlePaidCourse(event as PaymentEvent)
          break

        // 付款失败
        case 'course_pay_unpaid':
          logger.log(JSON.stringify(event, null, 4))
          this.handlePaymentFailure(event as PaymentEvent)
          break

        // 进入直播间，离开直播间
        case 'live_stream_status':
          this.handleLiveStreamStatus(event as ILiveStreamEvent)
          break

        case 'tanglang_live_enter_exit':
          this.handleTanglangLiveEnterExit(event as ITanglangLiveEnterExitEvent)
          break

        case 'xiaohongshu_order':
        case 'baoding_order':
          this.handleXiaoHongshuOrder(event as IXiaoHongshuOrderEvent)
          break

        case 'merge_user':
          this.handleMergeUser(event as IMergeUserEvent)
          break

        default:
          this.handleUnknownEvent(event)
          break
      }
    } catch (e) {
      logger.error('moer event handler error:', e)
    }
  }

  static async handlePreCourseComplete(event: IMoerEvent) {
    logger.log(JSON.stringify(event, null, 4))

    const chat = await ChatDB.getByMoerId(event.userId.toString())
    if (!chat) {
      return
    }

    await ChatStatStoreManager.initState(chat.id)

    ChatStateStore.update(chat.id, {
      state: {
        is_complete_pre_course: true,
      },
    })

    // 加个锁，防止跟对话冲突
    const lock = new AsyncLock()

    await lock.acquire(chat.id, async () => {
      await new CheckPreCourseCompletionTask().process({
        name: TaskName.CheckPreCourseCompletion,
        chatId: chat.id,
        userId: getUserId(chat.id),
      })
    }, { timeout: 3 * 60 * 1000 }) // 时间设长点，防止任务失效


    await DataService.saveChat(chat.id, getUserId(chat.id))
  }

  public static async handleCompleteEnergyTest(event: EnergyTestEvent) {
    logger.log(JSON.stringify(event, null, 4))

    const eventData = event
    const chatInfo = await ChatDB.getByMoerId(event.userId.toString())
    if (!chatInfo) {
      logger.warn('MoerId 未绑定')
      return
    }

    await ChatStatStoreManager.initState(chatInfo.id)

    if (!Config.isOnlineTestAccount() && ChatStateStore.get(chatInfo.id).state.is_complete_energy_test) {
      return
    }

    ChatStateStore.update(chatInfo.id, {
      state: {
        is_complete_energy_test: true,
      },
      userSlots: {
        energy_test_score: eventData.examScore,
      },
    })

    const userId = getUserId(chatInfo.id)
    await DataService.saveChat(chatInfo.id, userId)

    // 加个锁，防止跟对话冲突
    const lock = new AsyncLock()

    await lock.acquire(chatInfo.id, async () => {
      await sleep(60 * 1000)

      await new HandleEnergyTest().process({
        name: TaskName.CompleteEnergyTest,
        chatId: chatInfo.id,
        userId: getUserId(chatInfo.id),
      })
    }, { timeout: 3 * 60 * 1000 }) // 时间设长点，防止任务失效


    await DataService.saveChat(chatInfo.id, userId)
  }

  public static handleCourseComplete(event: IMoerEvent) {
    // logger.debug('完课:', JSON.stringify(event, null, 4))
  }

  public static async handlePaidCourse(event: PaymentEvent) {
    logger.debug('已付款:', JSON.stringify(event, null, 4))
    const chatUser = await ChatDB.getByMoerId(event.userId.toString())
    if (!chatUser) {
      return
    }
    await ChatStatStoreManager.initState(chatUser.id)

    if (ChatStateStore.get(chatUser.id).state.is_complete_payment) {
      return
    }
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.chat.update({ where:{ id:chatUser.id }, data:{ pay_time:new Date() } })

    ChatStateStore.update(chatUser.id, {
      state: {
        is_complete_payment: true,
      },
      nextStage: MoerNode.PostSale,
    })
    const userId = getUserId(chatUser.id)

    await DataService.saveChat(chatUser.id, userId)

    await MessageSender.sendById({
      chat_id: chatUser.id,
      user_id: getUserId(chatUser.id),
      ai_msg: '恭喜同学加入系统课，感恩精进，咱们收件地址是哪里，给咱们寄垫子。',
    })

    await HumanTransfer.transfer(chatUser.id, userId, HumanTransferType.PaidCourse, 'onlyNotify')

    // 更新备注
    await catchError(JuziAPI.updateUserAlias(Config.setting.wechatConfig?.id as string, userId, `F${chatUser.course_no}${chatUser.contact.wx_name.slice(0, 10)}`))
  }

  public static async handlePaymentFailure(event: IMoerEvent) {
    logger.debug('付款失败:', JSON.stringify(event, null, 4))
    const chatUser = await ChatDB.getByMoerId(event.userId.toString())
    if (!chatUser) {
      return
    }
    await ChatStatStoreManager.initState(chatUser.id)

    // 如果已付款，不再处理
    if (await DataService.isPaidSystemCourse(chatUser.id)) {
      return
    }

    if (ChatStateStore.getFlags(chatUser.id).handled_failed_payment) {

      await ChatHistoryService.addUserMessage(chatUser.id, '下单失败')

      await LLMNode.invoke({
        state: await getState(chatUser.id, getUserId(chatUser.id)),
        dynamicPrompt: '客户当前正在犹豫是否下单，后台看到了下单失败。可以询问客户："班班在后台看到了您的订单，但还未支付成功哦，咱这边是遇到什么问题了么"',
      })
      return
    }

    ChatStateStore.update(chatUser.id, {
      state: {
        handled_failed_payment: true,
      },
    })

    await sleep(3000)


    await MessageSender.sendById({
      chat_id: chatUser.id,
      user_id: getUserId(chatUser.id),
      ai_msg: `亲，老师这边看您在直播间已经成功抢到了 1000元的学习优惠券和4重优惠福利.
但一直没有报名成功，系统这里有显示您这里卡在了跳转支付的位置
您这边是有遇到了什么问题吗~还是说不太会操作呀？`,
    })

    await sleep(5000)

    await MessageSender.sendById({
      chat_id: chatUser.id,
      user_id: getUserId(chatUser.id),
      ai_msg: '[系统班介绍]',
      send_msg: {
        type: IWecomMsgType.Link,
        summary: '每天45分钟，助你成事悦己，高频正觉',
        imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/2421741338713_.pic.jpg',
        title: '21天|冥想系统班',
        sourceUrl: 'https://h5.esnewcollege.com/pages/course/detail?sku=20240107008805&pid=665301&checkLogin=1'
      }
    })

    await sleep(3000)

    await MessageSender.sendById({
      chat_id: chatUser.id,
      user_id: getUserId(chatUser.id),
      ai_msg: '你可以先点开链接看下介绍，现在支付还是享受直播间的优惠的，支付完截图给我，我来帮您登记',
    })

    const userId = getUserId(chatUser.id)

    await HumanTransfer.transfer(chatUser.id, userId, HumanTransferType.HesitatePayment, 'onlyNotify')

    await DataService.saveChat(chatUser.id, userId)
  }

  public static async handleLiveStreamStatus(event: ILiveStreamEvent) {
    const chatId = await DataService.getChatIdByMoerId(event.viewerId)
    if (!chatId) {
      return
    }

    const currentTime = await DataService.getCurrentTime(chatId)
    if (!currentTime.is_course_week) {
      return
    }

    let isInLiveStreamPattern = ''
    let isInClass = ''
    let isNotified = ''
    switch (currentTime.day) {
      case 1:
        isInLiveStreamPattern = 'is_in_day1_class_live_stream'
        isInClass = 'is_in_day1_class'
        isNotified = 'is_day1_login_out_notified'
        break
      case 2:
        isInLiveStreamPattern = 'is_in_day2_class_live_stream'
        isInClass = 'is_in_day2_class'
        isNotified = 'is_day2_login_out_notified'
        break
      case 3:
        isInLiveStreamPattern = 'is_in_day3_class_live_stream'
        isInClass = 'is_in_day3_class'
        isNotified = 'is_day3_login_out_notified'
        break
      case 4:
        isInLiveStreamPattern = 'is_in_day4_class_live_stream'
        isInClass = 'is_in_day4_class'
        isNotified = 'is_day4_login_out_notified'
        break
      default:
        break
    }

    await ChatStatStoreManager.initState(chatId)

    if (event.interactType === 'login') {
      ChatStateStore.update(chatId, {
        state: {
          [isInLiveStreamPattern]: true,
          [isInClass]: true,
        },
      })

    } else if (event.interactType === 'diconnect') {
      ChatStateStore.update(chatId, {
        state: {
          [isInLiveStreamPattern]: false,
        },
      })

    }

    const userId = getUserId(chatId)

    await DataService.saveChat(chatId, userId)

    // 掉线通知
    // 掉线 90s 通知一次，只通知一次
    // 只通知，不转人工
    // 只在上课时间做校验
    if (await DataService.isWithinClassTime(currentTime) && !await DataService.isPaidSystemCourse(chatId)) {
      if (ChatStateStore.getFlags(chatId)[isInLiveStreamPattern] === false) {
        await FlowTask.schedule(
          FlowTaskType.LogOutNotification,
          chatId,
          90 * 1000,
          { userId, currentTime }
        )
      }
    }
  }

  public static async handleTanglangLiveEnterExit(event: ITanglangLiveEnterExitEvent) {
    const chatId = await DataService.getChatIdByMoerId(event.unionId)
    if (!chatId) {
      return
    }

    const currentTime = await DataService.getCurrentTime(chatId)
    if (!currentTime.is_course_week) {
      return
    }

    let isInLiveStreamPattern = ''
    let isInClass = ''
    let isNotified = ''
    let lastEnterLiveTime = ''
    switch (currentTime.day) {
      case 1:
        isInLiveStreamPattern = 'is_in_day1_class_live_stream'
        isInClass = 'is_in_day1_class'
        isNotified = 'is_day1_login_out_notified'
        lastEnterLiveTime = 'last_enter_live_time_day1'
        break
      case 2:
        isInLiveStreamPattern = 'is_in_day2_class_live_stream'
        isInClass = 'is_in_day2_class'
        isNotified = 'is_day2_login_out_notified'
        lastEnterLiveTime = 'last_enter_live_time_day2'
        break
      case 3:
        isInLiveStreamPattern = 'is_in_day3_class_live_stream'
        isInClass = 'is_in_day3_class'
        isNotified = 'is_day3_login_out_notified'
        lastEnterLiveTime = 'last_enter_live_time_day3'
        break
      case 4:
        isInLiveStreamPattern = 'is_in_day4_class_live_stream'
        isInClass = 'is_in_day4_class'
        isNotified = 'is_day4_login_out_notified'
        lastEnterLiveTime = 'last_enter_live_time_day4'
        break
      default:
        break
    }

    await ChatStatStoreManager.initState(chatId)

    if (event.actionType === 'ENTER_LIVE') {
      ChatStateStore.update(chatId, {
        state: {
          [isInLiveStreamPattern]: true,
          [isInClass]: true,
        },
        userSlots:{
          [lastEnterLiveTime]: new Date().getTime()
        }
      })
    } else if (event.actionType === 'LEAVE_LIVE') {
      ChatStateStore.update(chatId, {
        state: {
          [isInLiveStreamPattern]: false,
        },
        userSlots:{
          [lastEnterLiveTime]: undefined
        }
      })
    }

    const userId = getUserId(chatId)

    await DataService.saveChat(chatId, userId)

    // 掉线通知
    // 掉线 90s 通知一次，只通知一次
    // 只通知，不转人工
    // 只在上课时间做校验
    if (await DataService.isWithinClassTime(currentTime) && !await DataService.isPaidSystemCourse(chatId)) {
      if (ChatStateStore.getFlags(chatId)[isInLiveStreamPattern] === false) {
        await FlowTask.schedule(
          FlowTaskType.LogOutNotification,
          chatId,
          90 * 1000,
          { userId, currentTime, isNotified, isInLiveStreamPattern }
        )
      }
    }
  }

  public static handleUnknownEvent(event: IMoerEvent) {
    logger.debug('unknown moer event:', JSON.stringify(event))
  }

  private static async handleXiaoHongshuOrder(event: IXiaoHongshuOrderEvent) {
    const chatId = event.wxId
    const userId = getUserId(chatId)
    await ChatStatStoreManager.initState(chatId)

    // 小红书绑定
    if (event.course_type === 0) {
      // 绑定手机号， moerId
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: chatId
        },
        data: {
          moer_id: String(event.userId)
        }
      })

      ChatStateStore.update(chatId, {
        userSlots: {
          phoneNumber: event.mobile
        }
      })

      await DataService.saveChat(chatId, userId)
    } else if (event.course_type === 1) {
      // 发送小课堂
      const courseNo = event.stage as number // 课程期数

      const liveLink = await DataService.getCourseLinkByCourseNo(courseNo, 0)
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '课程激活啦！咱们课程是下周一开始哈~我把课程表发同学一下',
      })

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '[入门营课程表]',
        send_msg:{
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%e8%af%be%e7%a8%8b%e8%a1%a8.PNG'
        }
      })

      await sleep(2000)
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `✨【课前必做】很多同学刚来的时候都不知道冥想具体是什么，唐宁老师特别准备了10分钟小讲堂：
${liveLink}
（注意用下单手机号登录）      

看完后自动激活后续课程，班班会发入学礼哈🎁`,
      })

      await NewCourseUser.create(userId, chatId, courseNo, event.mobile, String(event.userId))
    }
  }

  private static async handleMergeUser(event: IMergeUserEvent) {
    // 更新 moerId, 手机号，课程期数
    const chat = await ChatDB.getByMoerId(event.oldUserId.toString(10))
    if (!chat) {
      return
    }

    let courseNo = chat.course_no
    const [error, user] = await catchError(MoerAPI.getUserByPhone(event.newMobile))
    if (user) {
      courseNo =  DataService.parseCourseNo(user)
    }

    await PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat.id
      },
      data: {
        moer_id: event.newUserId.toString(10),
        course_no: courseNo,
      }
    })

    ChatStateStore.update(chat.id, {
      userSlots: {
        phoneNumber: event.newMobile
      }
    })

    await DataService.saveChat(chat.id, getUserId(chat.id))
  }
}

export class LogOutNotification {

  static async notify(chatId: string, userId: string, currentTime: IScheduleTime) {
    if (!currentTime.is_course_week || currentTime.day > 4) {
      return
    }

    await ChatStatStoreManager.initState(chatId)
    const userSlotsPainPoints = await ContextBuilder.getCustomerPortrait(chatId)

    // 根据天数，返回对应信息
    const courseContentSummary = {
      1: '第一课：情绪减压，解析情绪和睡眠问题的成因和处理方式，再从手把手教冥想入门姿势。最带练对情绪和睡眠非常有帮助冥想《沉浸式秒睡》',
      2: '第二课：财富唤醒，聚焦【财富问题】，解读什么是富足和吸引力法则，帮助大家一步步找到自己负债、迷茫的内在原因。通过【财富果园】的冥想带练，帮助大家扫除对于物质、财富的内心阻碍，养成更加容易获取财富、打理财富的思维习惯',
      3: '第三课：红靴子是老师最重磅的课程，老师会带练【红靴子飞跃】冥想，主要聚焦于大家专注力、内在能量的提升。老师说如何一生只练一个冥想，就练习红靴子吧。这堂课结束的时候来时会和想精进学习的人说明21天的系统课内容',
      4: '第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍',
    }

    // 不同天数，返回不同的话术，避免重复
    const logOutNotificationPrefix = {
      1: '同学，这边看到你掉线了，有什么问题么?',
      2: 'Hi,  刚刚看到不在直播间了。需要帮助吗？',
      3: '同学，看到掉线了，是网络出问题了吗?',
      4: '同学咱这边掉线了，是有什么问题吗?',
    }

    if (StringHelper.isEmpty(userSlotsPainPoints)) {
      await this.sendDefaultNotify(chatId, userId, currentTime)
      return
    }

    const llmRes = await LLM.predict(`结合客户的情况和今天冥想课程的内容，提醒掉线的客户回到直播课程。
如果相关性不强，可以多推理几步，找一下间接原因，从而结合上客户的情况。

客户信息：
${userSlotsPainPoints}

课程内容：
${courseContentSummary[currentTime.day]}

例如：${logOutNotificationPrefix[currentTime.day]} 今天老师会针对你提到过的 xxx 讲 xxx，希望你听完呢

以“${logOutNotificationPrefix[currentTime.day]}”开始，“希望你听完呢”结束，简洁清晰。`)

    await MessageSender.sendById({
      chat_id: chatId,
      user_id: userId,
      ai_msg: llmRes
    })
  }

  private static async sendDefaultNotify(chatId: string, userId: string, currentTime: IScheduleTime) {
    const logOutNotification = {
      1: '同学，这边看到你掉线了，有什么问题么? 今天老师会针对你提到过的情绪波动和睡眠困扰，讲解情绪管理的方法，并带练一个对改善睡眠非常有效的冥想《沉浸式秒睡》，希望你听完呢。',
      2: 'Hi，刚刚看到不在直播间了。需要帮助吗？今天老师会通过【财富果园】的冥想带练，讲解如何扫除内心对于物质和财富的阻碍。这可能会帮助你更好地梳理关于负债和迷茫的问题，希望你听完呢！',
      3: '同学，看到掉线了，是网络出问题了吗？今天老师会带大家练【红靴子飞跃】冥想，这个课程可以帮助提升专注力和内在能量，我记得你之前提到希望提高工作时的专注状态，这节课非常适合你哦，希望你听完呢。',
      4: '同学咱这边掉线了，是有什么问题吗? 第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍',
    }
    await MessageSender.sendById({
      chat_id: chatId,
      user_id: userId,
      ai_msg: logOutNotification[currentTime.day]
    })
  }

  private static async addPreCourseFinishTask(chatId: string, userId: string) {
    const plusOneDay = DateHelper.add(new Date(), 1, 'day')
    plusOneDay.setHours(18, 50, 0, 0)


    await ScheduleTask.addTask(getGeneralSopKey(), TaskName.GroupSend,  plusOneDay, {
      chatId: chatId,
      userId: userId,
      name: TaskName.TPlusNTask,
      tag:'t_plus_1_18_50'
    })
  }
}