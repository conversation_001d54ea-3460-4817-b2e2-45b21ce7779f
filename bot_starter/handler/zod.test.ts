import { z } from 'zod'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { DataService } from '../../bot/service/moer/getter/getData'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const schema = z.object({
      imContactId: z.string(),
      name: z.string(),
      avatar: z.string().url(),
      createTimestamp: z.number(),
      imInfo: z.object({
        externalUserId: z.string(),
        followUser: z.object({
          wecomUserId: z.string()
        })
      }),
      botInfo: z.object({
        botId: z.string(),
        imBotId: z.string(),
        name: z.string(),
        avatar: z.string().url()
      })
    })

    const result = schema.safeParse({
      'imContactId': '7881302391322380',
      'name': 'god',
      'avatar': 'http://wx.qlogo.cn/mmhead/x1pGc6GEOmiaszu4DOwlFgo8H6dcHzFBkAvFOaISGicOMJswEmLvw5xyVDHlJ52CPibiaXcbS5Iu8Uk/0',
      'createTimestamp': 1728566046000,
      'imInfo': {
        'externalUserId': 'wmXvL2CQAAUBMPsAesyO8eT4YZmlGPvA',
        'followUser': {
          'wecomUserId': 'QiaoQiao'
        }
      },
      'botInfo': {
        'botId': '66fe82c9bfe92c45551f7722',
        'imBotId': '1688857003605938',
        'name': '冥想助教-乔乔老师',
        'avatar': 'https://wework.qpic.cn/wwpic3az/298583_QmZshdriSqC4C-N_1724146515/0'
      }
    })

    console.log(JSON.stringify(result, null, 4))
  }, 60000)

  it('测试下', async () => {
    const chatsWithMoerId = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        moer_id: {
          isSet: true
        },
        course_no: DataService.getCurrentWeekCourseNo()
      },
      select: {
        moer_id: true,
        id: true
      }
    })

    console.log(JSON.stringify(chatsWithMoerId, null, 4))
  }, 60000)
})