import { FileHelper } from '../../bot/lib/file'
import path from 'path'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { Config } from '../../bot/config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const contentJson = await FileHelper.readFile(path.join(__dirname, 'account.json'))
    const accountList = JSON.parse(contentJson)

    console.log(JSON.stringify(accountList, null, 4))
  })

  it('', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))
  }, 60000)

  it('', async () => {
    // anyScript
  }, 60000)
})