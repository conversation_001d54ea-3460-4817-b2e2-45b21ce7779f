// // 定时任务发布 服务
// import { <PERSON>ronJob } from 'cron'
// import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
//
// new CronJob(
//   '0 0 0 * * 1', // cronTime
//   incrementLessonNo, // onTick
//   null, // onComplete
//   true, // start
//   'Asia/Shanghai'  // 设定为中国时区
// )
//
// // 更新 课程期数
// async function incrementLessonNo() {
//   try {
//     const updatedLesson = await PrismaMongoClient.getInstance().moer_lesson.update({
//       where: { id: '66c456140a7fd946c9596c6c' },
//       data: {
//         lesson_no: {
//           increment: 1, // 使 lesson_no 字段加 1
//         },
//         updated_at: new Date(), // 更新 updated_at 时间
//       },
//     })
//     console.log('Updated lesson:', updatedLesson)
//   } catch (error) {
//     console.error('Error updating lesson_no:', error)
//   } finally {
//     await PrismaMongoClient.getInstance().$disconnect()
//   }
// }