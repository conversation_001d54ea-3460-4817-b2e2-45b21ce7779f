import { MessageReplyService } from '../bot/service/message/message_reply'
import { Config } from '../bot/config/config'
import { FlowTask } from '../bot/service/moer/components/schedule/silent_requestion'
import { sleep } from '../bot/lib/schedule/schedule'
import { UUID } from '../bot/lib/uuid/uuid'
import { AsyncLock } from '../bot/lib/lock/lock'
import { GroupSend } from '../bot/service/moer/components/flow/schedule/task/groupSend'
import { ITask } from '../bot/service/moer/components/schedule/type'

async function f() {
  const lock = new AsyncLock()
  await lock.acquire('fku', async () => {
    await sleep(3 * 1000)
    console.log('hi')
  }, { timeout: 2 * 1000 })
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('123123123123', async () => {
    const data = {
      'chatId': '7881300027049437_1688857949631398',
      'userId': '7881300027049437',
      'name': '是否到课',
      'scheduleTime': {
        'is_course_week': true,
        'day': 4,
        'time': '20:45:00'
      },
      'tag': '4th_day_check3',
      'sendTime': new Date('2025-05-08T12:45:00.000Z')
    }
    await new GroupSend().process(data as ITask)
  }, 60000)

  it('lock 123', async () => {
    await Promise.all([f(), f(), f()])

  }, 60000)

  it('123213', async () => {
    const lock = new AsyncLock()
    await lock.acquire('resource', async () => {
      //  新建一个任务 3s 后执行
      //  15 mins 后客户没有消息，进入到发送能量测评
      await FlowTask.schedule('123', async () => {
        await lock.acquire('resource', async () => {
          console.log('hi')
        })

      }, 3 * 1000)
    })

    await sleep(10 * 1000)
  }, 60000)


  it('test concurrent lock', async () => {
    const lock = new AsyncLock()
    const uuid = UUID.short()

    const promise1 = lock.acquire(uuid, async () => {
      console.log('First operation started')
      await sleep(4 * 1000)
      console.log('hi1')
      return 'result1'
    }, { timeout: 3 * 1000 })

    const promise2 = lock.acquire(uuid, async () => {
      console.log('Second operation started')
      await sleep(4 * 1000)
      console.log('hi2')
      return 'result2'
    }, { timeout: 5 * 1000 })

    // 等待两个操作都完成
    await Promise.all([promise1, promise2])

    // 应该看到的执行顺序是:
    // 1. First operation started
    // 2. hi1
    // 3. Second operation started
    // 4. hi2
  }, 1E8)

  it('should pass', async () => {
    // 创建 AsyncLock 实例
    const lock = new AsyncLock()

    // 共享资源
    let sharedResource = 0

    // 模拟异步操作
    function asyncTask() {
      return new Promise((resolve) => {
        const currentValue = sharedResource
        setTimeout(() => {
          sharedResource = currentValue + 1
          resolve(null)
        }, Math.random() * 100)
      })
    }

    // 没有锁的测试函数
    async function testWithoutLock() {
      sharedResource = 0 // 重置共享资源
      console.log('Test without lock:')

      // 创建多个并发任务
      const tasks = []
      for (let i = 0; i < 10; i++) {
        // @ts-ignore23123
        tasks.push(asyncTask())
      }

      await Promise.all(tasks)
      console.log('Final shared resource value without lock:', sharedResource)
    }

    // 使用锁的测试函数
    async function testWithLock() {
      sharedResource = 0 // 重置共享资源
      console.log('Test with lock:')

      // 创建多个并发任务
      const tasks = []
      for (let i = 0; i < 10; i++) {
        // @ts-ignore12321
        tasks.push(lock.acquire('resource', async () => {
          await asyncTask()
        }))
      }

      await Promise.all(tasks)
      console.log('Final shared resource value with lock:', sharedResource)
    }


    // 没有锁的测试
    await testWithoutLock()

    // 使用锁的测试
    await testWithLock()
  }, 60000)

  it('测试', async () => {
    const lock = new AsyncLock()
    Config.setting.localTest = true

    const userId = '123'
    const texts = ['你好', '我是流川枫']

    async function reply() {
      await lock.acquire(userId, async () => {
        return await MessageReplyService.reply(texts, userId)
      }, { timeout: 10 * 1000 * 2 })
    }

    const promises: Promise<any>[] = []
    for (let i = 0; i < 10; i++) {
      promises.push(reply())
    }

    await Promise.all(promises)
  }, 60000)
})