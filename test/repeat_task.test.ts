import { CheckPreCourseCompletionTask } from '../bot/service/moer/components/flow/schedule/task/checkPreCourseCompletion'
import { DataService } from '../bot/service/moer/getter/getData'
import { getUserId } from '../bot/config/chat_id'
import { sleep } from '../bot/lib/schedule/schedule'
import { Config } from '../bot/config/config'
import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'
import { ClassGroupTaskManager } from '../bot_starter/client/class_group'
import { MessageSender } from '../bot/service/moer/components/message/message_send'

interface CourseInfo {
    day: number
    is_recording?: boolean
}

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '1688858254705213',
      botUserId: 'ShengYueQing',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      notifyGroupId: 'R:10829337560927503',
      classGroupId: 'xx',
      courseNo: 1
    }

    Config.setting.localTest = false
  })



  it('小课堂任务', async () => {
    const userList = await DataService.getChatByWechatName('Horus')
    // @ts-ignore fk u
    // wx_id === 1688858254705213
    const chat =  userList[0] as any

    const chatId = chat._id

    DataService.isCompletedCourse = async (chat_id: string, course: CourseInfo) => {
      return true
    }

    const userId = getUserId(chatId)
    const tasks = await CheckPreCourseCompletionTask.getTask(chatId, userId)


    for (const task of tasks) {
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `开始执行任务：${task.scheduleTime?.time}`,
      })

      await new CheckPreCourseCompletionTask().process(task)
      await sleep(10000)
    }
  }, 60000)


  it('repeat', async () => {
    const queue = new Queue(`create_${ClassGroupTaskManager.getQueueName()}`, { connection:  RedisDB.getInstance() })
    const repeatableJobs = await queue.getRepeatableJobs()

    if (repeatableJobs.length === 0) {
      // 添加重复任务
      await queue.add(
        'weeklyStartGroupTask',
        { timestamp: Date.now() },
        {
          repeat: { pattern: '0 0 * * 6' },
          jobId: 'weeklyStartGroupTask', // 固定的 jobId 确保只有一个任务
        }
      )
    }

    // 停止任务
    async function stopJob() {
      const repeatableJobs = await queue.getRepeatableJobs()

      console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))
      for (const job of repeatableJobs) {
        await queue.removeRepeatableByKey(job.key)
      }

      await queue.close()

      console.log('任务已停止')
    }


    // await stopJob()

    console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))
  }, 60000)

  // it('', async () => {
  //
  // }, 30000)
})