import {PrismaMongoClient} from '../bot/model/mongodb/prisma'
import {ChatHistoryService} from "../bot/service/moer/components/chat_history/chat_history";


describe('Test', function () {
  beforeAll(() => {

  })

  it('聊天记录', async () => {
    const chat_id = '7881299533985687_1688856322643146'

    const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chat_id)
    // let start = false
    // for (let chatHistoryElement of chatHistory) {
    //   if (chatHistoryElement.content === '您加企业号就可以了哈~') {
    //     console.log(chatHistoryElement.created_at.toLocaleString())
    //     start = true
    //   }
    //
    //   if (start) {
    //     console.log(chatHistoryElement.content, chatHistoryElement.created_at.toLocaleString())
    //   }
    // }

    // let chatHistory = await PrismaMongoClient.getInstance().chat_history.findMany({
    //   where: {
    //     chat_id: chat_id
    //   },
    //   orderBy: {
    //     created_at: 'asc'
    //   },
    //   select: {
    //     chat_id: true,
    //     role: true,
    //     content: true,
    //   }
    // }) as IDBBaseMessage[]
    //
    // for (let idbBaseMessage of chatHistory) {
    //   if (idbBaseMessage.content === '您加企业号就可以了哈~') {
    //     console.log(idbBaseMessage)
    //   }
    // }
  }, 60000)

  it('', async () => {
    await PrismaMongoClient.getInstance().moer_lesson.create({
      data: {
        updated_at: new Date(), // 使用 faker 生成一个最近的日期
        lesson_no: 30, // 生成一个 1 到 100 的随机数作为期数
        start_at: new Date('2024-09-02 00:00:00'), // 2022-12-14 08:00:00
        lessons: [
          {
            'id': '649b7bf2c3a5f3b99a63c348',
            'name': 'Introduction to the Course',
            'index': 0,
            'address': 'Room 101, Building A',
            'is_recording': false
          },
          {
            'id': '649b7bf2c3a5f3b99a63c349',
            'name': 'Lesson 1: Basics of Topic (Live)',
            'index': 1,
            'address': 'Room 102, Building A',
            'is_recording': false
          },
          {
            'id': '649b7bf2c3a5f3b99a63c350',
            'name': 'Lesson 2: Deep Dive into Topic (Live)',
            'index': 2,
            'address': 'Room 103, Building B',
            'is_recording': false
          },
          {
            'id': '649b7bf2c3a5f3b99a63c351',
            'name': 'Lesson 3: Practical Applications (Live)',
            'index': 3,
            'address': 'Room 104, Building C',
            'is_recording': false
          },
          {
            'id': '649b7bf2c3a5f3b99a63c352',
            'name': 'Lesson 4: Final Review and Q&A (Live)',
            'index': 4,
            'address': 'Room 105, Building D',
            'is_recording': false
          },
          {
            'id': '649b7bf2c3a5f3b99a63c353',
            'name': 'Lesson 1: Basics of Topic (Replay)',
            'index': 1,
            'address': 'Room 102, Building A',
            'is_recording': true
          },
          {
            'id': '649b7bf2c3a5f3b99a63c354',
            'name': 'Lesson 2: Deep Dive into Topic (Replay)',
            'index': 2,
            'address': 'Room 103, Building B',
            'is_recording': true
          },
          {
            'id': '649b7bf2c3a5f3b99a63c355',
            'name': 'Lesson 3: Practical Applications (Replay)',
            'index': 3,
            'address': 'Room 104, Building C',
            'is_recording': true
          },
          {
            'id': '649b7bf2c3a5f3b99a63c356',
            'name': 'Lesson 4: Final Review and Q&A (Replay)',
            'index': 4,
            'address': 'Room 105, Building D',
            'is_recording': true
          }
        ],
      }
    })
  }, 60000)

  it('clean database', async () => {
    const result = await PrismaMongoClient.getInstance().chat.updateMany({
      data: {
        course_no: 0,
      },
    })

    console.log(`成功更新了 ${result.count} 条记录的 course_no 为 0。`)
    return result
  }, 60000)

})