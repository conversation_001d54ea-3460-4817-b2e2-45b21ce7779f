import logger from '../../bot/model/logger/logger'
import { MessageSender } from '../../bot/service/moer/components/message/message_send'
import { TaskName } from '../../bot/service/moer/components/flow/schedule/type'
import {
  CheckPreCourseCompletionTask
} from '../../bot/service/moer/components/flow/schedule/task/checkPreCourseCompletion'
import { GroupSend } from '../../bot/service/moer/components/flow/schedule/task/groupSend'
import { Config } from '../../bot/config/config'
import { DataService } from '../../bot/service/moer/getter/getData'
import { getUserId } from '../../bot/config/chat_id'
import { getTaskList } from '../../bot/service/moer/components/flow/schedule/task/getTaskList'
import { sleep } from '../../bot/lib/schedule/schedule'
import { ChatHistoryService } from '../../bot/service/moer/components/chat_history/chat_history'
import { ITask } from '../../bot/service/moer/components/schedule/type'


export async function handleTask(task: ITask) {
  if (!task.scheduleTime)  {
    logger.warn(JSON.stringify(task))
    return
  }

  await MessageSender.sendById({
    chat_id: task.chatId,
    user_id: task.userId,
    ai_msg: `${task.name} ${task.scheduleTime.is_course_week ? '上课周' : '非上课周'} Day${task.scheduleTime.day} ${task.scheduleTime.time}`
  })

  switch (task.name) {
    case TaskName.CheckPreCourseCompletion:
      await new CheckPreCourseCompletionTask().process(task)
      break
    default:
      await new GroupSend().process(task)
  }
}

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: 'QiaoQiao',
      notifyGroupId: 'R:10829337560927503',
      classGroupId: 'xx',
      courseNo: 42
    }
  })

  it('should pass', async () => {
    Config.setting.localTest = false

    const userList = await DataService.getChatByWechatName('麦子')
    // @ts-ignore fk u
    const user =   userList[0] as any

    const chatId = user._id
    const wxId = user.wx_id

    const userId = getUserId(chatId)

    console.log(chatId, userId, wxId)

    await ChatHistoryService.clearChatHistory(chatId)

    let taskList = await getTaskList(userId, chatId)
    taskList = taskList.filter((task) => {
      return task.scheduleTime?.is_course_week === true  && task.scheduleTime?.day === 1
    })

    for (const iTask of taskList) {
      await handleTask(iTask)
      await sleep(10 * 1000)
    }
  }, 1E8)
})