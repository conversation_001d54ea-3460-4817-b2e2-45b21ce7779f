import { DataService } from '../../../bot/service/moer/getter/getData'
import { ChatHistoryService, IDBBaseMessage } from '../../../bot/service/moer/components/chat_history/chat_history'
import { FileHelper } from '../../../bot/lib/file'
import { Config } from '../../../bot/config/config'
import { getChatId, getUserId } from '../../../bot/config/chat_id'
import { ChatStateStore, ChatStatStoreManager } from '../../../bot/service/moer/storage/chat_state_store'
import { ChatDB } from '../../../bot/service/moer/database/chat'
import { MemoryStore } from '../../../bot/service/moer/components/memory/memory_store'
import { IReceivedMessage, IReceivedMessageSource, IWecomReceivedMsgType } from '../../../bot/lib/juzi/type'
import { faker } from '@faker-js/faker'
import { UUID } from '../../../bot/lib/uuid/uuid'
import { loadConfigByAccountName } from '../../tools/load_config'
import { sleep } from '../../../bot/lib/schedule/schedule'
import { GlobalMessageHandlerService } from '../../../bot/service/message/message_merge'
import { when } from 'jest-when'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { taskTimeToCourseTime } from '../../../bot/service/moer/components/schedule/creat_schedule_task'
import { RandomHelper } from '../../../bot/lib/random/random'
import * as os from 'node:os'

import logger from '../../../bot/model/logger/logger'
import dayjs from 'dayjs'
import path from 'path'

/**
 * 聊天模拟配置接口
 */
export interface ChatSimulationConfig {
    /** 微信账号名称，默认 'syq' */
    accountName: string
    /** 机器人名称，默认 '麦子老师' */
    botName: string
    /** 课程编号，默认 65 */
    defaultCourseNo: number
    /** 消息发送间隔配置 */
    messageDelay: {
        /** 最小间隔（毫秒），默认 15000 */
        minInterval: number
        /** 超过最小间隔时的固定延迟（毫秒），默认 30000 */
        defaultInterval: number
    }
}

/**
 * 拉取消息配置选项
 */
export interface PullMessagesOptions {
  /** 课程编号列表 */
  courseNos: number[]
  /** 最小客户消息数，默认 10 */
  minUserMessageCount?: number
  /** 最大客户消息数，默认 50 */
  maxUserMessageCount?: number

  /** 限制选取的客户数量，如果设置则随机选取指定数量的客户 */
  userLimit?: number
}

/**
 * 聊天记录输入格式
 */
export interface ChatHistoryInput {
    /** 原始聊天ID或微信名称 */
    identifier: string
    /** 聊天消息列表 */
    messages: IDBBaseMessage[]
    /** 可选的课程编号 */
    courseNo?: number
}

/**
 * 聊天模拟器类
 * 用于模拟线上真实聊天场景进行测试
 */
export class ChatSimulator {
  private config: ChatSimulationConfig
  private initialized = false
  private getCurrentTimeMock: jest.SpyInstance | null = null

  constructor() {
    this.config = {
      accountName:  'syq',
      botName: '麦子老师',
      defaultCourseNo: 65,
      messageDelay: {
        minInterval: 40000,
        defaultInterval: 40000
      }
    }
  }

  /**
     * 初始化模拟环境
     */
  private async initialize() {
    if (this.initialized) return

    Config.setting.wechatConfig = await loadConfigByAccountName(this.config.accountName)
    Config.setting.startTime = Date.now()
    Config.setting.AGENT_NAME = this.config.botName

    GlobalMessageHandlerService.startWorker()

    // 设置时间模拟
    this.getCurrentTimeMock = jest.spyOn(DataService, 'getCurrentTime')

    this.initialized = true
  }

  /**
     * 运行单个聊天历史模拟
     */
  async runSingle(chatHistory: ChatHistoryInput): Promise<void> {
    await this.initialize()
    await this.processChatHistory(chatHistory)
  }

  /**
     * 并发运行多个聊天历史模拟
     */
  async runMultiple(chatHistories: ChatHistoryInput[]): Promise<void> {
    await this.initialize()

    await Promise.allSettled(chatHistories.map((history) => this.processChatHistory(history)))

    console.log(this.getWxNamePrefix())
  }

  /**
     * 从JSON文件加载聊天历史
     */
  async loadFromJSON(filePath: string): Promise<ChatHistoryInput[]> {
    const content = await FileHelper.readFile(filePath)
    const histories = JSON.parse(content)

    return histories.map((history: any, index: number) => {
      // 转换日期字符串为Date对象
      history.messages.forEach((msg: any) => {
        msg.created_at = new Date(msg.created_at)
      })

      return {
        identifier: history[0]?.chat_id || `chat_${index}`,
        messages: history.messages
      }
    })
  }


  /**
   * 拉取指定期数的聊天记录
   * @param options - 拉取配置选项
   * @returns 聊天历史输入数组
   */
  async pullFromCourseNos(options: PullMessagesOptions): Promise<ChatHistoryInput[]> {
    const {
      courseNos,
      minUserMessageCount = 10,
      maxUserMessageCount = 50,
      userLimit
    } = options

    const allChatHistories: ChatHistoryInput[] = []
    console.log(`开始拉取聊天记录，课程编号: ${courseNos.join(', ')}`)

    for (const courseNo of courseNos) {
      const chats = await DataService.getChatsByCourseNo(courseNo)
      console.log(`课程 ${courseNo} 共有 ${chats.length} 个聊天`)

      const qualifiedChats: ChatHistoryInput[] = []

      for (const chat of chats) {
        const chatId = chat.id

        try {
          const userMessageCount = await ChatHistoryService.getUserMessageCount(chatId)

          if (userMessageCount >= minUserMessageCount && userMessageCount <= maxUserMessageCount) {
            const messages = await ChatHistoryService.getChatHistoryByChatId(chatId, true)

            // 跳过空的聊天记录
            if (!messages || messages.length === 0) {
              continue
            }

            qualifiedChats.push({
              identifier: chat.contact?.wx_name || chatId,
              messages,
              courseNo: courseNo
            })

            console.log(`添加聊天记录: ${chatId}，客户消息数: ${userMessageCount}，客户名: ${chat.contact?.wx_name}`)
          }
        } catch (error) {
          console.error(`处理聊天 ${chatId} 时出错:`, error)
        }
      }

      console.log(`课程 ${courseNo} 符合条件的聊天数: ${qualifiedChats.length}`)
      allChatHistories.push(...qualifiedChats)
    }

    // 如果设置了客户数量限制，则随机选取
    let finalChatHistories = allChatHistories
    if (userLimit && userLimit > 0 && userLimit < allChatHistories.length) {
      console.log(`\n从 ${allChatHistories.length} 个聊天中随机选取 ${userLimit} 个...`)
      finalChatHistories = RandomHelper.randomSelect(allChatHistories, userLimit)

      // 打印被选中的客户
      console.log('被选中的客户:')
      finalChatHistories.forEach((chat, index) => {
        console.log(`  ${index + 1}. ${chat.identifier} (课程${chat.courseNo}，消息数: ${chat.messages.length})`)
      })
    }

    console.log(`\n最终返回 ${finalChatHistories.length} 个聊天记录`)
    return finalChatHistories
  }


  /**
     * 从数据库获取聊天历史（按微信名称）
     */
  async loadFromDatabase(wxNames: string[]): Promise<ChatHistoryInput[]> {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findMany({
      where: { contact: { is: { wx_name: { in: wxNames } } } }
    })

    const chatHistories: ChatHistoryInput[] = []
    for (const user of userInfo) {
      if (!user.course_no) {
        console.warn(`User ${user.contact.wx_name} has no course_no, skipping...`)
        continue
      }

      const messages = await ChatHistoryService.getChatHistoryByChatId(user.id)
      chatHistories.push({
        identifier: user.contact?.wx_name || user.id,
        messages,
        courseNo: user.course_no
      })
    }

    return chatHistories
  }

  /**
     * 清理测试数据
     */
  async cleanup(chatIds: string[]): Promise<void> {
    for (const chatId of chatIds) {
      await ChatHistoryService.clearChatHistory(chatId, false)
      ChatStateStore.clear(chatId)
      await MemoryStore.clearMemory(chatId)
      await PrismaMongoClient.getInstance().log_store.deleteMany({ where: { chat_id: chatId } })
    }
  }

  public getWxNamePrefix() {
    const machineId =  os.hostname() || process.pid.toString()
    return `local${dayjs().format('YYYYMMDD')}-${machineId}`
  }

  /**
     * 处理单个聊天历史
     */
  private async processChatHistory(chatHistory: ChatHistoryInput): Promise<void> {
    // TODO 模拟事件

    const messages = chatHistory.messages
    if (!messages || messages.length === 0) return

    // 获取相关ID
    const oldChatId = messages[0].chat_id
    const userId = getUserId(oldChatId)
    const chatId = getChatId(userId)

    // 获取原始聊天信息
    const chat = await ChatDB.getById(oldChatId)
    const oldWxName = chat?.contact?.wx_name || chatHistory.identifier

    console.log(`Processing chat - UserId: ${userId}, ChatId: ${chatId}, WxName: ${oldWxName}`)

    // 设置测试客户信息
    const wxName = `${this.getWxNamePrefix()}-${oldWxName}`
    const courseNo = chatHistory.courseNo ||
            (await ChatDB.getCourseNo(oldChatId)) ||
            this.config.defaultCourseNo

    // 初始化聊天
    await this.initializeChat(chatId, userId, wxName, courseNo)

    // 处理消息
    await this.processMessages(chatId, wxName, messages, oldChatId)
  }

  /**
     * 初始化聊天环境
     */
  private async initializeChat(chatId: string, userId: string, wxName: string, courseNo: number): Promise<void> {
    const existingChat = await ChatDB.getById(chatId)

    if (!existingChat) { // 新建 chat
      await ChatDB.create({
        id: chatId,
        round_ids: [],
        contact: {
          wx_id: userId,
          wx_name: wxName,
        },
        wx_id: Config.setting.wechatConfig?.id as string,
        created_at: new Date(),
        chat_state: ChatStateStore.get(chatId),
        course_no: courseNo,
        is_test: true
      })
    } else { // 重新利用同一个 chat, 进行重置
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: chatId
        },
        data: {
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: wxName,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: ChatStateStore.get(chatId),
          course_no: courseNo,
          is_test: true
        }
      })
    }

    await ChatHistoryService.clearChatHistory(chatId, false)
    await ChatDB.setHumanInvolvement(chatId, false)
    await MemoryStore.clearMemory(chatId)
    await PrismaMongoClient.getInstance().log_store.deleteMany({ where: { chat_id: chatId } })
    await ChatStatStoreManager.initState(chatId)
  }

  /**
   * 处理消息序列
  */
  private async processMessages(
    chatId: string,
    wxName: string,
    messages: IDBBaseMessage[],
    oldChatId: string
  ): Promise<void> {
    let isFirstMessage = false
    let lastSendTime = new Date().getTime()
    let lastUserMessage = ''

    for (const [index, message] of messages.entries()) {
      // 跳过助手消息
      if (message.role === 'assistant') continue

      // 处理第一条客户消息前的历史
      if (!isFirstMessage) {
        const preMessages = messages.slice(0, index)
        await ChatHistoryService.setChatHistory(
          chatId,
          preMessages.map((msg) => ({ role: msg.role, content: msg.content }))
        )
        isFirstMessage = true
        lastSendTime = message.created_at.getTime()
      } else {
        // 处理SOP消息
        await this.handleSOPMessages(chatId, messages, index)
      }

      // 模拟时间
      await this.mockCurrentTime(chatId, message, oldChatId)

      // 计算并执行延迟
      await this.executeMessageDelay(lastSendTime, message.created_at.getTime())
      lastSendTime = message.created_at.getTime()

      // 发送消息
      const wecomMessage = this.convertToWecomMessage(message, wxName)

      // 如果上条消息还未被回复，再多等待 5 s
      const lastMessage = await ChatHistoryService.getLastMessage(chatId)
      if (lastMessage.content === lastUserMessage) {
        await sleep(5 * 1000)
      }

      GlobalMessageHandlerService.addMessage(wecomMessage)
      lastUserMessage = message.content
    }
  }

  /**
     * 处理SOP消息
     */
  private async handleSOPMessages(chatId: string, messages: IDBBaseMessage[], index: number): Promise<void> {
    if (index - 1 >= 0 && messages[index - 1].short_description) {
      for (let i = index - 1; i >= 0; i--) {
        if (!messages[i].short_description) {
          const preMessages = messages.slice(i + 1, index)
          for (const preMessage of preMessages) {
            await ChatHistoryService.addBotMessage(
              chatId,
              preMessage.content,
              preMessage.short_description
            )
          }
          break
        }
      }
    }
  }

  /**
     * 模拟当前时间
     */
  private async mockCurrentTime(chatId: string, message: IDBBaseMessage, oldChatId: string): Promise<void> {
    const currentTime = await taskTimeToCourseTime(message.created_at, oldChatId)

    if (this.getCurrentTimeMock) {
      when(this.getCurrentTimeMock)
        .calledWith(chatId)
        .mockReturnValue(currentTime)

      logger.trace({ chat_id: chatId }, 'currentTime', JSON.stringify(currentTime, null, 4))
    }
  }

  /**
     * 执行消息延迟
     */
  private async executeMessageDelay(lastSendTime: number, currentMessageTime: number): Promise<boolean> {
    if (lastSendTime === currentMessageTime) {
      // 第一条消息，立即发送
      return true
    }

    const diff = currentMessageTime - lastSendTime
    const { minInterval, defaultInterval } = this.config.messageDelay

    if (diff < minInterval) {
      console.log(`Waiting ${diff / 1000} seconds...`)
      await sleep(diff)
    } else {
      console.log(`Waiting ${defaultInterval / 1000} seconds...`)
      await sleep(defaultInterval)
    }

    return diff < minInterval
  }

  /**
     * 转换消息格式
     */
  private convertToWecomMessage(message: IDBBaseMessage, wxName: string): IReceivedMessage {
    return {
      avatar: faker.image.url(),
      botId: UUID.short(),
      botUserId: Config.setting.wechatConfig!.botUserId,
      chatId: UUID.short(),
      contactName: wxName,
      contactType: 0,
      coworker: false,
      externalUserId: UUID.short(),
      imBotId: Config.setting.wechatConfig!.id,
      imContactId: getUserId(message.chat_id),
      token: 'e0d70927040a4efa92b79b7279ecb1c1',
      timestamp: new Date().getTime(),
      isSelf: false,
      messageId: UUID.short(),
      messageType: IWecomReceivedMsgType.Text,
      orgId: '661ce5985ed60835eb133f49',
      source: IReceivedMessageSource.MobilePush,
      payload: {
        text: message.content
      }
    }
  }

  async saveToFile(activeUserChats: ChatHistoryInput[], fileName: string) {
    const filePath = path.join(process.cwd(), 'dev', fileName)

    await FileHelper.writeFile(filePath, JSON.stringify(activeUserChats))

    return filePath
  }
}
