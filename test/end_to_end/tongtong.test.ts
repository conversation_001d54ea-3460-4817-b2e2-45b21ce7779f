import { ChatHistoryService } from '../../bot/service/moer/components/chat_history/chat_history'
import chalk from 'chalk'
import { UUID } from '../../bot/lib/uuid/uuid'
import { getChatId } from '../../bot/config/chat_id'
import { WorkFlow } from '../../bot/service/moer/components/flow/flow'
import { DataService } from '../../bot/service/moer/getter/getData'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const chat_ids = ['7881301484929790_1688855184697783']
    for (const chatId of chat_ids) {
      let chatHistory = await  ChatHistoryService.getChatHistoryByChatId(chatId)

      // 取前 20 条 消息
      chatHistory = chatHistory.slice(0, 20)
      console.log(chatHistory.map((message) =>  `${chalk.green(`${message.role   }:`)}${ message.content}`).join('\n\n'))

    }
  }, 60000)

  it('mock user1', async () => {
    // '7881301484929790_1688855184697783',
    const chat_ids = [ '7881299881929213_1688855739679096', '7881302386976855_1688855739679096', '7881301613927239_1688855548631328', '7881301304915445_1688856297674945']
    for (const chatId of chat_ids) {
      const userMessages = await  ChatHistoryService.getUserMessages(chatId)

      const user_id = UUID.short()
      const chat_id = getChatId(user_id)

      await ChatHistoryService.addBotMessage(chat_id, `亲爱的出境服务在线同学，您好呀。
    
✨欢迎参加墨尔冥想5天冥想入门营！我是你的贴心班班 【麦子老师】

之前也是从0开始和唐宁老师学冥想，之后会辅助您的冥想学习，咱们课程【下周一（4月7日）20:00】开始哈~

✨【课前必做】很多同学刚来的时候都不知道冥想具体是什么，唐宁老师特别准备了10分钟小讲堂：
https://t.meihao.com/3Kfi
（注意用下单手机号登录）      

看完后自动激活后续课程，班班会发入学礼哈🎁`)

      await ChatHistoryService.addBotMessage(chat_id, `唐宁老师很关注每一个同学的需求，所以让我们一定要收集下大家的情况，方便她下周一给大家针对性讲解，回复下，老师做好统计哈。

一.您目前的生活角色？：
1-职场奋斗者    2- 家庭管理者   3-退休精进者   4-修行者...

二.您的冥想经验值？：
 5-纯小白 - 6接触过 - 7有基础

三.最想点亮的人生议题：
   8- 情绪减压 |9-  专注提升 | 10- 睡眠改善...
   11- 财富能量 | 12 亲密关系 | 13 灵性成长`)

      function getRandomChoices() {
        const count = Math.floor(Math.random() * 3) + 1 // 随机选择 1 到 3 个数字
        const choices = new Set()
        while (choices.size < count) {
          choices.add(Math.floor(Math.random() * 13) + 1) // 1 到 13 之间的数字
        }
        return Array.from(choices)
      }

      await WorkFlow.step(chat_id, user_id, getRandomChoices().join(','))

      for (let i = 0; i < 5; i++) {
        await WorkFlow.step(chat_id, user_id, userMessages[i])
      }

    }
  }, 1E8)


  it('测试一下新的 调查问卷', async () => {
    const testCases = [
      '1-6-8',
      '3—6—10',
      '4-5-8910111213',
      '1，6,13',
      '1-5-11',
      '一-五-八',
      '1 6 8',
      '🙆🏻‍♀️.您目前的生活角色？\n1-职场奋斗者\n\n🐣.您的冥想经验值？\n 5-纯小白\n  \n🎯.最想点亮的人生议题\n   8- 情绪减压\n   9-  专注提升'
    ]

    DataService.isCompletedCourse = async (chat_id: string, course: any) => {
      return Math.random() < 0.5
    }

    for (const testCase of testCases) {
      const user_id = UUID.short()
      const chat_id = getChatId(user_id)

      await ChatHistoryService.addBotMessage(chat_id, `亲爱的出境服务在线同学，您好呀。
    
✨欢迎参加墨尔冥想5天冥想入门营！我是你的贴心班班 【麦子老师】

之前也是从0开始和唐宁老师学冥想，之后会辅助您的冥想学习，咱们课程【下周一（4月7日）20:00】开始哈~

assistant:✨【课前必做】很多同学刚来的时候都不知道冥想具体是什么，唐宁老师特别准备了10分钟小讲堂：
https://t.meihao.com/3Kfi
（注意用下单手机号登录）      

看完后自动激活后续课程，班班会发入学礼哈🎁`)

      await ChatHistoryService.addBotMessage(chat_id, `唐宁老师很关注每一个同学的需求，所以让我们一定要收集下大家的情况，方便她下周一给大家针对性讲解，回复下，老师做好统计哈。

一.您目前的生活角色？：
1-职场奋斗者    2- 家庭管理者   3-退休精进者   4-修行者...

二.您的冥想经验值？：
 5-纯小白 - 6接触过 - 7有基础

三.最想点亮的人生议题：
   8- 情绪减压 |9-  专注提升 | 10- 睡眠改善...
   11- 财富能量 | 12 亲密关系 | 13 灵性成长`)

      await WorkFlow.step(chat_id, user_id, testCase)
    }

  }, 60000)
})