import path from 'path'
import { ChatSimulator } from './helper/mock_real_conversation'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'

describe('Chat Simulation Tests', () => {
  let simulator: ChatSimulator

  beforeAll(() => {
    simulator = new ChatSimulator()
  })

  it('从JSON文件运行聊天模拟', async () => {
    // 方式1: 从JSON文件加载, 如果大批量重复测试，可以预先拉取，写入到 JSON 中作为缓存， 方便下次测试
    const chatHistories = await simulator.loadFromJSON(
      path.join(__dirname, 'chat_history/chat_history_20250520.json')
    )

    // 运行所有聊天历史
    await simulator.runMultiple(chatHistories)
  }, 1E8)

  it('从数据库运行指定客户的聊天模拟', async () => {
    // 方式2: 从数据库加载指定客户
    const wxNames = ['江枫渔火']
    const chatHistories = await simulator.loadFromDatabase(wxNames)

    // 运行所有聊天历史
    await simulator.runMultiple(chatHistories)
  }, 1E8)

  it('cleanup', async () => {
    const chatIds = ['local_7881303574913158']
    await simulator.cleanup(chatIds)
    await PrismaMongoClient.getInstance().chat.deleteMany({ where: { id: { in: chatIds } } })
  }, 9e8)


  it('从数据库拉取指定期数，对话轮数在范围内的 n 个客户', async () => {
    console.log(simulator.getWxNamePrefix())

    // 拉取（20-30条消息）的 10 个活跃客户 的聊天记录
    const activeUserChats = await simulator.pullFromCourseNos({
      courseNos: [53],
      minUserMessageCount: 20,
      maxUserMessageCount: 30,
      userLimit: 10
    })

    // await simulator.saveToFile(activeUserChats,  '53_active_user_chats.json')
    //

    // // 方式1: 从JSON文件加载, 如果大批量重复测试，可以预先拉取，写入到 JSON 中作为缓存， 方便下次测试
    // const chatHistories = await simulator.loadFromJSON(
    //   path.join(process.cwd(), 'dev', '53_active_user_chats.json')
    // )

    await simulator.runMultiple(activeUserChats)
  }, 1E8)
})