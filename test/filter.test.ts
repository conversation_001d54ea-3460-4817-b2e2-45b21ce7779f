import { DataService } from '../bot/service/moer/getter/getData'

describe('Test', function () {
  beforeAll(() => {

  })

  it('getCourseNo', async () => {
    console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(DataService.getNextWeekCourseNo()), null, 4))
  }, 60000)


  it('should pass', async () => {
    const regex = /^[A-Za-z0-9_\u4E00-\u9FFF\u3000-\u303F\uFF00-\uFFEF\u2000-\u206F\u1F600-\u1F64F\u1F300-\u1F5FF\u1F680-\u1F6FF\u2600-\u26FF\u2700-\u27BF \t\r\n]+$/

    // 测试字符串
    const testString = 'Hello你好！😊 123_测试'

    if (regex.test(testString)) {
      console.log('字符串通过过滤')
    } else {
      console.log('字符串包含不允许的字符')
    }


  }, 60000)
})