import { readFileSync } from 'node:fs'
import { <PERSON><PERSON><PERSON>el<PERSON> } from '../bot/model/rag/rag'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'

describe('experience rag', () => {
  jest.setTimeout(600000)
  it('create experience rag index', async() => {
    await RAGHelper.createRAG('experience_pain', ['tag'], 'pain')
    await RAGHelper.createRAG('experience_goal', ['tag', 'pain'], 'goal')
    await RAGHelper.createRAG('experience_message', ['tag', 'pain', 'goal', 'strategy'], 'message')
  })

  it('import experience', async() => {
    const x = readFileSync('./sales_exp.json', 'utf-8')
    const mongoClient = PrismaMongoClient.getInstance()
    const json = JSON.parse(x)
    for (const item of json) {
      const chatHistory = await mongoClient.chat_history.findFirst({ where:{ id:item.chatHistoryId } })
      const exp = {
        chat_id: item.chatId,
        round_id: chatHistory?.round_id ?? '',
        user_message: item.userMessage,
        strategy: item.suggestion,
        job: item.refinedSlot.job,
        created_at:new Date(),
        pain: item.refinedSlot.painPoints,
        goal: item.refinedSlot.goal,
        course_status: item.refinedSlot.courseStatus,
        course_feeling: item.refinedSlot.practiceExperience,
        is_handled: false,
        is_add: false
      }
      await mongoClient.experience.create({ data:exp })
    }
  })
})