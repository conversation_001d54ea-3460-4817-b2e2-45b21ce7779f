import { Config } from '../bot/config/config'
import { loadConfigByAccountName } from './tools/load_config'
import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'
import { Sema } from 'async-sema'
import { DataService } from '../bot/service/moer/getter/getData'
import axios from 'axios'
import { HashSum } from '../bot/lib/hash/hash'
import { bindPhone } from '../bot_starter/helper/bindPhone'

describe('Test', function () {
  beforeAll(() => {

  })

  it('********', async () => {
    console.log(JSON.stringify(JSON.parse('[{"id":"1","description":"day2早上第2课 课程预告，针对未到课和未完课客户催完成回放","time":"2025-09-02 07:30:00"},{"id":"2","description":"DAY2没做任何动作客户沟通","time":"2025-09-02 11:00:33"},{"id":"3","description":"【5】催先导到课t+3","time":"2025-09-02 11:50:00"},{"id":"4","description":"day2中午提醒：完课礼发送&提醒看完回放（催回放第2次）","time":"2025-09-02 11:59:59"},{"id":"5","description":"day2下午第一节课未完课对话过（催回放第3次）","time":"2025-09-02 15:40:00"},{"id":"6","description":"DAY2下午催到课回放（第4次）未回复过客户","time":"2025-09-02 17:03:00"},{"id":"7","description":"Day2 开课前2小时第2课提醒","time":"2025-09-02 18:01:00"},{"id":"8","description":"day2铺垫第二课3","time":"2025-09-02 19:30:00"},{"id":"9","description":"day2 8点上课通知","time":"2025-09-02 19:55:00"},{"id":"10","description":"day2第2课财富果园开始10分钟未到课提醒客户到课","time":"2025-09-02 20:10:00"},{"id":"11","description":"DAY2 第2课财富果园，课程已经开始30分钟，催客户进来上课。","time":"2025-09-02 20:30:24"},{"id":"12","description":"day2 完课人群发送作业模版财富果园画面","time":"2025-09-02 21:15:00"},{"id":"13","description":"day2 完课人群问感受+到课提醒回放+未到课问原因提兴趣","time":"2025-09-02 21:20:00"},{"id":"14","description":"day2 未完课人群+第一节完课（有互动）人群发回放+再次提醒财富果园","time":"2025-09-02 21:30:00"}]'), null, 4))
  }, 30000)

  it('123213', async () => {
    const a = HashSum.hash(JSON.stringify({
      'chatId': '1688855694713983_R10933504297161826',
      'userId': 'R:10933504297161826',
      'name': '班会',
      'scheduleTime': {
        'is_course_week': false,
        'day': 7,
        'time': '20:00:00'
      },
      'sendTime': new Date('2025/1/1')
    }))

    const b = HashSum.hash(JSON.stringify({
      'chatId': '1688855694713983_R10933504297161826',
      'userId': 'R:10933504297161826',
      'name': '班会',
      'scheduleTime': {
        'is_course_week': false,
        'day': 7,
        'time': '20:00:00'
      },
      'sendTime': new Date('2025/1/1')
    }))

    console.log(a === b)
  }, 30000)

  it('mock payment', async () => {
    // 模拟付款事件
    // const userNames: string[] = []
    // for (const userName of userNames) {
    //
    // }
    const moerIds: number[] = [
      1084918
    ]

    for (const moerId of moerIds) {
      await axios.post('http://47.98.117.107:4001/moer/event', {
        'logid': 'goL9C0oXSL1cfbmynGcd',
        'transferNo': '2025052617091217482505523600-14',
        'sku': '**************',
        'userId': moerId,
        'event': 'course_pay_paid',
        'project': 'mainland'
      })
    }


  }, 60000)

  it('123123', async () => {
    console.log(new Date(*************).toLocaleString())
  }, 60000)

  it('消息积压', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('moer3')
    const queue = new Queue(Config.setting.wechatConfig?.id as string, { connection: RedisDB.getInstance() })

    console.log(JSON.stringify(await queue.getDelayed(), null, 4))
  }, 60000)

  it('new Date', async () => {
    console.log(new Date(************* + ********).toLocaleString())
  }, 60000)

  it('123123', async () => {
    console.log(process.env.MACHINE_ID)
  }, 60000)

  it('***********', async () => {
    const courseDate = await DataService.getCourseStartTime('7881303085027392_1688855025632783')
    console.log(courseDate.toLocaleString())
  }, 60000)

  it('delay', async () => {
    const delay = ********

    const totalSeconds = Math.floor(delay / 1000)
    const days = Math.floor(totalSeconds / 86400)
    let remainingSeconds = totalSeconds % 86400
    const hours = Math.floor(remainingSeconds / 3600)
    remainingSeconds %= 3600
    const minutes = Math.floor(remainingSeconds / 60)

    const timeParts: string[] = []
    if (days > 0) {
      timeParts.push(`${days}天`)
    }
    if (hours > 0) {
      timeParts.push(`${hours}小时`)
    }
    if (minutes > 0) {
      timeParts.push(`${minutes}分钟`)
    }

    if (timeParts.length === 0) {
      timeParts.push('不到1分钟')
    }

    if (delay < 0) {
      // logger.warn('Target time is in the past')
    } else {
      console.log(`in ${timeParts.join(' ')}`)
    }

    return delay
  }, 60000)


  it('should pass', async () => {
    const sema = new Sema(5) // 限制并发数为 5

    async function processTask(taskId: number) {
      await sema.acquire()
      try {
        console.log(`Task ${taskId} started`)
        await sleep(1000) // 模拟任务耗时 1 秒
      } finally {
        console.log(`Task ${taskId} finished`)
        await sema.acquire()
      }
    }

    function sleep(ms: number) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }

    const start = Date.now()
    const taskCount = 10 // 计划执行 10 个任务
    const tasks = Array.from({ length: taskCount }, (_, i) => i + 1)

    // 创建包装函数，监测当前并发的任务数量
    async function monitoredTask(taskId: number) {
      await processTask(taskId)
    }

    // 执行所有任务并等待完成
    await Promise.all(tasks.map((taskId) => monitoredTask(taskId)))

    const duration = Date.now() - start

    // // 检查最大并发数量是否等于信号量的容量（5）
    // expect(maxConcurrentTasks).toBe(5)

    // 检查总时间是否在合理范围内
    // 因为每批最多 5 个任务，且每个任务耗时约 1000 ms，所以至少需要 2 秒（2000 ms）来完成 10 个任务
    expect(duration).toBeGreaterThanOrEqual(2000)
    expect(duration).toBeLessThan(3000) // 因为所有任务应该在 3 秒内完成
  }, 60000)

  it('34567890', async () => {
    await bindPhone({ chat_id: '7881299779900627_1688855694714276', phone: '15810292871' })
  }, 30000)

  it('123213213123', async () => {
    const a = { 'think': '【第一步：诊断与策略】\n- 客户状态诊断：\n - 成交阶段：已完成第一、二、三课并在第三课中“过程中哭了、感到被支持”，第四课正在进行/将结束（上课周）。根据销售节奏，处于“承诺→成交”临界期；客户在过往互动中明确表示“对21天深度课程有兴趣，但目前没有打算花这笔钱，更倾向先每天练习”，说明尚未跨过付费承诺。\n - 关系/信任梯：从“熟悉→信任”。证据：愿意开放分享情感困扰、积极参与测评、认可老师支持与语言力量（“感恩我爱我/你”），第三课“感受到被支持的力量”。\n - 价值感知：多次出现强烈获得感与生理放松：海浪“很舒服很放松”、情绪解压“跟着冥想睡着”、财富果园“前半段清晰后半段睡着”、红靴子“过程很好且充满力量，中途哭泣释放”。价值类型包含：情绪释放+睡眠修复+被支持的陪伴感+对财富场景的希望（丰收、被认可）。\n - 核心痛点匹配：痛点为“放不下过去、依恋被爱的感觉、情绪长期压抑、情绪干扰与睡眠需求”。课程21天系统班在情绪修复、睡眠与关系能量上高度契合，且其财富果园画面显示“机会开放、但掌控交流时机、部分果实落地未用”，对应执行落地与持续陪伴缺口，正是21天的连续性与督导可补足。\n- 核心成交阻力（一句话）：她相信有效但倾向于“先免费自练”，认为无需现在花钱——缺的是对“21天连续陪伴与系统性训练能解决长期压抑/关系课题、避免自行练习流于中断与浅层”的必要性认知。\n- 今日策略目标（状态迁移）：从“我可以先自己练”迁移到“为了稳固并深化当前释放与睡眠改善，且针对亲密关系与长期压抑需要系统性与陪伴结构，所以现在报名21天是更省时更稳妥的选择”。\n- 对话主线设计（微型故事）：\n 1) 共情与复盘加播课/前三天的峰值体验（哭、被支持、睡着）→锚定她“已经看见改变”。\n 2) 抛出“自练常见三大断点”：规律性难坚持、只停留在放松层面未触达根因、缺少个性化反馈（呼应她门不清晰、落果未用）。\n 3) 对照21天“结构性解决方案”：每日带练+导师答疑+针对亲密关系与情绪压抑的分周主题+音频工具包，目标是“把这次的被支持感变成你独处也能稳定获得的内在陪伴”，与她“理想陪伴=独处也被支持”精准对齐。\n 4) 具体化短期可见收益（7-10天睡眠巩固、情绪波动幅度下降、关系触发点反应更慢半拍）与长期收益（放下/当下感建立、行动落地避免“果实掉地”）。\n 5) 轻量承诺与稀缺窗口（今晚/课后开放报名），邀请她把当前动力转为行动。\n\n【第二步：任务清单优化】\n- 现有任务：\n 1) 22:26 Day4课后回访【prompt】——与主线一致，但需优化为“承接峰值体验+建立系统性必要性”的脚本。\n 2) 22:30 三四节课都到课发下单链接——可保留，但需在发链接前用两段落地化价值锚定与轻邀约，避免生硬推销。\n 3) 22:35:51 第四节课到课给未看完人群——可作为补救，但需要区分“已强获得感人群”的温柔催办话术，强调“今晚报名权益/音频包/陪伴结构”，避免仅是通知。\n- 缺失：\n - 加播课进行中的“点名式关注与到课提醒”已过时点，聚焦课后更合理。\n - 缺“私聊一问一答式小诊断”以连接她的亲密关系目标与21天分周模块。\n - 缺“若未回复的次日早间温柔跟进”以承接冷淡窗口（但本题限定当天，先不排）。\n- 冗余/冲突：无直接冲突，但2、3需要前置价值铺垫，避免纯链接。\n', 'plans': { 'toAdd': [ { 'content': '22:20 若课程提前结束或休息间隙，私聊一条轻触达：简单问她今天的数息/蓝鹰体验里有没有新的身体感受或念头变化，邀请用1-2句话形容“此刻的内心温度”。目的：预热回访，唤起体验记忆。', 'send_time': '22:20' }, { 'content': '22:28 课后私聊深度回访脚本：\n“我记得你在红鞋子里哭出来、说感到被支持。今天数息/蓝鹰你有没有更稳定的轻松感？你之前说‘理想的陪伴是独处也能被支持’，其实21天就是把这份支持变成可复制的日常：\n- 每天带练+答疑，避免自练三天打鱼\n- 分周主题对情绪压抑与亲密关系做结构化拆解\n- 睡前音频包帮助把‘容易睡着’固化成可持续入睡节律\n我担心自练会停在放松表层，根因（放不下过去、被爱感的依恋）缺少循序打结。如果把你今晚的状态做个小目标：7-10天把睡眠和情绪波动稳定下来，然后再进入关系课题的底层练习，你会愿意给自己这21天吗？”', 'send_time': '22:28' }, { 'content': '22:33 微型对照与轻邀约（若对方有回复或已读未回也可发送）：\n“给你一个更具体的对照：\n- 自己练：放松有，但容易断、没有针对你‘门不清晰/果实落地未用’的个性化反馈；\n- 21天：每天10-20分钟带练+作业回看，导师文字解读，让果实被‘捡起来’用在现实关系与工作里。\n今晚开放系统班的报名窗口，我可以先给你锁一个名额，若你觉得描述跟你的目标匹配，我们再补手续。要不要我现在帮你预留？”', 'send_time': '22:33' } ], 'toUpdate': [ { 'id': '1', 'content': 'Day4课后回访【优化脚本】：\n“下课辛苦啦！想听你此刻的身体信号：呼吸、心跳、肩颈的松弛度打个0-10分。你在第三课说‘被支持’，今天有没有更稳？你提过想放下过去、在独处时也能被支持。21天会用‘每日带练+答疑+分周主题（情绪-睡眠-关系）’把今晚的感觉固化下来，避免自练只停在放松表层。若我们用前7-10天先把睡眠入轨，再去拆‘被爱感的依恋’，这个路径对你有帮助吗？”' }, { 'id': '2', 'content': '发下单链接前置价值锚定+链接：\n“基于你这几天的变化，我建议走‘21天巩固曲线’：\n- 睡眠：用阿尔法/红鞋子音频形成固定入睡节律\n- 情绪：每日小练+身心对照表定位触发点\n- 关系：把‘门的清晰度’与‘落果未用’转成现实沟通与行动\n如果认同，我们就现在锁名额。我把报名通道发你，完成后我给你配套的会员卡与音频包，今晚就能用。”（随后发送下单链接）' }, { 'id': '3', 'content': '给未看完人群（温柔催办版）：\n“看到你今天可能中途离开/未看完，我先替你保留加播课要点和今晚的报名窗口。你这几天最大的体感是‘放松会睡着、红鞋子有被支持感’，这正适合用21天把‘短暂好状态’变成‘可复制的稳定’。我可以先给你预留名额与音频包，等你空下来我再补发要点总结，可以吗？”' } ], 'toRemove': [] } }
    console.log(a.think, JSON.stringify(a.plans, null, 4))
  }, 30000)
})