import { Client } from 'langsmith'
import { DataService } from '../bot/service/moer/getter/getData'
import dayjs from 'dayjs'
import { ChatDB } from '../bot/service/moer/database/chat'
import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'

describe('Test', function () {
  beforeAll(() => {

  })

  it('取消下现在到 下周一的每日任务', async () => {
    const chats = await DataService.getChatsByCourseNo(82)
    for (const chat of chats) {
      if (chat.wx_id !== '1688854340707859') {
        continue
      }

      // 删除对应的 job
      const courseStartTime = await DataService.getCourseStartTime(chat.id)
      const startOfCourseDay = dayjs(courseStartTime).startOf('day')
      const jobs = await new Queue('silent_reask_queue_1688854340707859', {
        connection: RedisDB.getInstance()
      }).getDelayed()

      for (const job of jobs) {
        if (job.data.task_name === '每日规划') {
          // waiting time

          if (job.delay + job.timestamp < startOfCourseDay.toDate().getTime()) {
            console.log(new Date(job.delay + job.timestamp).toLocaleString())
            await job.remove()
          }
        }
      }

    }


  }, 30000)

  it('123123', async () => {
    const send_time = dayjs(`${dayjs().format('YYYY-MM-DD')} 18:02`).format()
    console.log(send_time)
    console.log(new Date(send_time).toLocaleString())
  }, 30000)

  it('course', async () => {
    // const currentTime = await DataService.getCurrentTime('7881303260963350_1688856322643146')
    //
    // // 周一之后不发送完课礼了
    // if ((currentTime.is_course_week) && ((currentTime.day >= 2) || (currentTime.day === 1 && new Date().getHours() >= 19))) {
    //   console.log('hi')
    // }
    const courseStartTime = await DataService.getCourseStartTime('7881303260963350_1688856322643146')
    const formatCourseStartTime = dayjs(courseStartTime).format('M月D')

    console.log(formatCourseStartTime)
  }, 60000)

  it('should pass', async () => {
    const client = new Client()
    const key = 'promptName'
    const value = 'MergeSlotArray'

    const runs = client.listRuns({
      projectName: 'moer',
      filter: `and(eq(metadata_key, '${key}'), eq(metadata_value, '${value}'))`,
      error: false
    })

    // 利用 PromptTemplate 解析 输入参数
    for await (const run of runs) {
      if (run.name === 'StrOutputParser') continue

      if (run.name === 'AzureChatOpenAI') {
        const input = run.inputs.messages[0][0].kwargs.content
        const output = run.outputs?.generations[0][0].text
        console.log(input, output)
      }
    }
  }, 60000)

  it('123123111111', async () => {
    console.log(new Date('2025-09-03 19:55:00').toLocaleString())
    console.log(new Date('2025-09-03 19:55:00').getTime() - Date.now())
  }, 30000)
})