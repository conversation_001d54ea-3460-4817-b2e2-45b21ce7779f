import { FlowTask } from '../bot/service/moer/components/schedule/silent_requestion'
import { UUID } from '../bot/lib/uuid/uuid'
import { sleep } from '../bot/lib/schedule/schedule'
import logger from '../bot/model/logger/logger'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'

describe('FlowTask 测试', function () {
  let testChatId: string
  let taskExecutionResults: string[] = []

  beforeAll(async () => {
    // 启动 FlowTask worker
    FlowTask.startWorker()

    // 生成测试用的 chat_id
    testChatId = UUID.short()

    // 清空执行结果数组
    taskExecutionResults = []

    logger.log('FlowTask 测试环境初始化完成')
  })

  beforeEach(() => {
    // 每个测试前清空结果
    taskExecutionResults = []
  })

  it('应该能够注册和执行基本的测试任务', async () => {
    // 注册测试任务
    FlowTask.registerTask('test_task', async (chat_id: string, params) => {
      logger.log(`执行测试任务 for chat: ${chat_id}`, params)
      taskExecutionResults.push(`test_task_executed_${chat_id}`)
    })

    // 调度任务，1秒后执行
    await FlowTask.schedule(
      'test_task',
      testChatId,
      1000, // 1秒
      { testParam: 'test_value' },
      {
        auto_retry: false,
        independent: true
      }
    )

    // 等待任务执行
    await sleep(2000)

    // 验证任务是否执行
    expect(taskExecutionResults).toContain(`test_task_executed_${testChatId}`)
    expect(taskExecutionResults.length).toBe(1)
  }, 10000)

  it('应该能够处理带参数的任务', async () => {
    const testParams = {
      userId: 'test_user_123',
      message: '测试消息',
      timestamp: Date.now()
    }

    // 注册带参数的测试任务
    FlowTask.registerTask('test_task_with_params', async (chat_id: string, params) => {
      logger.log(`执行带参数的测试任务 for chat: ${chat_id}`, params)
      taskExecutionResults.push(`params_task_executed_${chat_id}_${params.userId}`)

      // 验证参数传递正确
      expect(params.userId).toBe(testParams.userId)
      expect(params.message).toBe(testParams.message)
      expect(params.timestamp).toBe(testParams.timestamp)
    })

    // 调度任务
    await FlowTask.schedule(
      'test_task_with_params',
      testChatId,
      500,
      testParams,
      {
        auto_retry: false,
        independent: true
      }
    )

    // 等待任务执行
    await sleep(1500)

    // 验证任务执行
    expect(taskExecutionResults).toContain(`params_task_executed_${testChatId}_${testParams.userId}`)
  }, 8000)

  it('应该能够测试任务的自动重试功能', async () => {
    let executionCount = 0

    // 注册会重试的测试任务
    FlowTask.registerTask('test_retry_task', async (chat_id: string, params) => {
      executionCount++
      logger.log(`执行重试测试任务 (第${executionCount}次) for chat: ${chat_id}`, params)
      taskExecutionResults.push(`retry_task_executed_${executionCount}`)
    })

    // 调度任务，启用自动重试
    await FlowTask.schedule(
      'test_retry_task',
      testChatId,
      500,
      { retryTest: true },
      {
        auto_retry: true,
        independent: true
      }
    )

    // 在任务执行前插入一条新消息，触发重试机制
    await sleep(200) // 等待一下，但不要等到任务执行

    // 模拟新消息到达，这会触发重试
    await PrismaMongoClient.getInstance().chat_history.create({
      data: {
        chat_id: testChatId,
        role: 'user',
        content: '触发重试的新消息',
        created_at: new Date(),
      }
    })

    // 等待足够长的时间让重试机制工作
    await sleep(6000) // 重试延迟是5分钟，但我们只等6秒来验证重试被调度了

    // 验证任务至少被调度了（可能还没执行重试）
    expect(executionCount).toBeGreaterThanOrEqual(0)
    logger.log(`重试测试完成，执行次数: ${executionCount}`)
  }, 15000)

  it('应该能够处理独立任务和非独立任务', async () => {
    // 注册两个测试任务
    FlowTask.registerTask('independent_task', async (chat_id: string) => {
      taskExecutionResults.push(`independent_task_${chat_id}`)

      console.log('hi1')
    })

    FlowTask.registerTask('non_independent_task', async (chat_id: string) => {
      taskExecutionResults.push(`non_independent_task_${chat_id}`)

      console.log('hi2')
    })

    // 先调度一个非独立任务
    await FlowTask.schedule(
      'non_independent_task',
      testChatId,
      2000,
      undefined,
      {
        auto_retry: false,
        independent: false
      }
    )

    // 再调度另一个非独立任务，应该会取消前一个
    await FlowTask.schedule(
      'independent_task',
      testChatId,
      1000,
      undefined,
      {
        auto_retry: false,
        independent: false
      }
    )

    // 等待任务执行
    await sleep(3000)

    // 验证只有后面的任务执行了
    expect(taskExecutionResults).toContain(`independent_task_${testChatId}`)
    expect(taskExecutionResults).not.toContain(`non_independent_task_${testChatId}`)
  }, 8000)

  it('应该能够处理任务注册错误', async () => {
    // 尝试调度一个未注册的任务
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

    await FlowTask.schedule(
      'unregistered_task',
      testChatId,
      1000,
      undefined,
      {
        auto_retry: false,
        independent: true
      }
    )

    // 等待一下
    await sleep(1500)

    // 验证没有任务执行
    expect(taskExecutionResults.filter((r) => r.includes('unregistered_task'))).toHaveLength(0)

    consoleSpy.mockRestore()
  }, 5000)

  it('应该能够获取队列信息', async () => {
    const queue = FlowTask.getQueue()
    expect(queue).toBeDefined()
    expect(queue.name).toContain('silent_reask_queue_')

    // 测试队列基本功能
    const waiting = await queue.getWaiting()
    const active = await queue.getActive()
    const completed = await queue.getCompleted()

    expect(Array.isArray(waiting)).toBe(true)
    expect(Array.isArray(active)).toBe(true)
    expect(Array.isArray(completed)).toBe(true)

    logger.log(`队列状态 - 等待: ${waiting.length}, 活跃: ${active.length}, 完成: ${completed.length}`)
  }, 5000)

  afterAll(async () => {
    // 清理测试数据
    try {
      await PrismaMongoClient.getInstance().chat_history.deleteMany({
        where: {
          chat_id: testChatId
        }
      })
    } catch (error) {
      logger.warn('清理测试数据时出错:', error)
    }

    logger.log('FlowTask 测试清理完成')
  })
})
