import { RAGHelper } from '../bot/model/rag/rag'
import { Document } from 'langchain/document'
import { AliyunCredentials } from '../bot/lib/cer'
import { FreeSpiritOss } from '../bot/model/oss/oss'
import path from 'path'
import { UUID } from '../bot/lib/uuid/uuid'

interface QA {
    q: string // 问题
    a: string // 答案
    chunk: string // 来源文本段
    doc: string // 文档tag，用于通过时间过滤

// 通用文档
//  '常规问题全局.xlsx',
// 课程前一周
//  '开营班会'
// day1, day2, day3
//  '第一天课程-情绪减压.docx'
//  '第二天课程-财富果园.docx'
//  '第三天课程-效能提升.docx'
// 销售期
//  '销售问题.xlsx',

//     // 公共文档
//     const commonDocs = [
//      '常规问题全局.xlsx',
//         '冥想问题.xlsx',
//     '系统班全通班逐字稿',
//     '课后问题回访FAQ.docx',
//     ]
//
//     // 进量期间补充文档
//     const additionalNotCourseWeekDocs = [
//         '开营班会',
//     ]
//
//     const additionalCourseDay3Doc = [
//         '常规问题周三八点前.xlsx',
//     ]
//
//     // 上课周，课程相关的文档，根据天数逐步增加
//     const courseDayDocs = {
//     day1: ['第一天课程-情绪减压.docx'],
//         day2: ['第二天课程-财富果园.docx'],
//         day3: ['第三天课程-效能提升.docx'],
// }
//
// // 销售相关的额外文档
// const saleAdditionalDocs = [
//     '销售文档',
//     '销售问题.xlsx',
// ]
//
// // 非课程周的文档列表
// const notCourseWeekDocs = [
//     ...commonDocs,
//     ...additionalCourseDay3Doc,
//     ...additionalNotCourseWeekDocs,
// ]
//
// // 课程周第一天的文档列表
// const courseWeekDay1Docs = [
//     ...commonDocs,
//     ...additionalCourseDay3Doc,
//     ...courseDayDocs.day1,
// ]
//
// // 课程周第二天的文档列表
// const courseWeekDay2Docs = [
//     ...commonDocs,
//     ...additionalCourseDay3Doc,
//     ...courseDayDocs.day1,
//     ...courseDayDocs.day2,
// ]
//
// // 课程周第三天的文档列表
// const courseWeekDay3Docs = [
//     ...commonDocs,
//     ...additionalCourseDay3Doc,
//     ...courseDayDocs.day1,
//     ...courseDayDocs.day2,
//     ...courseDayDocs.day3,
// ]
//
// // 销售相关的文档列表
// const saleRagDocList = [
//     ...commonDocs,
//     ...courseDayDocs.day1,
//     ...courseDayDocs.day2,
//     ...courseDayDocs.day3,
//     ...saleAdditionalDocs,
// ]
}

function getResourceName(extension: string, description: string): string {
  const shortUUId = UUID.short().slice(0, 4)

  return `${description}_${shortUUId}${extension}`
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('添加 文档', async () => {
    const toInsert: QA = {
      q: '',
      a: '',
      chunk: '', // 可以填空字符串
      doc: ''
    }

    // rag 测试 moer_rag_1
    // 线上服务 moer_rag_2

    // 插入文件
    await RAGHelper.addDocuments('moer_rag_1', [new Document({
      pageContent: toInsert.q,
      metadata: toInsert
    })])
  }, 60000)

  // 上传 oss
  it('上传 oss', async () => {
    AliyunCredentials.initialize({
      region: 'cn-hangzhou',
      accountId: '****************',
      accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
      secretAccessKey: '******************************',
    })

    const bucket = new FreeSpiritOss('static')

    const res = await bucket.upload(path.join(__dirname, 'local.ts')) // 本地文件
    // await bucket.putObjectStream()  // Buffer or stream
    console.log(JSON.stringify(res, null, 4))

    console.log(`https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/${  res.url}`)
  }, 60000)

  it('文件名 hash', async () => {
    const filename = 'local.ts'
    const resourceName = getResourceName(path.extname(filename), '文件描述')
    console.log(resourceName)
    console.log(`[${resourceName}]`)
  }, 60000)
})