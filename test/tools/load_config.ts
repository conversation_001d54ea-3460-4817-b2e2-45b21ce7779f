import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { DataService } from '../../bot/service/moer/getter/getData'
import { IWechatConfig } from '../../bot/config/interface'

interface IMoerEnterpriseConfig {
  notifyGroupId: string
  classGroupId: string
  isGroupOwner?: boolean
  proxyGroupNotify?: boolean
}

export async function loadConfigByAccountName(name: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: 'moer',
        accountName: name
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }
  const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
    notifyGroupId: enterpriseConfig.notifyGroupId,
    classGroupId: enterpriseConfig.classGroupId,
    isGroupOwner: enterpriseConfig.isGroupOwner,
    proxyGroupNotify: enterpriseConfig.proxyGroupNotify
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    classGroupId: account.classGroupId,
    courseNo: DataService.getCurrentWeekCourseNo(),
    isGroupOwner: account.isGroupOwner,
    proxyGroupNotify: account.proxyGroupNotify
  }
}


export async function loadConfigByWxId(id: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: 'moer',
        wechatId: id
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }
  const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
    notifyGroupId: enterpriseConfig.notifyGroupId,
    classGroupId: enterpriseConfig.classGroupId,
    isGroupOwner: enterpriseConfig.isGroupOwner,
    proxyGroupNotify: enterpriseConfig.proxyGroupNotify
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    classGroupId: account.classGroupId,
    courseNo: DataService.getCurrentWeekCourseNo(),
    isGroupOwner: account.isGroupOwner,
    proxyGroupNotify: account.proxyGroupNotify
  }
}