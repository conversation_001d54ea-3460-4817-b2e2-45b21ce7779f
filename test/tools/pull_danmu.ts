import { DataService } from '../../bot/service/moer/getter/getData'
import dayjs from 'dayjs'

/**
 * @return Promise<string>
 * @param course_no
 */
export async function pull_danmu_by_course_no(course_no: number) {
  // 根据当期客户拉取弹幕
  // 获取 期数对应的时间
  const currentCourseNo = DataService.getCurrentWeekCourseNo()
  // 获取 直播 ID
  const courseInfo = await DataService.getCourseInfoByCourseNo(currentCourseNo)

  // 解析出 startTime
  const startTime = courseInfo.startTime

  const relevantResources = courseInfo.resource.filter((resource) => resource.day >= 1 && resource.day <= 4)

  // 拉取周一到周四的课程弹幕
  // 遍历每个相关资源，计算对应的日期，并拉取弹幕
  for (const resource of relevantResources) {
    const liveId = resource.liveId

    if (!liveId) {
      console.warn(`资源 "${resource.resourceName}" 没有有效的 liveId，跳过弹幕拉取`)
      continue
    }

    // 计算对应的日期：startTime + day 天
    const resourceDate = dayjs(startTime).add(resource.day - 1, 'day').format('YYYY-MM-DD')

    // 调用 DataService 拉取弹幕并存储到数据库
    await DataService.pullLiveDanmuAndStore(
      liveId.toString(),      // liveId 转为字符串
      resourceDate,           // 开始日期
      resourceDate,            // 结束日期（同一天）
      course_no,
      resource.day// 期数
    )

    console.log(`成功拉取并存储 liveId: ${liveId} 日期: ${resourceDate} 的弹幕`)
  }


}

