import { FileHelper } from '../../bot/lib/file'
import path from 'path'

export class ReadCodeBase {
  public static async read(folderPath: string) {
    const files = await FileHelper.listFiles(folderPath)
    const fileCode = await Promise.all(files.map(async (file) => {
      if (await FileHelper.isFolder(file)) {
        return ''
      }

      const fileContent = await FileHelper.readFile(file)
      const fileName = path.basename(file)

      return `${fileName}
\`\`\`\n${fileContent}\n\`\`\``
    }))

    return fileCode.join('\n\n')
  }
}