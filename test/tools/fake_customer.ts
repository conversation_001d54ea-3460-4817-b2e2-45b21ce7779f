import OpenAI from 'openai'
import { ChatStateStore, ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { mockChatHistoryJsonArray } from './mock_chat_message'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { Config } from '../../bot/config/config'
import { MockData } from '../../bot/service/moer/mock/mock'
import { MessageSender } from '../../bot/service/moer/components/message/message_send'
import { FileHelper } from '../../bot/lib/file'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { DataService } from '../../bot/service/moer/getter/getData'

export interface FakeCustomerMessage {
  role: string
  content: string
  chatId: string
  wxName: string
  round_id?: string
}

export interface MultiSessionParam{
  chatIds: string[]
  sessionSize: number
  rounds: number
  chatHistoryWindowSize?: number
  writeToFile?: boolean
  resPath?: string
}

/**
 * 虚拟客户调用
 * 一个对象等于一个chat session，会自带聊天上下文
 */
export class FakeCustomer {
  chatId: string
  wxName: string
  chatHistory: FakeCustomerMessage[]
  fullHistory: FakeCustomerMessage[]
  chatHistoryWindowSize: number
  modelName: string
  client: OpenAI
  systemPrompt: string

  constructor(chatHistoryWindowSize: number) {
    this.chatHistory = []
    this.fullHistory = []
    this.chatHistoryWindowSize = chatHistoryWindowSize
  }

  public static async createSession(chatId: string, chatHistoryWindowSize: number = 10) {
    const fakeCustomer = new FakeCustomer(chatHistoryWindowSize)
    await fakeCustomer.initClient()
    await fakeCustomer.initSystemPrompt(chatId)
    fakeCustomer.initParam(chatId)
    return fakeCustomer
  }

  public async startSession(round: number, writeToFile: boolean = false, resPath?: string) {

    this.mockData()

    Config.setting.langsmith.projectName = 'evaluators'
    let salesMsg = `到此，我们全部直播课就结束了哦，之前看咱们课程和作业都认真完成了，不知道咱们这几天学习下来感觉怎么样？因为体验课时间有限，如果需要更细化学习，还是需要参加我们的21天系统班课程。在系统班中，我们会有更专业的指导和更完整的课程体系，更有利于帮助您养成良好的习惯，真正实现持久的改变和提升。
咱们现在遇到什么卡点了不，我们可以一起来看看怎么突破？距离咱们唐宁老师专属1000元优惠券过期还有最后2小时`
    let round_id = ''

    let currentRound = 0
    while (currentRound < round) {
      this.appendChatHistory(salesMsg, 'user', round_id)

      const reply = await this.reply()
      if (!reply)
        break

      const state = await MockData.getState(reply)
      state.chat_id = this.chatId
      round_id = state.round_id
      salesMsg = ''
      MessageSender.sendById = async (msg: any, options?: any) => {
        salesMsg += msg.ai_msg
      }
      // await SalesNode.invoke(state)

      console.info('sales message: ', salesMsg)

      currentRound += 1
    }

    this.appendChatHistory(salesMsg, 'user', round_id)

    const chatHistory = FakeCustomer.inverseRole(this.getFullHistory())

    if (writeToFile && resPath)
    {
      await FileHelper.writeFile(
        resPath,
        JSON.stringify(
          chatHistory,
          (key, value) => value === undefined ? '' : value // 将 undefined 替换为 ''
        )
      )
    }

    return chatHistory

  }

  public static async startMultiSession(param: MultiSessionParam) {
    if (!param.chatHistoryWindowSize)
    {
      param.chatHistoryWindowSize = 10
    }

    const sessionList : FakeCustomer[] = []
    for (let i = 0; i < param.sessionSize; i++) {
      const chatIdLength = param.chatIds.length
      const chatId = param.chatIds[i % chatIdLength]
      const session = await FakeCustomer.createSession(chatId, param.chatHistoryWindowSize)
      sessionList.push(session)
    }

    const results: FakeCustomerMessage[][] = []
    for (let i = 0; i < sessionList.length; i++) {
      results.push(await sessionList[i].startSession(param.rounds, param.writeToFile, param.resPath))
    }

    if (param.writeToFile && param.resPath)
      await FileHelper.writeFile(
        param.resPath,
        JSON.stringify(
          results,
          (key, value) => value === undefined ? '' : value // 将 undefined 替换为 ''
        )
      )

    return results

  }


  public async reply() {
    const chats = [
      {
        role: 'system',
        content: this.systemPrompt,
      },
      ...this.chatHistory,
    ]

    const response = await this.client.chat.completions.create({
      messages: chats as OpenAI.ChatCompletionMessageParam[],
      model: this.modelName,
    })

    const replyMessage = response.choices[0].message.content
    if (replyMessage) {
      this.appendChatHistory(replyMessage, 'assistant')
    }

    mockChatHistoryJsonArray(FakeCustomer.inverseRole(this.chatHistory))

    return replyMessage
  }

  public getFullHistory() {
    return this.fullHistory
  }

  private async initClient() {
    this.client = new OpenAI({
      apiKey: 'sk-free-spirit-baoshu-afjadH7CkZinpkiDjqfxT3BlbkFJGym4WWRVmLptQE0jsdTc',
      baseURL: 'http://47.74.9.5/v1'
    })
    // const response = await this.client.fineTuning.jobs.list()
    // const modelName = response.data[1].fine_tuned_model
    // if (modelName)
    // {
    //   this.modelName = modelName
    // }
    this.modelName = 'ft:gpt-4o-mini-2024-07-18:free:sales-client-chat-20250228:B5onTPZA'
  }

  private async initSystemPrompt(chatId: string) {
    const wxName = await FakeCustomer.getWxName(chatId)
    this.wxName = wxName
    const slotText:string = await this.getSlotText(chatId)
    this.systemPrompt = `你是${wxName}
${slotText}
你正在与一名冥想课销售麦子老师聊天，请继续`
  }

  private initParam(chatId: string)
  {
    this.chatId = chatId
  }

  private async getSlotText(chatId: string) {
    await ChatStatStoreManager.initState(chatId)
    const userSlots = ChatStateStore.get(chatId).userSlots
    const painPointText = userSlots.pain_points ? `痛点：${userSlots.pain_points.join('，')}\n` : ''
    const meditationExperienceText = userSlots.meditation_experience ? `经验：${userSlots.meditation_experience.join('，')}\n` : ''
    const goalsAndNeedsText = userSlots.goals_and_needs ? `需求：${userSlots.goals_and_needs.join('，')}\n` : ''
    const meditationGoal = userSlots.meditation_goal ? `目标：${userSlots.meditation_goal}\n` : ''
    const meditationPracticeExperience = userSlots.meditation_practice_experience ? `感受：${userSlots.meditation_practice_experience}\n` : ''
    return `${painPointText}${meditationExperienceText}${goalsAndNeedsText}${meditationGoal}${meditationPracticeExperience}`
  }

  private appendChatHistory(message: string, role: string, round_id?: string) {
    if (this.chatHistory.length >= this.chatHistoryWindowSize)
      this.chatHistory.shift()
    this.chatHistory.push({
      role:role,
      content: message,
      round_id: round_id,
      wxName: this.wxName,
      chatId: this.chatId
    })

    this.fullHistory.push({
      role:role,
      content: message,
      round_id: round_id,
      wxName: this.wxName,
      chatId: this.chatId
    })
  }

  private static async getWxName(chatId: string) {
    const userInfo = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        id: chatId,
      }
    })
    return userInfo[0].contact.wx_name
  }

  private static inverseRole(chatHistory: FakeCustomerMessage[]): FakeCustomerMessage[]
  {
    return chatHistory.map((message) => {
      if (message.role === 'user')
      {
        return {
          wxName: message.wxName,
          chatId: message.chatId,
          role: 'assistant',
          content: message.content,
          round_id: message.round_id,
        }
      }
      else
      {
        return {
          wxName: message.wxName,
          chatId: message.chatId,
          role: 'user',
          content: message.content,
          round_id: message.round_id,
        }
      }
    })
  }

  private mockData() {
    DataService.saveChat = async (chat_id: string, senderId: string) => {
    }

    ChatDB.isHumanInvolvement = async () => {
      return false
    }

    ChatStateStore.update(this.chatId, { state:
          {
            is_complete_payment: false,
            is_send_installment_video: false,
          }
    })

  }
}