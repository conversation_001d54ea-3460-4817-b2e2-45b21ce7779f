import { FreeSpiritOCR } from '../../bot/model/ocr/ocr'
import { JuziAPI } from '../../bot/lib/juzi/api'
import logger from '../../bot/model/logger/logger'

interface IAddWecomFriendParams {
    accountName: 'qiaoqiao' | 'tongtong' | 'moer3' |'moer4'
    imageUrl: string
    helloMessage?: string // 添加好友欢迎语
}

export async function addWecomFriend(param: IAddWecomFriendParams) {
  const { accountName, imageUrl, helloMessage = '我是墨尔冥想助教' } = param

  // 解析手机号
  function parsePhoneNumber(text: string): string[] {
    const phoneRegex = /(?:\+86|86)?1[3-9]\d{9}/g
    const phoneNumbers = text.match(phoneRegex)

    return phoneNumbers ? phoneNumbers : []
  }

  const accountMap = {
    qiaoqiao: '****************',
    tongtong: '****************',
    moer3: '****************',
    moer4: '****************',
  }


  const ocrResults = await FreeSpiritOCR.recognizeUrl(imageUrl)
  const text = ocrResults.map((item) => item.text).join('\n')

  const phoneNumbers = Array.from(new Set(parsePhoneNumber(text)))
  console.log(JSON.stringify(phoneNumbers, null, 4))
  console.log(phoneNumbers.join('\n'))

  for (const phoneNumber of phoneNumbers) {
    try {
      await JuziAPI.addFriendByPhone({
        imBotId: accountMap[accountName],
        phone: phoneNumber,
        hello: helloMessage,
      })

      logger.trace(phoneNumber, '添加成功')
    } catch (error) {
      logger.error(phoneNumber, '添加失败', error)
    }
  }

}