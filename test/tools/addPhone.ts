import { <PERSON>r<PERSON><PERSON> } from '../../bot/model/moer_api/moer'
import { DataService } from '../../bot/service/moer/getter/getData'
import { clearTasks } from '../../bot/service/moer/components/flow/schedule/task_starter'
import { Config } from '../../bot/config/config'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { ObjectUtil } from '../../bot/lib/object'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { IWechatConfig } from '../../bot/config/interface'
import { ChatStatStoreManager } from '../../bot/service/moer/storage/chat_state_store'
import { NewCourseUser } from '../../bot/service/moer/components/flow/helper/newCourseUser'
import logger from '../../bot/model/logger/logger'
import { catchError } from '../../bot/lib/error/catchError'


export function getConfigByWxId(wxId: string) {
  const wechatAccountMapping = {
    '****************': {
      'id': '****************',
      'name': 'qiaoqiao',
      'botUserId': 'QiaoQiao',
      'notifyGroupId': 'R:*****************',
      'classGroupId': 'R:*****************',
      'courseNo': 47,
      'isGroupOwner': true
    },
    '****************': {
      'id': '****************',
      'name': 'tongtong',
      'botUserId': 'MaiZi',
      'notifyGroupId': 'R:*****************',
      'classGroupId': 'R:*****************',
      'courseNo': 47,
      'isGroupOwner': true
    },
    '****************': {
      'id': '****************',
      'name': 'moer3',
      'botUserId': 'Aitest',
      'notifyGroupId': 'R:*****************',
      'classGroupId': 'R:*****************',
      'courseNo': 47,
    },
    '****************': {
      'id': '****************',
      'name': 'moer4',
      'botUserId': 'AITEST1',
      'notifyGroupId': 'R:*****************',
      'classGroupId': 'R:*****************',
      'courseNo': 47,
    },
  }

  return wechatAccountMapping[wxId] as IWechatConfig
}

export async function getAllUnBindPhoneUsers() {
  const unboundPhoneEvents = await PrismaMongoClient.getInstance().event_track.findRaw({
    filter: {
      'meta.reason': 'NotBindPhone',
    }
  }) as unknown as any[]

  const unboundUsers: any[] = []

  // 查找未绑定 moer ID 的客户
  for (const unboundEvent of unboundPhoneEvents) {
    const chatRecord = await ChatDB.getById(unboundEvent.chat_id)

    if (chatRecord && !chatRecord.moer_id) {
      if (chatRecord.contact?.wx_name) {
        const wechatAccountMapping = {
          // '****************': 'qiaoqiao',
          // '****************': 'tongtong',
          // '****************': 'moer3',
          // '****************': 'moer4',
          '****************': 'moer5',
          '****************': 'moer6',
          '****************': 'moer7',
        }


        if (['****************', '****************', '****************', '****************'].includes(chatRecord.contact.wx_name)) {
          continue
        }

        unboundUsers.push({
          [chatRecord.contact.wx_name]: `${wechatAccountMapping[chatRecord.wx_id]} ${  chatRecord.created_at?.toLocaleString()}`
        })
      } else {
        console.log(JSON.stringify(chatRecord, null, 4))
      }
    }
  }

  console.log(JSON.stringify(unboundUsers, null, 4))
  return unboundUsers
}

export async function addPhoneToUser(mobiles: Record<string, string>) {
  Config.setting.wechatConfig = {
    orgToken: '',
    id: '****************',
    botUserId: 'MaiZi',
    name: '麦子老师',
    notifyGroupId: 'R:*****************',
    classGroupId: 'R:*****************',
    courseNo: 38
  }

  // 拉出当前期所有客户
  // 向消息队列中添加一个任务

  // 拉当前账号，所有客户
  const chats = await PrismaMongoClient.getInstance().chat.findMany({
    where: {
      created_at: {
        gte: new Date('2025-01-04')
      }
    }
  })

  const names = ObjectUtil.keys(mobiles) as string[]
  // 将 names 转换为 Set 以提高查找效率
  const namesSet = new Set(names)

  // 创建一个 Map 来存储每个名字对应的最后一个 chat
  const chatMap = new Map<string, any>() // 请将 ChatType 替换为实际的 chat 类型

  for (let i = 0; i < chats.length; i++) {
    const chat = chats[i]
    const name = chat.contact.wx_name
    if (namesSet.has(name)) {
      chatMap.set(name, chat) // 每次遇到相同名字时，会覆盖之前的值，保留最后一个
    }
  }

  // 从 Map 中获取过滤后的 chats
  const filteredChats = Array.from(chatMap.values())

  const promises: Promise<any>[] = []
  for (const chat of filteredChats) {
    await updateInfo(chat, mobiles[chat.contact.wx_name])
  }

  await Promise.all(promises)


  async function updateInfo(chat, mobile: string) {
    if (!names.includes(chat.contact.wx_name)) {
      return
    }

    // @ts-ignore fk u
    Config.setting.wechatConfig.id = chat.wx_id
    const externalId = await JuziAPI.wxIdToExternalUserId(chat.contact.wx_id)
    if (!externalId) {
      logger.error('找不到联系人', chat.contact.wx_name)
      return
    }

    // eslint-disable-next-line prefer-const
    let [error, data] = await catchError(MoerAPI.getUserPhone({ externalUserId: externalId }))
    if (mobiles[chat.contact.wx_name]) {
      data = {
        mobile: mobiles[chat.contact.wx_name],
        contact_id: '',
        name: '',
        avatar: ''
      }
    }

    if (error || !data || !data.mobile) {
      return
    }

    if (data.mobile === '19119281868') {
      return ''
    }

    const phone = mobile
    const moerUser = await MoerAPI.getUserByPhone(phone)
    const courseNo = DataService.parseCourseNo(moerUser)
    await clearTasks(chat.id)

    await ChatStatStoreManager.initState(chat.id) // 防止状态丢失

    // await startTasks(getUserId(chat.id), chat.id) // 创建任务
    await NewCourseUser.create(chat.contact.wx_id, chat.id, courseNo, phone, moerUser.id.toString())

    console.log(chat.contact.wx_name, phone)
  }
}