import { Queue } from 'bullmq'
import { RedisDB } from '../../../bot/model/redis/redis'
import { DataService } from '../../../bot/service/moer/getter/getData'
import { TaskName } from '../../../bot/service/moer/components/flow/schedule/type'
import { ITask } from '../../../bot/service/moer/components/schedule/type'
import { getUserId } from '../../../bot/config/chat_id'
import { calTaskTime, IScheduleTime } from '../../../bot/service/moer/components/schedule/creat_schedule_task'
import { ScheduleTask } from '../../../bot/service/moer/components/schedule/schedule'
import { CacheDecorator } from '../../../bot/lib/cache/cache'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const queue = new Queue('1688856383589355_1688857003605938', {
      connection: RedisDB.getInstance()
    })

    console.log(await queue.count())
    // await clearTasks('1688856383589355_1688857003605938')
    // await removeTasks('1688856383589355_1688857003605938')
  }, 60000)

  it('', async () => {
    // 把上一期客户的， TaskName.UnPaidRemind2 删了，添加 TaskName.UnPaidRemind3
    // 获取上期客户的 Task
    const chats = await DataService.getChatsByCourseNo(62)

    DataService.getCourseStartTime = CacheDecorator.decorateAsync(DataService.getCourseStartTime)
    DataService.getCourseInfoByCourseNo = CacheDecorator.decorateAsync(DataService.getCourseInfoByCourseNo)

    for (const chat of chats) {
      const queue =  new Queue(chat.id, {
        connection: RedisDB.getInstance()
      })

      const delayedTask = await queue.getDelayed()

      const task = delayedTask.find((job) => job.name === TaskName.UnPaidRemind2)

      if (!task) {
        console.log(chat.id, '未找到任务')
        continue
      }

      await queue.obliterate({ force: true })

      // 塞入一个 Task
      const newTask:ITask = {
        chatId: chat.id,
        userId: getUserId(chat.id),
        name: TaskName.UnPaidRemind3,
        scheduleTime: {
          post_course_week: 1,
          day: 3,
          time: '19:50:00'
        }
      }

      newTask.sendTime = await calTaskTime(newTask.scheduleTime as IScheduleTime, chat.id)

      await queue.addBulk([ ScheduleTask.taskToJob(newTask)])
    }


  }, 1E8)
})