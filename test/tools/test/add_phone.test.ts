import { addPhoneToUser, getAllUnBindPhoneUsers, getConfigByWxId } from '../addPhone'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../bot/model/redis/redis'
import { Config } from '../../../bot/config/config'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { DataService } from '../../../bot/service/moer/getter/getData'
import { ObjectUtil } from '../../../bot/lib/object'
import { getUserId } from '../../../bot/config/chat_id'
import { TaskName } from '../../../bot/service/moer/components/flow/schedule/type'
import {
  addPreCourseCheckTask,
  SendWelcomeMessage
} from '../../../bot/service/moer/components/flow/schedule/task/sendWelcomeMessage'
import { ChatStateStore } from '../../../bot/service/moer/storage/chat_state_store'
import { JuziAPI } from '../../../bot/lib/juzi/api'
import { startTasks } from '../../../bot/service/moer/components/flow/schedule/task_starter'
import { loadConfigByWxId } from '../load_config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('走一遍发送欢迎语的流程', async () => {
    Config.setting.localTest = false

    const idMap =  {
      '老何': '7881301579950404_1688857949631398'
      // '尹': '7881299766914411_1688857003605938',
      // '建花': '7881303355936429_1688857003605938',
      // '林间小筑': '7881300570283212_1688857003605938',
      // '王鸣伟': '7881302964934109_1688857003605938',
      // '隆哥': '7881299670913197_1688857003605938',
      // '小火苗': '7881302981089413_1688857003605938',
      // '妙顺': '7881300116121888_1688857003605938',
      // '梅梅': '7881301422936085_1688857003605938',
      // '张以菊': '7881301642924152_1688857949631398',
      // 'llz': '7881300072926089_1688857949631398',
      // '周静琪': '7881302692944168_1688857949631398',
      // '小语': '7881303508937066_1688857949631398',
      // '李湘红17267428413': '7881300343915002_1688857949631398',
      // '美宸': '7881301502313662_1688857949631398',
      // 'liuxing': '7881301016297285_1688854822630695',
      // 'liu': '7881303500917033_1688854822630695',
      // '清禾': '7881303609014719_1688854822630695',
      // '耿二碗团一高俊彪': '7881300630987125_1688854822630695',
      // '岩君老师-美好时光品牌营销': '7881303483908637_1688854822630695',
      // '小水滴': '7881302295933073_1688856322643146',
      // '风和日丽🌺': '7881302416020360_1688856322643146',
      // '雨中飘荡的回忆': '7881300592259117_1688856322643146',
      // 'LJX': '7881301907062694_1688856322643146',
      // '谢涛1968': '7881301067935684_1688856322643146',
      // '胥爱云：金牌月嫂催乳发汗小儿推拿': '7881299747992225_1688856322643146'
    }

    // 很多人，都没建库

    for (const name of ObjectUtil.keys(idMap)) {
      const chat_id = idMap[name]
      const user_id = getUserId(chat_id)
      const wx_id = chat_id.split('_')[1]

      // if (chat.moer_id) { // 绑定上了继续
      //   continue
      // }

      // 加载配置
      Config.setting.wechatConfig =  getConfigByWxId(wx_id)

      ChatStateStore.update(chat_id, {
        state: {
          is_friend_accepted: true
        }
      })

      await new SendWelcomeMessage().process({
        userId: user_id,
        chatId: chat_id,
        name: TaskName.SendWelcomeMessage
      })

      await DataService.saveChat(chat_id, user_id)

      console.log('finish', name)
    }

    // 没有绑定 墨尔 Id 的话，走流程
  }, 1E8)

  it('获取客户信息', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('1688857949631398', '7881300846030208'), null, 4))
  }, 60000)

  it('获取未绑定手机号的客户', async () => {
    await getAllUnBindPhoneUsers()
  }, 60000)

  it('测试手机号绑定', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688857949631398')

    // await bindPhone({
    //   chat_id: '7881302637928531_1688857949631398',
    //   phone: '16601310307'
    // })

    await addPreCourseCheckTask('7881302637928531_1688857949631398', '7881302637928531')

    if (Config.setting.localTest || Config.setting.eventForward) {
      await startTasks('7881302637928531', '7881302637928531_1688857949631398')
    }
  },  1E8)

  it('补充手机号信息', async () => {
    Config.setting.localTest = false
    Config.setting.bindingPhone = true

    const contact = {
      // '优淘': '15214373193',
      // '希涵': '18911760366',
      // '抗衰老逆龄达人马秋莲': '13639483160',
      // 'sky': '13972763157'
      // 'Water': '17868678587',
      // '静待花开': '18751826861'
    }

    await addPhoneToUser(contact)
  }, 1E8)

  it('检查任务是否被添加', async () => {
    const queue = new Queue('7881302523960164_1688855025632783', {
      connection: RedisDB.getInstance()
    })

    await startTasks('7881302523960164', '7881302523960164_1688855025632783') // 创建任务

    console.log(JSON.stringify(await queue.getJobs()), null, 4)
  }, 60000)

  it('批量更新标签', async () => {
    // 补充 48期进量的标签
    const id = '1688857949631398'
    const courseNo = 47

    Config.setting.wechatConfig =   {
      id,
      botUserId: 'MaiZi',
      name: '暴叔',
      notifyGroupId: 'R:10735753744477170',
      classGroupId: 'x',
      courseNo: 1
    }

    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: courseNo,
        wx_id: id
      }
    })

    await Promise.all(chats.map(async (chat) => {
      const userId = chat.contact.wx_id

      if (Config.isInternalMember(userId)) {
        return
      }

      console.log(chat.contact.wx_name, `${courseNo}期`)
      await DataService.updateTags(userId, '48期')
    }))


  }, 60000)
})