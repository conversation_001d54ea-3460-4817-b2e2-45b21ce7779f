import { addWecomFriend } from '../add_helper'


describe('Test', function () {
  beforeAll(() => {

  })

  it('time', async () => {
    const timestamp = *************
    const date = new Date(timestamp)
    console.log(date.toLocaleString())
  }, 60000)

  it('图片 OCR -> 自动加企微', async () => {
    await addWecomFriend({ accountName: 'qiaoqiao', imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/11735215154_.pic.jpg' })
  }, 60000)

  it('', async () => {
    const ps =   [
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********'
    ]

    console.log(ps.join('\n'))
  }, 60000)
})