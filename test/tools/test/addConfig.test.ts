import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { JuziAPI } from '../../../bot/lib/juzi/api'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const configs = [
      // 企业账号是可以重叠的
      {
        'enterpriseName': 'moer',
        'accountName': 'moer5',
        'wechatId': '****************',
        'address': 'http://***************:4006',
        'port': '4006',
        'botUserId': 'Chen',
        'orgToken': '',
        'enterpriseConfig': {
          'notifyGroupId': 'R:*****************',
          'classGroupId': 'R:*****************',
          'isGroupOwner': true
        }
      },
      // {
      //   'enterpriseName': 'moer',
      //   'accountName': 'moer6',
      //   'wechatId': '****************',
      //   'address': 'http://***************:4007',
      //   'port': '4007',
      //   'botUserId': '101',
      //   'orgToken': '',
      //   'enterpriseConfig': {
      //     'notifyGroupId': 'R:*****************',
      //     'classGroupId': 'R:*****************',
      //     'isGroupOwner': true
      //   }
      // },
      // {
      //   'enterpriseName': 'moer',
      //   'accountName': 'moer7',
      //   'wechatId': '****************',
      //   'address': 'http://***************:4008',
      //   'port': '4008',
      //   'botUserId': '104',
      //   'orgToken': '',
      //   'enterpriseConfig': {
      //     'notifyGroupId': 'R:*****************',
      //     'classGroupId': 'R:*****************',
      //     'isGroupOwner': true
      //   }
      // }
    ]

    await PrismaMongoClient.getConfigInstance().config.createMany({
      data: configs
    })
  }, 60000)

  it('获取群 id', async () => {
    const ids = ['****************']

    for (const id of ids) {
      const rooms =  await JuziAPI.listGroup(id)

      for (const room of rooms) {
        console.log(room.name, room.imRoomId, room.owner === id, room.owner)
      }
    }
  }, 60000)
})