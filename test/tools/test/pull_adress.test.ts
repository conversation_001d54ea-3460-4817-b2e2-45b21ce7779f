import { DataService } from '../../../bot/service/moer/getter/getData'
import { ChatHistoryService } from '../../../bot/service/moer/components/chat_history/chat_history'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const names: string[] = [
      '斐莎',
      '齐河源',
      '坦荡人生',
      '雪莲',
      '羞荷',
      '凯哥',
      '郭云',
      '妙德',
      '若桐（吉祥光）',
      '欒美紅🍀如光.妙卿',
      'Celina',
      '王丽',
      '柯云虹',
      '上弦月',
      '小古',
      '妙不可言',
      '如意',
      'Lu～',
      '^温酒^',
      '红玲',
      '🌈光',
      '『萱』',
      '宇宙宝宝疗愈师，咨询'
    ]

    for (const name of names) {
      const chats = await DataService.getChatByWechatName(name)
      const chat = chats[0] as any

      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chat._id)

      for (const chatHistoryElement of chatHistory) {
        if (chatHistoryElement.content.includes(chat.chat_state.userSlots.address)) {
          if (chatHistoryElement.content.length > 100) {
            break
          }
          console.log(name, `${chatHistoryElement.content} ${ chat.chat_state.userSlots.phoneNumber}`)
          break
        }
      }

    }
  }, 60000)
})