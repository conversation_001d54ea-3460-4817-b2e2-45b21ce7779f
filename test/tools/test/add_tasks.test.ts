import { DataService } from '../../../bot/service/moer/getter/getData'
import { startTasks } from '../../../bot/service/moer/components/flow/schedule/task_starter'
import { getUserId } from '../../../bot/config/chat_id'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../bot/model/redis/redis'
import { Config } from '../../../bot/config/config'
import { ChatDB } from '../../../bot/service/moer/database/chat'
import { CacheDecorator } from '../../../bot/lib/cache/cache'
import { loadConfigByWxId } from '../load_config'
import { MessageSender } from '../../../bot/service/moer/components/message/message_send'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.localTest = false

    DataService.getCourseStartTime = CacheDecorator.decorateAsync(DataService.getCourseStartTime)
    DataService.getCourseInfoByCourseNo = CacheDecorator.decorateAsync(DataService.getCourseInfoByCourseNo)

    // 离线绑定客户事件
    const chats = await DataService.getChatsByCourseNo(58)

    for (const chat of chats) {
      const chatId = chat.id
      const userId = getUserId(chatId)

      const queue =  new Queue(chat.id, {
        connection: RedisDB.getInstance()
      })

      const delayedTasks =  await queue.getDelayed()
      if (delayedTasks.length > 0) {
        continue
      }

      Config.setting.wechatConfig = await loadConfigByWxId(chat.wx_id)
      if (chatId !== '7881302696937545_1688856297674945') {
        await MessageSender.sendById({
          user_id: userId,
          chat_id: chatId,
          ai_msg: '亲爱的同学，抱歉给您带来困扰！我们发现后台数据出现了些小问题，但请放心，您的课程并没有重复购买。您可以继续参加下周3月17日的线上课程，期待您的参与。感谢您的理解与支持！[玫瑰]'
        })
      }

      await startTasks(userId, chatId) // 创建任务

      await ChatDB.setHumanInvolvement(chatId, false)

      // 添加事件监听
      const bind_queue = new Queue(`bind_phone_${Config.setting.wechatConfig?.id}`, {
        connection: RedisDB.getInstance()
      })

      await bind_queue.add('task', {
        chat_id: chat.id,
        wx_name: chat.contact.wx_name,
        phone: ''
      })
    }
  }, 1E8)
})