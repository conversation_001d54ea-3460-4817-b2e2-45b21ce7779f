import { ChatHistoryService, IDBBaseMessage } from '../../bot/service/moer/components/chat_history/chat_history'
import { MemoryRecall } from '../../bot/service/moer/components/memory/memory_search'
import { ChatStateStore, ChatStatStoreManager, IChatState } from '../../bot/service/moer/storage/chat_state_store'
import { BaseMessage } from '@langchain/core/messages'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'


export function mockChatHistoryById(sourceChat_id: string) {
  const originFunc = ChatHistoryService.getChatHistoryByChatId.bind(ChatHistoryService)
  ChatHistoryService.getChatHistoryByChatId = async (chat_id: string) => {
    return originFunc(sourceChat_id)
  }
}

export function mockChatHistoryByStr(chatHistory: string) {
  ChatHistoryService.formatHistoryHelper = (messages: (IDBBaseMessage | BaseMessage)[]) => {
    return chatHistory
  }
}

export function mockChatHistoryJsonArray(chatHistory: any[]) {
  ChatHistoryService.getChatHistoryByChatId = async (chat_id: string) => {
    return chatHistory
  }
}

export function mockMemory(sourceChat_id: string) {
  const originFunc = MemoryRecall.memoryRecall.bind(MemoryRecall)
  MemoryRecall.memoryRecall = async (userMessage:string, chat_id: string) => {
    return originFunc(userMessage, sourceChat_id)
  }
}

export async function mockGetUserSlots(sourceChat_id: string, targetChat_id: string) {
  await ChatStatStoreManager.initState(sourceChat_id)
  const userSlots = ChatStateStore.get(sourceChat_id).userSlots
  ChatStateStore.update(targetChat_id, {
    userSlots: userSlots
  })
}

export async function mockGetChatState(chatState: IChatState) {
  ChatStateStore.get = (chat_id: string) => {
    return chatState
  }
}