import { Config } from '../bot/config/config'
import { Client } from 'langsmith'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    // process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    const client = new Client()

    // Check for runs with user_id=4070f233-f61e-44eb-bff1-da3c163895a3
    const runs = client.listRuns({
      projectName: 'moer',
      filter: 'and(eq(metadata_key, \'round_id\'), eq(metadata_value, \'hohCzumVbs5t7so7ybFpbC\'))',
    })

    for await (const run of runs) {
      console.log(JSON.stringify(run, null, 4))
    }
  }, 60000)
})