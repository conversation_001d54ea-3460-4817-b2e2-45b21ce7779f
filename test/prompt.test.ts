import * as hub from 'langchain/hub'
import { Config } from '../bot/config/config'
import { ChatPromptTemplate } from '@langchain/core/prompts'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const prompt = await hub.pull('user_profile_merge', {
      apiKey: Config.setting.langsmith.apiKey,
    })

    console.log(JSON.stringify(prompt, null, 4))
  }, 60000)
})