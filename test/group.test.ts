import { ChatDB } from '../bot/service/moer/database/chat'
import { AoChuangWechatContact } from '../bot/lib/auchuang/openapi/contact'
import { getChatId } from '../bot/config/chat_id'
import { GroupNotification } from '../bot/service/moer/notification/group'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { toJsonSchema } from 'openai-zod-functions'
import { z } from 'zod'
import { AoChuangWechatGroupContact } from '../bot/lib/auchuang/openapi/group_contact'
import { CacheDecorator } from '../bot/lib/cache/cache'

describe('Test', function () {
  beforeAll(() => {
    // Config.setting.wechatConfig = {
    //   // id: 'b468bdbaba4c38ac16568276d392525c4fc8afbc', // 小仙女 测试
    //   id: '607ee3a6463a2c8435ea701e83f92553e500200d', // free tony
    //   // id: '96c019c04c084e94222016e4195691680459fd7d', // free spirit
    //   // id: 'c6213814562ba0abc46ba4619a0abca57c1c2611', // god
    //   name: '小仙女本仙',
    //   botUserId: 'x'
    // }
  })

  it('1', async () => {
    console.log(JSON.stringify(await AoChuangWechatContact.getContactByName('童年'), null, 4))
  }, 30000)

  it('xiaoai', async () => {
    // count xiaoai friends in group
    const group = await AoChuangWechatGroupContact.getContactByChatRoomId('43248009102@chatroom')
    const friendsInGroup: any[] = []

    if (group) {
      const members = group.members
      AoChuangWechatContact.pullContacts = CacheDecorator.decorateAsync(AoChuangWechatContact.pullContacts)
      for (const member of members) {
        // const contacts = await AoChuangWechatGroupContact.pullContacts(member.id)
        const user = await AoChuangWechatContact.getContactByWechatId(member.wechatId)

        if (user) {
          friendsInGroup.push(user)
        }
      }

      console.log(friendsInGroup.length)
    }
  }, 1E8)


  it('zod obj', async () => {
    const fcDefinition =  {
      name: 'analyzeUserPersonaTags',
      description: '对从聊天记录中提取客户画像标签进行处理',
      schema: z.object({
        educational_background: z.string().describe('客户当前的学历，尽量使用缩写，如大四，本科，保研，研0, 研一，研二，在职，博士等。如果没有，则填空字符串'),
        goal: z.string().describe('客户的目标，使用简短的不超过5个字的描述，如找工作，发论文，保研，做项目等。如果没有，则填空字符串'),
        field: z.string().describe('客户的研究领域，尽量使用缩写，如CV,NLP,RL等。如果没有，则填空字符串'),
        major: z.string().describe('客户的专业方向，如机械、计算机、数学等。如果没有，则填空字符串'),
      }),
    }

    console.log(JSON.stringify(toJsonSchema(fcDefinition), null, 4))
  }, 30000)


  it('1231', async () => {
    console.log(await ChatHistoryService.countRemainingMsgAfterMsg('578ee927a702471a4197320d294bfc33a3e4527e_b468bdbaba4c38ac16568276d392525c4fc8afbc', '好啦，你要的资料已经发给你了哦！资料没问题回复1哦。'))
  }, 30000)

  it('test', async () => {
    // await statsig.initialize(
    //     "secret-wPozGiEyaZE0F6jN9bX9RB0AZe9frqk1uINgFyvJU9P",
    //     // {
    //     //   environment: {
    //     //     tier: "development",
    //     //   }
    //     // }
    // )
    //
    // for (let i = 0; i < 10; i++) {
    //   const user = {userID: `user${i}`}
    //
    //   const checkValue = statsig.checkGateSync(user, "toggle_test")
    //   console.log(checkValue)
    //
    //   const experimentValue = statsig.getExperimentSync(user, '策略_a/b_test').getValue('变量1')
    //   console.log(experimentValue)
    //
    //   statsig.logEvent(user, "fuck", i, {
    //     price: "9.99",
    //     item_name: "diet_coke_48_pack",
    //   })
    // }
    //
    // await statsig.flush()
    //
    // // statsig.shutdown()
  }, 30000)

  it('should pass', async () => {
    const user = await AoChuangWechatContact.getContactByName('乔治不是佩奇')
    if (!user) {
      console.log('user not found')
      return
    }

    const chatId = getChatId(user.id)
    console.log(await ChatDB.isHumanInvolvement(chatId))
  })

  it('tags', async () => {

  }, 30000)

  it('test1', async () => {
    const wechatId = 'plant'
    let user = await AoChuangWechatContact.getContactByWechatId(wechatId)
    if (!user) {
      user = await AoChuangWechatContact.getContactByName(wechatId)

      if (!user) {
        console.log('找不到客户，无法操作')
        return
      }
    }

    const chat_id = getChatId(user.id)
    const chat = await ChatDB.getById(chat_id)
    if (!chat) {
      GroupNotification.notify(`客户暂未聊过天，无法操作：${wechatId}`)
      return
    }

    // const isHumanInvolved = false
    const isHumanInvolved = true

    await ChatDB.setHumanInvolvement(chat_id, isHumanInvolved)
    await AoChuangWechatContact.updateAlias({ id: user.id, alias: isHumanInvolved ? '[off]' : '[on]', leading: true, replace: /\[on]|\[off]/ })
    console.log(chat_id)
    console.log(await ChatDB.isHumanInvolvement(chat_id))
  }, 30000)

  it('11', async () => {
    const wechatId = '久伴我!'
    let user = await AoChuangWechatContact.getContactByWechatId(wechatId)
    if (!user) {
      user = await AoChuangWechatContact.getContactByName(wechatId)

      if (!user) {
        console.log('找不到客户，无法操作')
        return
      }
    }

    const chat_id = getChatId(user.id)
    console.log(await ChatDB.isHumanInvolvement(chat_id))
  }, 30000)


})