import { RedisCacheDB } from '../bot/model/redis/redis_cache'
import { ChatInterruptHandler, InterruptError } from '../bot/service/moer/components/message/interrupt_handler'
import logger from '../bot/model/logger/logger'
import Redis from 'ioredis'

// --- Test Suite Setup ---

describe('ChatInterruptHandler Integration Test', () => {
  let redisClient: Redis

  const CHAT_ID = 'integration-test-chat-abc-123'
  const CHAT_VERSION_KEY = `chat:${CHAT_ID}:version`

  // Establish connection and select test DB before all tests
  beforeAll(async () => {
    // This assumes RedisDB.getInstance() uses ioredis and can be configured by env vars
    // or you can manually create a connection: new Redis(process.env.REDIS_URL)
    redisClient = RedisCacheDB.getInstance()
  })

  // Clean the test DB before each test for perfect isolation
  beforeEach(async () => {
    // Clean up the specific key used in tests to ensure isolation
    // We assume RedisCacheDB has a .del() method for this.
    await new RedisCacheDB(CHAT_VERSION_KEY).del()

    console.log(await new RedisCacheDB(CHAT_VERSION_KEY).get())
  })

  // Clean up and close the connection after all tests are done
  afterAll(async () => {
    await redisClient.quit()   // Disconnect gracefully
  })

  describe('static create', () => {
    it('当版本号在 Redis 中不存在时，应初始化为 0 并创建实例', async () => {
      // Pre-condition: Ensure the key doesn't exist
      const preCheck = await new RedisCacheDB(CHAT_VERSION_KEY).get()
      expect(preCheck).toBeNull()

      const handler = await ChatInterruptHandler.create(CHAT_ID)

      // 1. Check the instance property
      expect(handler['expectedVersion']).toBe(0)

      // 2. Verify the state in Redis directly through the abstraction
      const storedVersion = await new RedisCacheDB(CHAT_VERSION_KEY).get()
      // RedisCacheDB should handle JSON parsing, so we expect a number.
      expect(storedVersion).toBe(0)
    })

    it('当版本号在 Redis 中已存在时，应使用现有版本号创建实例', async () => {
      // Pre-condition: Set an existing version in Redis using the abstraction
      await new RedisCacheDB(CHAT_VERSION_KEY).set(5)

      const handler = await ChatInterruptHandler.create(CHAT_ID)

      // Check instance property, expecting a parsed number
      expect(handler['expectedVersion']).toBe(5)
    })
  })

  describe('static incrementChatVersion', () => {
    it('当版本号不存在时，应将其从 0 增加到 1', async () => {
      // The `incr` command in Redis treats a non-existent key as 0
      const newVersion = await ChatInterruptHandler.incrementChatVersion(CHAT_ID)

      expect(newVersion).toBe(1)
      const storedVersion = await new RedisCacheDB(CHAT_VERSION_KEY).get()
      // Even if INCR stores a plain string '1', a robust get() should parse it.
      expect(storedVersion).toBe(1)
    })

    it('当版本号已存在时，应正确地将其 +1', async () => {
      // Setup the state using a plain number. The underlying SUT's INCR command
      // requires a number-like string. We assume RedisCacheDB handles this,
      // or that incrementChatVersion is robust enough.
      await new RedisCacheDB(CHAT_VERSION_KEY).set(7)

      const newVersion = await ChatInterruptHandler.incrementChatVersion(CHAT_ID)

      expect(newVersion).toBe(8)
      const storedVersion = await new RedisCacheDB(CHAT_VERSION_KEY).get()
      expect(storedVersion).toBe(8)
    })
  })

  describe('interruptCheck', () => {
    it('如果版本号未改变，则不应抛出任何异常', async () => {
      // Use the abstraction to set up the initial state
      await new RedisCacheDB(CHAT_VERSION_KEY).set(3)
      const handler = await ChatInterruptHandler.create(CHAT_ID) // expectedVersion is 3

      await expect(handler.interruptCheck()).resolves.toBeUndefined()
    })

    it('如果版本号已改变，则应抛出 InterruptError', async () => {
      const handler = await ChatInterruptHandler.create(CHAT_ID) // expectedVersion is 0

      // Simulate another process incrementing the version
      await ChatInterruptHandler.incrementChatVersion(CHAT_ID) // currentVersion is now 1

      await expect(handler.interruptCheck()).rejects.toThrow(InterruptError)
      await expect(handler.interruptCheck()).rejects.toThrow('当前客户有新消息，当前流程被打断')
    })

    it('如果版本号已改变，应记录一条 trace 日志', async () => {
      const handler = await ChatInterruptHandler.create(CHAT_ID) // expectedVersion is 0
      await ChatInterruptHandler.incrementChatVersion(CHAT_ID) // currentVersion is now 1

      // Use try/catch to inspect the side-effect (logging) without a test failure
      try {
        await handler.interruptCheck()
      } catch (e) {
        // Expected error
        logger.error(e)
      }
    })
  })

  describe('hasChanged', () => {
    it('如果版本号未改变，应返回 false', async () => {
      const handler = await ChatInterruptHandler.create(CHAT_ID)
      expect(await handler.hasChanged()).toBe(false)
    })

    it('如果版本号已改变，应返回 true 且不抛出异常', async () => {
      const handler = await ChatInterruptHandler.create(CHAT_ID) // expectedVersion is 0
      await ChatInterruptHandler.incrementChatVersion(CHAT_ID) // currentVersion is now 1

      await expect(handler.hasChanged()).resolves.toBe(true)
    })
  })
})