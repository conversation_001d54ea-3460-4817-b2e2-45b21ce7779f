import { WorkFlow } from '../bot/service/moer/components/flow/flow'
import { UUID } from '../bot/lib/uuid/uuid'
import { getChatId } from '../bot/config/chat_id'
import { Config } from '../bot/config/config'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { DataService } from '../bot/service/moer/getter/getData'

describe('Test', function () {
  beforeAll(() => {

  })
  it('墨尔rag测试', async () => {
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '五天看完以后看回访就行不再花钱行么？')
  }, 1E8)

  it('留学规划测试 -> 不同意进群', async () => {
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)

  it('情绪化问题测试', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，我完蛋了，怎么办啊')
    await WorkFlow.step(chat_id, user_id, '我没啥具体问题，就是没学上了，求一下安慰')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
  }, 1E8)

  it('留学规划测试 -> 进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '可以的')
  }, 1E8)

  it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)


  it('regex', async () => {
    const s =  '75分有点低，申请名校难度大\n你有考虑哪些国家吗？\n预算大概多少？'
    console.log(/.*[？?么吗啥].*/.test('好的，咱们现在本科几年级？\n'))
  }, 30000)

  it('123123123123', async () => {
    // 取出所有客户 moerId 并缓存一下
    const chatsWithMoerId = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        moer_id: {
          isSet: true
        },
        course_no: {
          in: [
            DataService.getCurrentWeekCourseNo(),
            DataService.getCurrentWeekCourseNo() - 67 // B 线期数
          ]
        }
      },
      select: {
        moer_id: true,
        id: true
      }
    })

    console.log(chatsWithMoerId.length)
  }, 60000)


  it('123123123123qq', async () => {

  }, 60000)

})