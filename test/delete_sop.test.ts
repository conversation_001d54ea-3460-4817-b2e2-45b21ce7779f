import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const queueName =  'moer-general-sop-1688854822630695'

    const  queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    const jobs = await queue.getDelayed()
    const chatIds = new Set()


    for (const job of jobs) {
      if (job.name === '小红书激活') {
        chatIds.add(job.data.chatId)

        await job.remove()
      }
    }

    console.log(chatIds)

    // '7881303498988684_1688854822630695',
    //     '7881301196952712_1688854822630695',
    //     '7881302319011221_1688854822630695',
    //     '7881302230944272_1688854822630695',
    //     '7881303497979465_1688854822630695'
  })
})