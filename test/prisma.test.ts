import { PrismaClient } from '@prisma/client'
import { CommonRegexType, RegexHelper } from '../bot/lib/regex/regex'
import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { MoerRag } from '../bot/service/moer/components/rag/moer_embedding_search'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { Queue } from 'bullmq'
import { RedisCacheDB } from '../bot/model/redis/redis_cache'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const prisma = new PrismaClient()
    console.log(JSON.stringify(await prisma.chat_history.create({
      data: {
        chat_id: 'xxxx',
        role: 'assistant',
        content: 'hi',
        created_at: new Date()
      }
    }), null, 4))
  })

  it('*********', async () => {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'moer'
        }
      }
    )

    for (const config of configs) {
      const queueName = `user-message-queue_${config.wechatId}`
      const queue = new Queue(queueName, {
        connection: RedisCacheDB.getInstance()
      })

      const workers = await queue.getWorkers()

      if (!workers.length) {
        console.log(config.accountName)
      }
    }

    // console.log(JSON.stringify(await queue.getDelayed(), null, 4))
  }, 60000)

  it('', async () => {
    function extractSentence (text: string): [string, string] {
      const index = text.search (/[!?~。！？~\n]/)
      if (index !== -1) {
        return [text.substring (0, index + 1).trim (), text.substring (index + 1)]
      }
      return [text.trim (), '']
    }
    let currentSentence = `首先准备好本科成绩单和学位证书

然后考个雅思，目标6.5分以上

接下来就是选校和申请材料，咱们可以详细聊聊`
    while ((!RegexHelper.strIncludeRegex (currentSentence, CommonRegexType.URL)) && currentSentence.search (/[!?~。！？~\n]/) !== -1) {
      const [sentence, remaining] = extractSentence (currentSentence)
      if (sentence) {
        console.log(sentence)
      }
      currentSentence = remaining
    }

  }, 60000)


  it('1', async () => {
    console.log(RegexHelper.strIncludeRegex (`首先准备好本科成绩单和学位证书

然后考个雅思，目标6.5分以上

接下来就是选校和申请材料，咱们可以详细聊聊`, CommonRegexType.URL))
  }, 60000)

  it('12', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getChatHistoryByChatId('7881300846030208_1688854546332791'), null, 4))
  }, 60000)

  it('123123', async () => {
    const isAiCompleteSlotAskTask = /.*[？?么吗啥].*/.test(`先把绩点搞清楚，绩点很关键

然后大三大四多参加科研项目和实习

考研和申请美国研究生都需要这些背景`) ? 'true' : 'false' // 问句检查

    console.log(isAiCompleteSlotAskTask)
  }, 60000)

  it('rag', async () => {
    const ragResults = await MoerRag.ragImplementation('moer_rag', '久久进入不了状态，没有任何画面', 'xx')
    console.log(JSON.stringify(ragResults, null, 4))
  }, 60000)

  it('123123', async () => {
    await PrismaMongoClient.getInstance().danmu.createMany({
      data: [],
      // @ts-ignore fku prisma
      skipDuplicates: true,
    })
  }, 60000)
})