import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import { DataService } from '../bot/service/moer/getter/getData'
import { Config } from '../bot/config/config'
import { CsvHelper } from '../bot/lib/csv/csv_parse'

describe('Test', function () {
  beforeAll(() => {

  })

  it('调用接口查询客户完课率', async () => {
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: 49,
        wx_id: '1688858047620029'
      },
    })

    const csvObj = {}


    const completedChats: string[] = []
    await Promise.all(chats.map(async (chat) => {
      await ChatStatStoreManager.initState(chat.id)

      try {
        const isCompleted = await DataService.isCompletedCourse(chat.id, {
          day: 0
        })

        if (Config.isInternalMember(chat.contact.wx_id)) {
          return
        }

        if (isCompleted) {
          completedChats.push(chat.id)

          csvObj[chat.contact.wx_name] = {
            moerId : chat.moer_id
          }
        }

      } catch (e) {
        //
      }
    }))

    console.log(completedChats.length)
    CsvHelper.write2DJson('moer7 完课.csv', csvObj, ['userId'])
  }, 60000)


  it('should pass', async () => {
    // const names = ['柯云虹', '淑红', 'lily', '小古']
    //
    // for (let i = 0; i < names.length; i++) {
    //   const chats = await DataService.getChatByWechatName(names[i])
    //   const chat = chats[0] as any
    //
    //   const chatHistory =   await ChatHistoryService.getChatHistoryByChatId(chat._id, true)
    //   const formattedMessages = chatHistory.map((history) => {
    //     return {
    //       role: history.role,
    //       content: history.content,
    //       time: history.created_at.toLocaleString(),
    //     }
    //   })
    //
    //   console.log(JSON.stringify(formattedMessages, null, 2))
    //
    //   await FileHelper.writeFile(`${names[i]  }.json`, JSON.stringify(formattedMessages, null, 2))
    // }
  }, 60000)
})