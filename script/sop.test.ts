import { SopCsv } from '../admin_platform/app/api/sop_csv'
import { getUserId } from '../bot/config/chat_id'
import { Config } from '../bot/config/config'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { TanglangApi } from '../bot/lib/tanglang/api'
import { startTasks } from '../bot/service/moer/components/flow/schedule/task_starter'
import { loadConfigByWxId } from '../test/tools/load_config'

describe('迁移sop', () => {
  jest.setTimeout(6000000)
  test('将所有sop的条件都附带一个fixed', async () => {
    const mongoClient = PrismaMongoClient.getInstance()
    const sops = await mongoClient.sop.findMany()
    for (const sop of sops) {
      for (let i = 0; i < sop.situations.length; i++) {
        for (let j = 0; j < sop.situations[i].conditions.length; j++) {
          sop.situations[i].conditions[j].type = 'fixed'
        }
      }
      await mongoClient.sop.update({ where:{ id:sop.id }, data:{ ...sop, id:undefined } as unknown as any })
    }
  })
  test('import sop', async () => {

    const sop = await SopCsv.parseCsv('./import.csv')
    const mongoClient = PrismaMongoClient.getInstance()
    await mongoClient.sop.createMany({ data:sop })
  })

  test('reset sop', async() => {
    const mongoClient = PrismaMongoClient.getInstance()
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    const users = await mongoClient.chat.findMany({ where:{ course_no:71 } })
    const accounts = await mongoConfigClient.config.findMany({ where:{ enterpriseName:'moer' } })
    for (const account of accounts) {
      // console.log(account)
      // await clearTasks(users.map((item) => item.id), account.wechatId)
    }

    for (const user of users) {
      console.log(user.id)
      const userId = getUserId(user.id)
      Config.setting.wechatConfig = await loadConfigByWxId(user.wx_id)
      await startTasks(userId, user.id, true)
    }
  })


})