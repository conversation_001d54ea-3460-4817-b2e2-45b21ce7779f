#!/usr/bin/env ts-node
/**
 * 部署打包脚本
 *
 * 支持：
 *  - 通过 inquirer 输入版本号，如果输入为空则自动生成当前日期时间作为版本号
 *  - 交互式多选服务进行部署（使用 inquirer 的 checkbox）
 */

import * as fs from 'fs'
import { execSync } from 'child_process'
import inquirer from 'inquirer'
import chalk from 'chalk'
import yaml from 'js-yaml'

// Define the type for the parsed docker-compose structure
interface DockerCompose {
  services: Record<string, any>
}


// ===== 配置变量 =====
const IMAGE_NAME = 'test_bot_image'
// 远程仓库的基础地址，后续会拼接上服务名，例如：REMOTE_REPO
const REMOTE_REPO = 'crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test'
const LOG_FILE = './deploy_script.log'

// 读取可用服务
const filePath = './docker/docker-compose.yaml'
const doc: DockerCompose = yaml.load(fs.readFileSync(filePath, 'utf8')) as DockerCompose
const availableServices: string[] = Object.keys(doc.services)

// ===== 工具函数 =====
/**
 * 获取默认版本号：格式为 YYYYMMDDHHMMSS
 */
function getDefaultVersion(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day}.${hours}-${minutes}-${seconds}`
}

/**
 * 日志函数，将日志同时输出到控制台和日志文件中
 * @param message 要记录的日志信息
 * @param color
 */
function log(message: string, color: string = 'white') {
  const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19)
  const coloredMessage = chalk[color](`[${timestamp}] ${message}`)
  fs.appendFileSync(LOG_FILE, `${coloredMessage  }\n`)
  console.log(coloredMessage)
}

/**
 * 执行 shell 命令，若失败则退出
 * @param command 命令字符串
 */
function runCommand(command: string) {
  try {
    execSync(command, { stdio: 'inherit' })
  } catch (error) {
    log(`命令执行失败: ${command}`, 'red')
    process.exit(1)
  }
}

// ===== 主流程 =====
async function main() {
  log('===== 开始部署流程 =====', 'cyan')
  log('===== 本地构建流程并推送流程 =====', 'cyan')

  // 通过 inquirer 输入版本号
  const { versionInput } = await inquirer.prompt([
    {
      type: 'input',
      name: 'versionInput',
      message: '请输入版本号（留空自动生成）：',
      default: getDefaultVersion(), // 如果输入为空，自动生成默认版本号
    }
  ])
  const VERSION = versionInput.trim() || getDefaultVersion()  // 若客户输入为空，则使用默认版本号
  log(`使用的版本号: ${VERSION}`, 'yellow')

  // 使用 inquirer 交互式多选要部署的服务
  const answers = await inquirer.prompt([
    {
      type: 'checkbox',
      name: 'services',
      message: '请选择要部署的服务 (空格键选择，回车确认):',
      choices: availableServices,
      validate(answer: string[]) {
        if (answer.length < 1) {
          return '请至少选择一个服务进行部署！'
        }
        return true
      }
    }
  ])
  const selectedServices: string[] = answers.services
  log(`选中的服务: ${selectedServices.join(', ')}`, 'green')

  // 统一构建镜像，执行构建、打标签、推送操作
  const serviceImageTag = `${IMAGE_NAME}:${VERSION}`
  log(`构建 Docker 镜像: ${serviceImageTag}`, 'blue')

  // 假设每个服务目录下都有 Dockerfile，构建上下文为服务目录
  runCommand(`docker buildx build --platform linux/amd64 -t ${serviceImageTag} .`)

  // 推送前需要将镜像打上远程仓库的标签

  log(`标记镜像: ${serviceImageTag} 为 ${REMOTE_REPO}:${VERSION}`, 'magenta')
  runCommand(`docker tag ${serviceImageTag} ${REMOTE_REPO}:${VERSION}`)
  runCommand(`docker push ${REMOTE_REPO}:${VERSION}`)

  // 同时推送 latest 标签（可选）
  log(`标记镜像: ${serviceImageTag} 为 ${REMOTE_REPO}:latest`, 'magenta')
  runCommand(`docker tag ${serviceImageTag} ${REMOTE_REPO}:latest`)
  runCommand(`docker push ${REMOTE_REPO}:latest`)

  log('所有服务的镜像构建并推送完成', 'green')

  // 执行远程部署：将所有选中的服务名称作为参数传递到远程部署脚本中
  const sshCommand = `ssh root@139.224.228.125 "cd /root/.nvm/wechaty_bot && git pull && cd docker && ./server_deploy.sh ${selectedServices.join(' ')}"`
  log(`执行远程部署命令: ${sshCommand}`, 'yellow')
  runCommand(sshCommand)

  log(`部署流程完成，版本号: ${VERSION}`, 'cyan')
  execSync('open "https://sls.console.aliyun.com/lognext/project/moer-test/logsearch/moer-moer4?slsRegion=cn-shanghai"')
}

// 执行主流程
main()
