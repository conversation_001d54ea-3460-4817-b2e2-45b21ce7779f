#!/bin/bash

set -e

# 变量
REMOTE_REPO="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test"
TAG_LATEST="latest"
DOCKER_COMPOSE_FILE="docker-compose.yaml"
LOG_FILE="./deploy_script.log"

# 如果有传入参数，则使用传入的服务名称列表；如果没有，则默认部署所有客户端服务
if [ -n "$1" ]; then
    SERVICES_TO_DEPLOY=("$@")
else
    SERVICES_TO_DEPLOY=("client1" "client2")
fi

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') : $1" | tee -a "$LOG_FILE"
}

log "===== 服务器端开始部署流程 ====="

# 循环遍历每个需要部署的服务
for CLIENT_SERVICE in "${SERVICES_TO_DEPLOY[@]}"; do
    log "开始部署服务: $CLIENT_SERVICE"

    # 1. 拉取客户端镜像（从远程仓库拉取最新）
    log "拉取镜像: $REMOTE_REPO:$TAG_LATEST"
    docker compose -f "$DOCKER_COMPOSE_FILE" pull "$CLIENT_SERVICE" >> "$LOG_FILE" 2>&1

    # 2. 启动容器
    log "启动客户端服务: $CLIENT_SERVICE"
    docker compose -f "$DOCKER_COMPOSE_FILE" up -d --no-deps "$CLIENT_SERVICE" >> "$LOG_FILE" 2>&1

    # 3. 等待容器启动（10 秒）
    log "容器已启动，等待 10 秒后进行健康检查..."
    sleep 10

    # 4. 容器健康检查
    log "开始健康检查..."
    STATUS=$(docker compose -f "$DOCKER_COMPOSE_FILE" ps -q "$CLIENT_SERVICE" | xargs docker inspect -f '{{.State.Status}}' 2>/dev/null)

    log "容器 $CLIENT_SERVICE 当前状态为: $STATUS"

    if [ "$STATUS" != "running" ]; then
        log "容器 $CLIENT_SERVICE 当前状态为: $STATUS（非 running）"
        log "$CLIENT_SERVICE 部署失败，退出部署流程。"
        exit 1
    else
       EXPOSED_PORT=$(docker port "$CLIENT_SERVICE" | grep -o -m 1 '[0-9]\{4\}' | uniq)

       if curl -f "http://localhost:$EXPOSED_PORT" >> "$LOG_FILE" 2>&1; then
           log "健康检查通过：容器在宿主机的 $EXPOSED_PORT 端口可访问"
       else
           log "健康检查失败：容器在宿主机的 $EXPOSED_PORT 端口不可访问"
           log "$CLIENT_SERVICE 部署失败，退出部署流程。"
           exit 1
       fi
    fi

    log "$CLIENT_SERVICE 部署成功！"
    log "=========================================="
done

log "所有指定的服务部署流程完成。"
