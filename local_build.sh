#!/bin/bash

# 设置变量
IMAGE_NAME="test_bot_image"
REMOTE_REPO="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test"
LOG_FILE="./deploy_script.log"

# 获取版本号
if [ -n "$1" ]; then
    VERSION="$1"
    log_level="用户输入的版本号: $VERSION"
else
    # 使用当前日期和时间作为默认版本号
    VERSION=$(date '+%Y%m%d%H%M%S')
    log_level="默认生成的版本号: $VERSION"
fi

# 创建日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') : $1" | tee -a "$LOG_FILE"
}

# 开始日志
log "===== 开始部署流程 ====="
log "===== 本地构建流程并推送流程 ====="
log "$log_level"

# 构建 Docker 镜像
log "构建 Docker 镜像: $IMAGE_NAME:$VERSION"
docker buildx build --platform linux/arm64 -t "$IMAGE_NAME:$VERSION" .
if [ $? -ne 0 ]; then
    log "镜像构建失败！"
    exit 1
fi

# 标记并推送版本镜像
log "标记并推送镜像: $REMOTE_REPO:$VERSION"
docker tag "$IMAGE_NAME:$VERSION" "$REMOTE_REPO:$VERSION"
docker push "$REMOTE_REPO:$VERSION"
if [ $? -ne 0 ]; then
    log "推送镜像 $VERSION 失败！"
    exit 1
fi

# 如果需要，也可以推送 latest 标签
log "标记并推送镜像: $REMOTE_REPO:latest"
docker tag "$IMAGE_NAME:$VERSION" "$REMOTE_REPO:latest"
docker push "$REMOTE_REPO:latest"
if [ $? -ne 0 ]; then
    log "推送镜像 latest 失败！"
    exit 1
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 镜像构建并推送完成，版本号: $VERSION."
cd docker && ./local_deploy.sh