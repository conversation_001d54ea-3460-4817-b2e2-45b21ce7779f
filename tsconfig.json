{
  "include": [
    "bot/**/*",
    "bot_starter/**/*",
  ],
  "compileOnSave": true,
  "compilerOptions": {
    "allowJs": true,
    "strictPropertyInitialization": false,
    "outDir": "./dist/",
    "sourceMap": true,
    "pretty": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "module": "commonjs",
    "target": "esnext",
    "strict": true,
    "moduleResolution": "node",
    "lib": [
      "ES2021",
      "es2020",
      "dom",
      "es5",
      "es6"
    ],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  },
  "paths": {
    "~/config/*": [
      "config/*"
    ],
    "~/const/*": [
      "const/*"
    ],
    "~/function/*": [
      "function/*"
    ],
    "~/lib/*": [
      "lib/*"
    ],
    "~/locals/*": [
      "locals/*"
    ],
    "~/middleware/*": [
      "middleware/*"
    ],
    "~/model/*": [
      "model/*"
    ],
    "~/service/*": [
      "service/*"
    ]
  },
  "exclude": ["dst", "node_modules", "test"]
}
