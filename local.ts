import { createInterface } from 'readline'
import { Config } from './bot/config/config'
import { WorkFlow } from './bot/service/moer/components/flow/flow'
import { MessageSender } from './bot/service/moer/components/message/message_send'
import { ChatHistoryService } from './bot/service/moer/components/chat_history/chat_history'
import { ChatStateStore, ChatStatStoreManager } from './bot/service/moer/storage/chat_state_store'
import { ChatDB } from './bot/service/moer/database/chat'
import { clearTasks } from './bot/service/moer/components/flow/schedule/task_starter'
import { SendWelcomeMessage } from './bot/service/moer/components/flow/schedule/task/sendWelcomeMessage'
import { TaskName } from './bot/service/moer/components/flow/schedule/type'
import logger from './bot/model/logger/logger'
import chalk from 'chalk'
import { DataService } from './bot/service/moer/getter/getData'
import { StringHelper } from './bot/lib/string'
import { MoerNode } from './bot/service/moer/components/flow/nodes/type'

Config.setting.localTest = true
Config.setting.wechatConfig = {
  'id': '1688854546332791',
  //'token': 'e0d70927040a4efa92b79b7279ecb1c1',
  'name': 'syq',
  'botUserId': 'ShengYueQing',
  'notifyGroupId': 'R:10829337560927503',
  'classGroupId': 'R:10926892688487567',
  'courseNo': 47
}

// mock chat_id and user_id
// const user_id = UUID.short()
// const chat_id = getChatId(user_id)

// 真实个人的 user_id 和 chat_id（Yusen）
const user_id = '7881302505051440'
const chat_id = '7881302505051440_1688854546332790'

class CLI {
  private cli: any
  private commands: {
    [key: string]: (...args: string[]) => any
  }

  constructor() {
    this.cli = createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    this.commands = {
      'help': () => {
        this.displayMessage(`Commands: ${  Object.keys(this.commands).join(', ')}`)
      },
      'exit': () => {
        this.displayMessage('Exiting the test...')
        this.cli.close()
      },
      'clear': async () => {
        await MessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: '聊天已重置'
        })
        await ChatHistoryService.clearChatHistory(chat_id)
        await ChatStatStoreManager.clearState(chat_id)
        if (await ChatDB.getById(chat_id)) {
          await ChatDB.setHumanInvolvement(chat_id, false)
        }

        await clearTasks(chat_id)

        await new SendWelcomeMessage().process({
          userId: user_id,
          chatId: chat_id,
          name: TaskName.SendWelcomeMessage
        })
      },
      // Add more commands as needed
    }

    this.cli.on('line', async (line: string) => {
      const trimmedLine = line.trim()

      if (trimmedLine === '') {
        logger.warn('Empty input')
        return
      }

      const commandNames = Object.keys(this.commands)
      if (commandNames.some((c) => trimmedLine.startsWith(c))) {
        const [command, ...args] = trimmedLine.split(' ')
        if (this.commands[command]) {
          this.commands[command](...args)
        } else {
          this.displayMessage(`Unknown command: ${command}`)
        }
      } else {
        try {
          await ChatStatStoreManager.initState(chat_id)
          await WorkFlow.step(chat_id, user_id, line)
        } catch (error) {
          console.error('An error occurred:', error)
        }
      }
    })

    this.cli.on('close', async () => {
      // 删除临时客户
      if (StringHelper.isShortUUID(user_id)) {
        await ChatDB.removeById(chat_id)
      }
      process.exit(0)
    })

    console.log(chalk.redBright('请在下方开始输入: '))
  }

  displayMessage(message: string) {
    console.log(chalk.redBright(message))
  }
}

async function runInteractiveTest() {
  // 阶段跳转
  await ChatStatStoreManager.initState(chat_id)
  ChatStateStore.update(chat_id, {
    nextStage: MoerNode.FreeTalk
  })

  // 时间模拟
  DataService.getCurrentTime = async () => {
    return {
      day:5,
      time: '09:00:00',
      is_course_week: true
    }
  }

  // 创建一个假的客户
  if (!await ChatDB.getById(chat_id)) {
    await ChatDB.create({
      id: chat_id,
      round_ids: [],
      contact: {
        wx_id: user_id,
        wx_name: 'test'
      },
      wx_id: '1688854546332791',
      created_at: new Date(),
      chat_state: ChatStateStore.get(chat_id),
      moer_id: '977260',
      course_no: DataService.getCurrentWeekCourseNo()
    })
  }

  new CLI()
}

runInteractiveTest()