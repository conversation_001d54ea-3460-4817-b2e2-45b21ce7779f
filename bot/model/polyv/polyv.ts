import * as crypto from 'crypto'
import { Config } from '../../config/config'
import axios from 'axios'

// 定义 User 接口
interface IUser {
  actor: string | null
  banned: boolean
  channelId: number
  clientIp: string
  nickname: string
  pic: string
  roomId: string
  sessionId: string | null
  uid: string
  userId: string
  userSource: string
  userType: string
  param4: string | null
  param5: string | null
}

// 定义播放日志接口
interface IDanmu {
  id: string
  accountId: string
  clientIP: string
  content: string | object
  event: null | string
  image: string
  msgType: null | string
  quote: string
  sessionId: string
  roomId: string
  channelId: number
  time: number
  userType: string
  status: string
  sourceType: string
  user: IUser
}

interface IViewLogContent {
  viewerId: string
  nick: string
  viewType: string
  sessionId: string
  startTime: number
  playDuration: string
  channelId: number
  area: string
  ip: string
  browser: string
  param4: string
  param5: string
}

interface IViewLogData {
  pageNumber: number
  pageSize: number
  totalPages: number
  totalItems: number
  contents: IViewLogContent[]
}

interface IViewLogResponse {
  code: number
  status: string
  requestId: string
  data: IViewLogData
  success: boolean
  desc?: string
}

/**
 * 保利威直播服务 API
 */
export class PolyvAPI {
  public static sign(params: Record<string, any>) {
    const appId = Config.setting.polyv.appId
    const appSecret = Config.setting.polyv.appSecret

    const timestamp = Date.now().toString()
    const paramMap: { [key: string]: any } = {}
    // 公共参数
    paramMap['appId'] = appId
    paramMap['timestamp'] = timestamp

    // 过滤掉参数值为 null 或 undefined 的参数
    const filteredParams = Object.keys(params).reduce((acc, key) => {
      if (params[key] !== null && params[key] !== undefined) {
        acc[key] = params[key]
      }
      return acc
    }, {})

    // MD5签名(默认)
    // 按 key 字典顺序升序排列参数
    const sortedParams = Object.keys(filteredParams)
      .sort()
      .map((key) => `${key}${filteredParams[key]}`)
      .join('')

    // 拼接首尾 appSecret
    const signStr = `${appSecret}${sortedParams}${appSecret}`

    // 生成 MD5 并转换为大写
    return crypto.createHash('md5').update(signStr, 'utf8').digest('hex').toUpperCase()
  }


  /**
   * 获取单场直播弹幕
   * @param channelId 频道 ID
   * @param startDay 日期，格式为 yyyy-MM-dd
   * @param endDay 日期，格式为 yyyy-MM-dd
   */
  public static async getLiveDanmu(channelId: string, startDay: string, endDay: string) {
    let allContents: IDanmu[] = []  // 存储所有分页的弹幕
    let page = 1
    let hasNextPage = true

    while (hasNextPage) {
      const params = {
        appId: Config.setting.polyv.appId,
        timestamp: new Date().getTime(),
        channelId,
        startDay,
        endDay,
        page
      }
      const sign = this.sign(params)

      const response = await axios.get('http://api.polyv.net/live/v3/channel/chat/get-history-page', {
        params: {
          ...params,
          sign,
        },
      })

      const data = response.data
      if (data && data.data && data.data.contents && data.data.contents.length === 1000) {
        page++  // 请求下一页
      } else {
        hasNextPage = false  // 如果没有内容了，结束循环
      }

      allContents = allContents.concat(data.data.contents)  // 将当前页的内容追加到总结果
    }

    return allContents
  }


  /**
   * 获取观众观看详情， 注意 startDate 和 endDate 不能超过太久，否则会请求超时
   * @param viewerId 观众 ID，同 MoerId
   * @param startDate 开始日期，格式为 yyyy-MM-dd
   * @param endDate 结束日期，格式为 yyyy-MM-dd
   */
  public static async getViewLog(viewerId: string, startDate: string, endDate: string) {
    const params = {
      appId: Config.setting.polyv.appId,
      timestamp: new Date().getTime(),
      startDate,
      endDate,
      viewerId,
      pageSize: 1000
    }
    const sign = this.sign(params)

    const response = await axios.get<IViewLogResponse>('http://api.polyv.net/live/v4/user/viewlog/detail', {
      params: {
        ...params,
        sign,
      },
    })

    if (response.data.code !== 200) {
      throw new Error(`请求失败：${response.data.code} ${response.data.desc}`)
    }

    return response.data
  }
}