import { PolyvAPI } from './polyv'
import { StringHelper } from '../../lib/string'
import { FileHelper } from '../../lib/file'

describe('Test', function () {
  beforeAll(() => {

  })

  it('danmu list', async () => {
    const data = await PolyvAPI.getLiveDanmu('5182563', '2024-09-26', '2024-09-26')
    console.log(JSON.stringify(data, null, 4))
    const res = data.filter((item) => StringHelper.isNumber(item.user.userId))

    const fk = res.map((item) => {
      return {
        userName: item.user.nickname,
        content: item.content,
        time: new Date(item.time).toLocaleString()
      }
    })

    // 写到文件里
    await FileHelper.writeFile('danmu.json', JSON.stringify(fk, null, 4))

  }, 60000)

  it('viewLog', async () => {
    try {
      console.log(JSON.stringify(await PolyvAPI.getViewLog('926677', '2024-10-14', '2024-10-14'), null, 4))
    } catch (e) {
      console.log(e)
    }

  }, 60000)

  it('log', async () => {
    console.log(new Date(1727266427000).toLocaleString())
  }, 60000)
})