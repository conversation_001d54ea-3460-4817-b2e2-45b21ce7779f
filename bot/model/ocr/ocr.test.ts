import { FreeSpiritOCR } from './ocr'

describe('Test', function () {
  beforeAll(() => {

  })

  it('', async () => {
    const a = true
    console.log(typeof a === 'boolean')
  }, 60000)

  it('ocr', async () => {
    const res = await FreeSpiritOCR.recognizeUrl('https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/1241731918951_.pic.jpg')
    const text = res.map((item) => item.text).join('')

    const pattern = /(\d+)分\/\d+分/
    const match = text.match(pattern)

    if (!match) return null

    const score = match[1]
    const numberScore = Number(score)

    // 多重验证
    if (
      !Number.isFinite(numberScore) || // 检查是否为有限数字
        numberScore < 0 || // 检查是否为负数
        !Number.isInteger(numberScore) // 检查是否为整数
    ) {
      return null
    }

    console.log(numberScore)
  }, 1E8)

  it('ocr_test', async () => {
    const res = await FreeSpiritOCR.recognizeUrl('https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/9370e9a4-6bc7-431a-b2ed-c880fa014561.jpg.jpg')

    const text = res.map((item) => item.text).join(' ')
    console.log(text)

    const pattern = /(-?\d+)分\/\d+分/
    const match = text.match(pattern)

    if (!match) return null

    const score = match[1]
    const numberScore = Number(score)

    // 多重验证
    if (
      !Number.isFinite(numberScore) || // 检查是否为有限数字
        // numberScore < 0 || // 检查是否为负数
        !Number.isInteger(numberScore) // 检查是否为整数
    ) {
      return null
    }

    console.log(numberScore)
  }, 1E8)
})