import stream from 'stream'
import { DateHelper } from '../../lib/date/date'
import axios from 'axios'
import { Config } from '../../config/config'

export class FeishuWebhookStream extends stream.Writable {
  private url: string
  constructor(url: string) {
    super()
    this.url = url
  }

  _write(chunk, encoding, callback) {
    if (Config.setting.localTest) { // 本地测试
      return
    }

    const log = JSON.parse(chunk.toString())
    const levelLabels = {
      10: 'trace',
      20: 'debug',
      30: 'info',
      40: 'warn',
      50: 'error',
      60: 'fatal',
    }

    const level = levelLabels[log.level] || 'UNKNOWN'
    const timestamp = DateHelper.getFormattedDate(new Date(log.time))
    const hostname = log.hostname ? log.hostname.slice(0, 7) : 'unknown'

    function removeEscapedCharacters(str) {
      // 匹配转义字符
      const escapedCharRegex = /\\[btnfr"'\\]/g
      return str.replace(escapedCharRegex, '')
    }

    function removeAnsiColors(str) {
      // 匹配 ANSI 颜色控制字符
      const ansiColorRegex = /[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nq-uy=><]/g
      return str.replace(ansiColorRegex, '')
    }

    function cleanLogMessage(str) {
      // 去除转义字符
      str = removeEscapedCharacters(str)
      // 去除 ANSI 颜色控制字符
      str = removeAnsiColors(str)
      return str
    }

    const formattedLog = JSON.stringify({
      level,
      timestamp,
      hostname,
      msg: cleanLogMessage(log.msg)
    }, null, 4)

    axios.post(this.url, {
      msg_type: 'text',
      content: {
        text: formattedLog
      }
    }).catch((error) => {
      console.error('Error sending log to Feishu')
    }).then(() => callback())
  }
}