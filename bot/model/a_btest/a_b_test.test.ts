import { ABTest } from './a_b_test'

describe('ABTest', () => {
  beforeEach(() => {
    // Reset the static properties before each test
    (ABTest as any).userCount = 0;
    (ABTest as any).usersInGroups = new Map()
  })

  test('should consistently assign the same user to the same group', () => {
    const userId = 'user1'
    const numGroups = 2

    const group1 = ABTest.getStrategyForUser(userId, numGroups)
    const group2 = ABTest.getStrategyForUser(userId, numGroups)

    expect(group1).toBe(group2)
  })

  test('should distribute users evenly among groups', () => {
    const numGroups = 3
    const numUsers = 90

    const groupCounts = new Array(numGroups).fill(0)

    for (let i = 0; i < numUsers; i++) {
      const userId = `user${i}`
      const group = ABTest.getStrategyForUser(userId, numGroups)
      groupCounts[group]++
    }

    // Check if the distribution is roughly even
    const expectedUsersPerGroup = numUsers / numGroups
    groupCounts.forEach((count) => {
      expect(count).toBeCloseTo(expectedUsersPerGroup, 0)
    })
  })

  test('should handle different numbers of groups', () => {
    const user1 = 'user1'
    const user2 = 'user2'

    const group1With2Groups = ABTest.getStrategyForUser(user1, 2)
    const group2With2Groups = ABTest.getStrategyForUser(user2, 2)

    expect(group1With2Groups).toBe(0)
    expect(group2With2Groups).toBe(1);

    // Reset for next test
    (ABTest as any).userCount = 0;
    (ABTest as any).usersInGroups = new Map()

    const group1With3Groups = ABTest.getStrategyForUser(user1, 3)
    const group2With3Groups = ABTest.getStrategyForUser(user2, 3)

    expect(group1With3Groups).toBe(0)
    expect(group2With3Groups).toBe(1)
  })
})
