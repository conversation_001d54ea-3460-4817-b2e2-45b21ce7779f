import { FeishuAPI, RecordResponse } from './feishu'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {

    // Usage
    const feishuApi = new FeishuAPI('cli_a55b6b1e793e100c', 'osDK1PdTsVnrZusUwFFOGVchHs3Rx8ys')
    const appToken = 'L2ERbIK82aoMxSsxmUDcDkFXn1b'
    const tableId = 'tblftUYEdLfvLNlx'

    const feishuData = await feishuApi.getDatabaseData(appToken, tableId)

      interface ZiliaoInfo {
          来源: string
          标题: string
          链接: string
      }

      function extractItemsInfo(responseData: RecordResponse): ZiliaoInfo[] {
        const rows: ZiliaoInfo[] = []

        // 遍历 items 数组，并提取信息
        responseData.data.items.forEach((item) => {
          const fields = item.fields

          if (fields.来源 && fields.标题 && fields.链接) {
            rows.push({
              来源: fields.来源,
              标题: fields.标题,
              链接: fields.链接
            })
          }
        })

        return rows
      }

      console.log(JSON.stringify(extractItemsInfo(feishuData), null, 4))
  })


})