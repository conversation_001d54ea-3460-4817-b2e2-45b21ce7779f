import { Config } from '../../config/config'
import axios from 'axios'

export class BingSearch {
  private static readonly BING_SEARCH_V7_ENDPOINT = 'https://api.bing.microsoft.com/v7.0/search'
  private static readonly DEFAULT_SEARCH_ENGINE_TIMEOUT = 5000 // Timeout in milliseconds
  private static readonly BING_MKT = 'zh-CN' // Market code


  public static async search(query: string): Promise<any[]> {
    const params = {
      q: query,
      mkt: BingSearch.BING_MKT
    }

    try {
      const response = await axios.get(BingSearch.BING_SEARCH_V7_ENDPOINT, {
        headers: {
          'Ocp-Apim-Subscription-Key': Config.setting.bingSearch.apiKey
        },
        params: params,
        timeout: BingSearch.DEFAULT_SEARCH_ENGINE_TIMEOUT
      })

      if (response.status !== 200) {
        console.error(`HTTP error: ${response.status} ${response.statusText}`)
        throw new Error('Search engine error.')
      }

      const jsonContent = response.data
      return jsonContent.webPages ? jsonContent.webPages.value : []

    } catch (error) {
      console.error(`Bing Search failed: ${JSON.stringify(error)}`)
      return []
    }
  }

  public static async getSimpleSearchResult(query: string): Promise<any[]> {
    const results = await BingSearch.search(query)
    return results.map((result: any) => {
      return {
        title: result.name,
        description: result.snippet,
        url: result.url
      }
    })
  }
}