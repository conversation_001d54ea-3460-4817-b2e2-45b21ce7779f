import { Redis } from 'ioredis'
import { RedisCacheDB } from './redis_cache'

interface RateLimiterOptions {
    windowSize: number    // 时间窗口大小（以秒为单位）
    maxRequests: number   // 时间窗口内允许的最大请求次数
    keyPrefix?: string    // 键前缀，默认为 'rate_limiter'
}

class RateLimiter {
  private redis: Redis
  private windowSize: number
  private maxRequests: number
  private keyPrefix: string

  constructor(options: RateLimiterOptions) {
    this.redis = RedisCacheDB.getInstance()
    this.windowSize = options.windowSize
    this.maxRequests = options.maxRequests
    this.keyPrefix = options.keyPrefix || 'rate_limiter'
  }

  /**
   * 检查请求是否被允许
   * @param key 唯一标识（例如客户ID或IP地址）
   * @param chatId
   * @returns 如果请求被允许，返回 true；否则返回 false
   */
  async isAllowed(key: string, chatId: string): Promise<boolean> {
    const redisKey = `${this.keyPrefix}:${key}_${chatId}`
    const currentCount = await this.redis.incr(redisKey)

    if (currentCount === 1) {
      // 第一次请求，设置过期时间
      await this.redis.expire(redisKey, this.windowSize)
    }

    if (currentCount > this.maxRequests) {
      return false
    }

    return true
  }

  /**
     * 获取剩余请求次数
     * @param key 唯一标识
     * @returns 剩余请求次数
     */
  async getRemaining(key: string): Promise<number> {
    const redisKey = `${this.keyPrefix}:${key}`
    const currentCount = await this.redis.get(redisKey)
    if (currentCount === null) {
      return this.maxRequests
    }
    return Math.max(this.maxRequests - parseInt(currentCount, 10), 0)
  }

  /**
     * 重置计数
     * @param key 唯一标识
     * @returns void
     */
  async reset(key: string): Promise<void> {
    const redisKey = `${this.keyPrefix}:${key}`
    await this.redis.del(redisKey)
  }
}

export default RateLimiter