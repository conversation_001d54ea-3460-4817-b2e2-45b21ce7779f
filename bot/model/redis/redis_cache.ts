import Redis from 'ioredis'
import { RedisDB } from './redis'

type RedisValueType = string | Buffer | number | object

/**
 * redis cache
 */
export class RedisCacheDB {
  private static redis?: Redis
  private readonly key: string

  public constructor(key = '') {
    this.key = key
  }

  public static getInstance() {
    if (!this.redis) {
      this.redis = RedisDB.getInstance()
    }

    return this.redis
  }
  /**
     * 获取缓存值，存在会尝试使用 JSON.parse，不存在返回 null。
     * @returns {Promise<object | string | null>}
     */
  public async get() {
    const value = await RedisCacheDB.getInstance().get(this.key)

    if (value === null) {
      return null
    }

    try {
      return JSON.parse(value)
    } catch (err) {
      return value
    }
  }

  /**
     * 返回是否存在
     */
  public async exist(): Promise<Boolean> {
    return await this.get() !== null
  }

  /**
     * 查看缓存剩余过期时间
     */
  public async ttl() {
    return RedisCacheDB.getInstance().ttl(this.key)
  }

  /**
   * 设置／刷新缓存值，使用 JSON.stringify 序列化, 默认过期时间为秒，需要毫秒请设置 expiryMode 为 PX
   * @param {any} value
   * @param {number|undefined} time 过期时间
   * @param expiryMode 过期模式 [EX seconds|PX milliseconds|EXAT timestamp seconds|PXAT milliseconds-timestamp|KEEPTTL]
   */
  async set(value: RedisValueType, time?: number, expiryMode: 'EX' | 'PX' | 'EXAT' | 'PXAT' | 'KEEPTTL' = 'EX') {
    if (typeof time === 'number' && time <= 0) {
      throw new Error(`expireSec: ${time} mode: ${expiryMode} must be greater than 0`)
    }

    // EXAT 功能现有版本 Redis 暂不支持，这里实现一下
    if (time && (expiryMode === 'EXAT' || expiryMode === 'PXAT')) {
      let currentTimeStamp = new Date().getTime()
      if (expiryMode === 'EXAT') {
        currentTimeStamp = Math.floor(currentTimeStamp / 1000)
      }

      if (time <= currentTimeStamp) {
        throw new Error('过期时间必须设为大于当前时间')
      }

      time = time - currentTimeStamp
      expiryMode = expiryMode === 'EXAT' ? 'EX' : 'PX'
    }


    if (time) {
      return RedisCacheDB.getInstance().set(this.key, JSON.stringify(value), expiryMode as 'EX', time)
    } else {
      return RedisCacheDB.getInstance().set(this.key, JSON.stringify(value))
    }
  }

  /**
     * 删除缓存值
     * @returns {Promise<number>}
     */
  async del(key?: string) {
    return RedisCacheDB.getInstance().del(key || this.key)
  }

  /**
     * 原子加 1 操作
     * @returns {Promise<number>}
     */
  async incr() {
    return RedisCacheDB.getInstance().incr(this.key)
  }

  /**
     * set 增加值
     */
  public async addSet(...member: string[]) {
    return RedisCacheDB.getInstance().sadd(this.key, member)
  }

  /**
     * 查询集合内所有元素
     */
  public async getSetMembers() : Promise<any[]> {
    const members =  await RedisCacheDB.getInstance().smembers(this.key)
    return members.map((value) => {
      try {
        return JSON.parse(value)
      } catch (err) {
        return value
      }
    })
  }

  /**
   * 获取集合内元素个数
   */
  public async getSetMembersCount() : Promise<number> {
    return RedisCacheDB.getInstance().scard(this.key)
  }


  /**
     * 移除集合内的成员
     */
  public async delSet(...member: string[]) {
    return RedisCacheDB.getInstance().srem(this.key, member)
  }

  /**
     * 增或改 ZSet 值
     * @param {string} score
     * @param {number} value
     */
  public async addZSet(value: string, score: number) {
    return RedisCacheDB.getInstance().zadd(this.key, score, value)
  }

  /**
     * 查 ZSet 值
     * @param {string} score
     */
  public async getZSet(score: string) {
    return RedisCacheDB.getInstance().zscore(this.key, score)
  }

  /**
     * 查 ZSet List值
     * @param {number | string} min
     * @param {number | string} max
     * @param {string} hasScores
     * @returns {Promise<array>}
     */
  async getListZSet(min = '-inf', max = '+inf', hasScores = 'WITHSCORES') {
    let result
    if (hasScores == null) {
      result = await RedisCacheDB.getInstance().zrangebyscore(this.key, min, max)
    } else {
      result = await RedisCacheDB.getInstance().zrangebyscore(this.key, min, max, 'WITHSCORES')
    }
    return result
  }

  /**
     * 删 ZSet 值
     * @param {string} id
     */
  async delZSet(id: string) {
    return RedisCacheDB.getInstance().zrem(this.key, id)
  }

  /**
     * 删 ZSet range
     * @param {number | string} minIndex
     * @param {number | string} maxIndex
     * @returns {number} 1为成功，0为失败
     */
  async delRangeZSet(minIndex: number, maxIndex: number) {
    return RedisCacheDB.getInstance().zremrangebyrank(this.key, minIndex, maxIndex)
  }

  /**
     * 删 ZSet list
     * @returns {number} 1为成功，0为失败
     */
  async delListZSet() {
    return RedisCacheDB.getInstance().del(this.key)
  }

  /**
     * 匹配查询所有键
     * @param pattern
     * @returns
     */
  async getKeys(pattern: string) {
    return RedisCacheDB.getInstance().keys(pattern)
  }
}