import { Client } from '@elastic/elasticsearch'
import { Config } from '../../config/config'
import { Retry } from '../../lib/retry/retry'
import { AzureOpenAIEmbedding } from '../../lib/ai/llm/openai_embedding'
import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import chalk from 'chalk'

export interface IElasticEmbeddingRes {
  id: string
  pageContent: string
  metadata: Record<string, any>
  score: number
}
export class ElasticSearchClient {
  private static client: Client | undefined
  public static getInstance() {
    if (!this.client) {
      this.client = new Client ({
        node: Config.setting.elasticSearch.url,
        auth: {
          username: Config.setting.elasticSearch.username,
          password: Config.setting.elasticSearch.password
        }
      })
    }
    return this.client
  }
}


class ElasticSearchService {

  static async deleteIndex(indexName: string) {
    const indexCannotDelete = ['moer_user_memory', 'user_memory', 'moer_rag_2', 'baoshu_rules', 'budget_index', 'wealth_orchard_new', 'wealth_orchard', 'moer_non_rag_queries', 'moer_image_rag', 'moer_previous_client_info', 'fku']
    if (indexCannotDelete.includes(indexName)) {
      console.warn(chalk.redBright(`WTF are you doing? MotherFker. Index "${indexName}" cannot be deleted`))
      throw new Error(`WTF are you doing? MotherFker. Index "${indexName}" cannot be deleted`)
    }

    const client = ElasticSearchClient.getInstance()
    const indexExists = await client.indices.exists({ index: indexName })
    if (!indexExists) {
      return
    }

    await client.indices.delete({
      index: indexName
    })
  }

  static async createIndex (index: string, mappings: object) {
    try {
      const response = await ElasticSearchClient.getInstance().indices.create ({
        index,
        body: { mappings }
      })
      console.log ('Index created:', index)
      return response
    } catch (error) {
      console.error ('Error creating index:', error)
      throw error
    }
  }

  static async insertDocuments(index: string, documents: object[]) {
    try {
      const body = documents.flatMap((doc) => [{ index: { _index: index } }, doc])

      const response = await ElasticSearchClient.getInstance().bulk({ refresh: true, body })

      console.log('Documents indexed')
      return response
    } catch (error) {
      console.error('Error indexing documents:', error)
      throw error
    }
  }

  static async insertDocument (index: string, body: object) {
    try {
      const response = await ElasticSearchClient.getInstance().index ({
        index,
        body,
        refresh: true
      })

      console.log ('Document indexed')
      return response
    } catch (error) {
      console.error ('Error indexing document:', error)
      throw error
    }
  }

  static async search (index: string, query: object, size = 10, options?: object): Promise<any[]> {
    try {
      const response = await Retry.retry(3, async () => {
        return ElasticSearchClient.getInstance().search({
          index,
          body: { query, ...options },
          size: size
        })
      },  { delayFunc: (count) => count * 1000 })

      if (response.hits.hits) {
        return response.hits.hits
      }
      return []
    } catch (error) {
      console.error ('Error searching documents:', error)
      throw error
    }
  }

  static async count (index: string): Promise<number> {
    try {
      const response = await ElasticSearchClient.getInstance().count({
        index
      })

      if (response.count) {
        console.log ('Count:', response.count)
        return response.count
      }
      return 0
    } catch (error) {
      console.error ('Error counting documents:', error)
      throw error
    }
  }

  public static async embeddingSearchLangChain(index: string, query: string, k = 10, lowestScore, filter?: object): Promise<any[]> {
    const clientArgs: ElasticClientArgs = {
      client: ElasticSearchClient.getInstance(),
      indexName: index,
    }

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
    //每个 query 会被返回和自己相近的 rag 结果
    const embeddingSearchResult = await vectorStore.similaritySearchWithScore(query, k, filter)
    return embeddingSearchResult.filter(([_, score]) => score > lowestScore)
  }

  public static async embeddingSearch(
    index: string,
    query: string,
    k = 10,
    lowestScore: number = 0.6,
    filter?: object
  ): Promise<IElasticEmbeddingRes[]> {
    const embeddings = await AzureOpenAIEmbedding.getInstance().embedQuery(query)

    const searchBody = {
      knn: {
        field: 'embedding',
        query_vector: embeddings,
        num_candidates: 50,
        k: k,
        filter: filter
      }
    }

    const res = await ElasticSearchClient.getInstance().search({
      index: index,
      body: searchBody,
    })
    return res.hits.hits
      .map((hit: any): IElasticEmbeddingRes => ({
        id: hit._id,
        pageContent: hit._source.text,
        metadata: hit._source.metadata,
        score: hit._score
      }))
      .filter((result) => result.score > lowestScore)
  }

  static async describeIndex(index: string) {
    try {
      const response = await ElasticSearchClient.getInstance().indices.get({
        index: index
      })
      console.log('Index description:', JSON.stringify(response, null, 4))
      return response
    } catch (error) {
      console.error('Error describing index:', error)
      throw error
    }
  }

  private static async isIndexExists(targetIndex: string) {
    // Check if the index exists
    try {
      return await ElasticSearchClient.getInstance().indices.exists({
        index: targetIndex
      })
    } catch (error) {
      console.error('Error checking index existence:', error)
      throw error
    }
  }

  static async deleteDocuments(indexName: string, documentIds: string[]) {
    try {
      if (!documentIds || documentIds.length === 0) {
        console.log(chalk.yellow('No documents to delete.'))
        return
      }

      const isIndexExists = await this.isIndexExists(indexName)
      if (!isIndexExists) {
        console.log(chalk.red(`Index ${indexName} does not exist.`))
        return
      }

      const deleteBody = documentIds.map((id) => ({ delete: { _index: indexName, _id: id } }))

      const response = await ElasticSearchClient.getInstance().bulk({
        refresh: true,
        body: deleteBody
      })
    } catch (error) {
      console.error('Error deleting documents:', error)
      throw error
    }
  }
}

export default ElasticSearchService