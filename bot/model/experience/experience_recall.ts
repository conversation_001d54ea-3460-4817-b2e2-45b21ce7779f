import { ExtractUserSlotsV2 } from '../../service/moer/components/flow/helper/slotsExtract'
import { ContextBuilder } from '../../service/moer/components/agent/context'
import { ChatStateStore } from '../../service/moer/storage/chat_state_store'
import { ChatHistoryService, IDBBaseMessage } from '../../service/moer/components/chat_history/chat_history'
import ElasticSearchService from '../elastic_search/elastic_search'
import logger from '../logger/logger'
import { UploadExperience } from './upload_experience'
import { EventTracker, IEventType } from '../logger/data_driven'
import { isScheduleTimeAfter } from '../../service/moer/components/schedule/creat_schedule_task'
import { DataService } from '../../service/moer/getter/getData'
import { RAGHelper } from '../rag/rag'

export class ExperienceRecall {

  private static index = 'moer_experience'

  public static async recallScene(chatId: string, roundId: string) {

    const currentTime = await DataService.getCurrentTime(chatId)
    if (isScheduleTimeAfter(currentTime, { is_course_week: true, day: 2, time: '20:00:00' })) {
      return ''
    }

    let chatHistory = await ChatHistoryService.getRecentConversations(chatId, 4, 'user')
    chatHistory = chatHistory.slice(-10)
    chatHistory = ExperienceRecall.filterChatHistory(chatHistory)
    const chatHistoryStr = ChatHistoryService.formatHistoryHelper(chatHistory)
    const scene = await UploadExperience.summaryScene(chatHistoryStr, roundId)

    const userPortrait = await ContextBuilder.getCustomerPortrait(chatId)
    const validSlotCount = await ExperienceRecall.getValidSlotNum(chatId)
    const meditationExperience = ExperienceRecall.extractMeditationExperience(userPortrait)

    const filter = {
      bool: {
        must: [
          { range: { 'metadata.minValidSlotCount': { 'lte': validSlotCount } } },
          { range: { 'metadata.maxValidSlotCount': { 'gte': validSlotCount } } },
          { term: { 'metadata.scene': scene.scene } }
        ],
        should:[
          { term:{ 'metadata.meditationExperience': meditationExperience } },
          { term:{ 'metadata.meditationExperience': '' } }
        ]
      }
    }

    const searchRes =  await ElasticSearchService.embeddingSearch(
      this.index,
      scene.summary,
      1,
      0.78,
      filter
    )

    let rerankRes = await RAGHelper.reRank(scene.summary, searchRes.map((item) => {
      return {
        q: item.pageContent,
        a: item.metadata.experience || item.metadata.strategy
      }
    }), { top_n: 1, threshold: 0.6 })

    rerankRes = rerankRes.filter((item) => {
      return item.score > 0.6
    })

    if (rerankRes.length === 0) {
      return ''
    } else {
      logger.trace({ chat_id: chatId }, 'experience', JSON.stringify(rerankRes))
      logger.trace({ chat_id: chatId }, 'scene', scene)
      logger.trace({ chat_id: chatId }, 'recallChatHistory', chatHistoryStr)

      const experience = rerankRes[0].a
      EventTracker.track(chatId, IEventType.sceneExperience, { experience: JSON.stringify(rerankRes), scene: scene, recallChatHistory: chatHistoryStr })
      return `## 参考经验
${experience}`
    }

  }

  private static async getValidSlotNum(chatId: string) {

    // await ChatStatStoreManager.initState(chatId)
    const chatState = ChatStateStore.get(chatId)
    const userSlots = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)

    const validSlotKey = ['痛点', '目标', '经验', '工作']

    let validCount = 0

    //遍历userSlots
    const record = userSlots.toRecord()
    if (record) {
      Object.keys(record).forEach((key) => {
        // 处理key值
        for (const syn of validSlotKey) {
          if (key.includes(syn)) {
            validCount += record[key].frequency
          }
        }
      })
    }
    return validCount
  }

  private static filterChatHistory(chatHistory: IDBBaseMessage[]) {

    chatHistory = chatHistory.slice(1, chatHistory.length)

    const maxMsgLength = 60

    // 过滤聊天间隔超过60min的对话
    for (let i = chatHistory.length - 2; i > 0;i--) {
      if (chatHistory[i].created_at.getTime() - chatHistory[i + 1].created_at.getTime() > 60 * 60 * 1000) {
        chatHistory = chatHistory.slice(i, chatHistory.length)
        break
      }
    }

    chatHistory =  chatHistory.map((message) => {
      if (message.role === 'assistant' && message.content.length >  maxMsgLength && message.short_description) {
        message.content = message.short_description
      }
      return message
    })

    return chatHistory.filter((message) => {
      return !(message.role === 'assistant' && message.content.length > maxMsgLength)
    })
  }

  private static extractMeditationExperience(text: string) {
    const meditation_experience_syn = {
      '纯小白': ['小白'],
      '接触过': ['接触', '初学', '有经验'],
      '有基础': ['基础']
    }


    for (const [key, keywords] of Object.entries(meditation_experience_syn)) {
      if (keywords.some((keyword) => text.includes(keyword))) {
        return key
      }
    }
    return ''

  }

  private static extractPainPoints(text: string): {
    stressAndAnxiety: boolean;
    sleepProblems: boolean;
    lowEnergyAndPoorMentalState: boolean;
    emotionalManagement: boolean;
    physicalHealthProblems: boolean;
    lackOfPersonalGrowthDirection: boolean;
    economicSituation: boolean;
    lackOfFocus: boolean;
    desireForSelfImprovement: boolean
  } {

    const pain_syns = {
      '压力和焦虑': ['压力', '焦虑', '烦', '崩溃', '紧绷', '紧张', '内耗'],
      '睡眠问题': ['睡眠', '睡不好', '失眠', '睡不着', '安眠药', '更年期', '睡眠问题', '多梦', '入睡困难'],
      '能量低和精神状态差': ['精神', '疲', '累', '倦', '没劲', '无力', '精神不好', '精神状态', '不振', '能量', '萎靡'],
      '不会管理情绪': ['情绪', '易怒', '伤心', '哭', '压抑', '生气', '烦', '开心', '快乐'],
      '身体健康问题': ['痛', '疼', '酸', '麻', '晕', '胀', '病', '不适', '身体', '身心', '健康', '虚', '闷', '不舒服'],
      '缺少个人成长和自我探索方向感': ['认知', '方向', '成长', '目标', '意义', '价值', '迷茫', '探索', '追求', '困惑', '自我', '真我', '逃避'],
      '经济状况': ['财', '钱', '经济', '贫', '穷', '债'],
      '专注力不够': ['专注', '注意力', '专心', '浮躁'],
      '渴望塑己利他': ['变好', '提升', '优秀', '改变', '进步', '疗愈', '帮助', '传递', '指导']
    }

    const painPointMap = {
      '压力和焦虑': 'stressAndAnxiety',
      '睡眠问题': 'sleepProblems',
      '能量低和精神状态差': 'lowEnergyAndPoorMentalState',
      '不会管理情绪': 'emotionalManagement',
      '身体健康问题': 'physicalHealthProblems',
      '缺少个人成长和自我探索方向感': 'lackOfPersonalGrowthDirection',
      '经济状况': 'economicSituation',
      '专注力不够': 'lackOfFocus',
      '渴望塑己利他': 'desireForSelfImprovement'
    }


    const result = {
      stressAndAnxiety: false,
      sleepProblems: false,
      lowEnergyAndPoorMentalState: false,
      emotionalManagement: false,
      physicalHealthProblems: false,
      lackOfPersonalGrowthDirection: false,
      economicSituation: false,
      lackOfFocus: false,
      desireForSelfImprovement: false
    }

    for (const [key, synonyms] of Object.entries(pain_syns)) {
      for (const word of synonyms) {
        if (text.includes(word)) {
          const field = painPointMap[key as keyof typeof painPointMap]
          if (field) {
            result[field] = true
          }
        }
      }
    }
    return result
  }

}

