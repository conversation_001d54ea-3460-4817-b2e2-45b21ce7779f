import { ChatHistoryTool } from '../../../../test/tools/chat_history_tool'
import { ChatHistoryService, IDBBaseMessage } from '../../../service/moer/components/chat_history/chat_history'
import { AzureOpenAIEmbedding } from '../../../lib/ai/llm/openai_embedding'
import { cosineSimilarity } from '../../../lib/text/text_similarity'
import ElasticSearchService from '../../elastic_search/elastic_search'
import { FileHelper } from '../../../lib/file'
import { RAGHelper } from '../../rag/rag'
import { ExperienceRecall } from '../experience_recall'
import { UUID } from '../../../lib/uuid/uuid'
import { IScheduleTime, isScheduleTimeAfter } from '../../../service/moer/components/schedule/creat_schedule_task'


describe('Experience Recall', () => {

  it('聊天场景匹配测试', async () => {
    const chatId1 = '7881300699060567_1688857949631398'
    const userMsg1 = '没有，就是好奇，不知道这是什么东西，想谅解一下，没什么其它想法，'
    const chatId2 = 'local_7881300699060567'
    const userMsg2 = '是刚才看到你们的视频了就点进去了'


    let chatHistory1 = await ChatHistoryTool.getChatHistoryByMsg(chatId1, userMsg1, 3, 10)
    let chatHistory2 = await ChatHistoryTool.getChatHistoryByMsg(chatId2, userMsg2, 3, 10)

    chatHistory1 = filterChatHistory(chatHistory1)
    chatHistory2 = filterChatHistory(chatHistory2)

    const chatHistoryStr1 = ChatHistoryService.formatHistoryHelper(chatHistory1)
    const chatHistoryStr2 = ChatHistoryService.formatHistoryHelper(chatHistory2)

    const embeddings1 = await AzureOpenAIEmbedding.getInstance().embedQuery(chatHistoryStr1)
    const embeddings2 = await AzureOpenAIEmbedding.getInstance().embedQuery(chatHistoryStr2)

    console.log(cosineSimilarity(embeddings1, embeddings2))

  }, 9e8)

  it('测试召回', async () => {

    const chatId1 = '7881299606170424_1688856322643146'
    const userMsg1 = '挺好'

    const chatHistory1 = await ChatHistoryTool.getChatHistoryByMsg(chatId1, userMsg1, 3, 10)

    ChatHistoryService.getRecentConversations = async (chat_id: string, rounds: number, role: 'user' | 'assistant' = 'user') => {
      return chatHistory1
    }

    const res = await ExperienceRecall.recallScene('7881299606170424_1688856322643146', UUID.v4())

    console.log(res)
  }, 9e8)

  it('召回批量测试', async () => {

    const data = await FileHelper.readFile('/Users/<USER>/Desktop/code/wechaty_bot/bot/model/experience/data/scene_exp_list.json')
    const sceneList = JSON.parse(data)
    const resList: any[] = []

    for (const scene of sceneList) {
      const filter = {
        bool: {
          must: [
            { term: { 'metadata.scene': scene.scene } }
          ]
        }
      }

      const searchRes =  await ElasticSearchService.embeddingSearch(
        'moer_experience',
        scene.summary,
        5,
        0.78,
        filter
      )

      if (searchRes.length === 0) {
        continue
      }

      const searchResQA = searchRes.map((item) => {
        return {
          q: item.pageContent,
          a: ''
        }
      })

      const rerankRes = await RAGHelper.reRank(scene.summary, searchResQA, { top_n: 10, threshold: 0.6 })

      const res: any = {}
      res.rerankRes = rerankRes.map((item) => {
        return {
          q: item.q,
          score: item.score
        }
      })
      res.summary = scene.summary
      res.match = rerankRes[0].q

      resList.push(res)

    }

    await FileHelper.writeFile('/Users/<USER>/Desktop/code/wechaty_bot/bot/model/experience/data/scene_exp_list_res.json', JSON.stringify(resList))

  }, 9e8)

})



function filterChatHistory(chatHistory: IDBBaseMessage[]) {
  return chatHistory.filter((message) => {
    return !(message.role === 'assistant' && message.content.length > 15)

  })
}