import { IChatExperience, UploadExperience } from '../upload_experience'
import { ChatHistoryService, IDBBaseMessage } from '../../../service/moer/components/chat_history/chat_history'
import ElasticSearchService from '../../elastic_search/elastic_search'
import { ChatHistoryTool } from '../../../../test/tools/chat_history_tool'


const experience: any[] = [
  {
    chatId: '7881299606170424_1688856322643146',
    chatHistoryKey: '挺好',
    minValidSlotCount: 0,
    maxValidSlotCount: 999,
    meditationExperience: '纯小白',
    experience: '当客户练习冥想后表示冥想感受不错时，可以具体询问客户哪里感受不错，冥想感受最好或者印象最深的时刻'
  },
  {
    chatId: '7881299606170424_1688856322643146',
    chatHistoryKey: '受外界影响大',
    minValidSlotCount: 0,
    maxValidSlotCount: 999,
    meditationExperience:'',
    experience: '客户正在分享自己学习冥想的目标或面临的痛点，这时可以\n- 基于客户的回应提出开放式问题，鼓励客户进一步阐述\n- 通过层层递进的提问，深入挖掘细节\n- 避免假设或过早给出建议，保持探索和专业性。'
  },
  {
    chatId: '7881300116965276_1688856297674847',
    chatHistoryKey: '还有1张表是吗',
    minValidSlotCount: 0,
    maxValidSlotCount: 999,
    meditationExperience:'',
    experience: '客户在看完小讲堂之后索要能量测评，说明客户小讲堂看的很认真，这时可以夸奖客户看课很认真，然后回复“加一个测评表您测完发给我，我帮您解析”（埋下和客户后续互动的钩子）'
  },
  {
    chatId: '7881300116965276_1688856297674847',
    chatHistoryKey: '呼吸是鼻吸鼻呼还是鼻吸口呼呀老师？',
    minValidSlotCount: 0,
    maxValidSlotCount: 999,
    meditationExperience: '纯小白',
    experience: '客户正在咨询冥想练习问题，可以基于客户当前话题深入探讨冥想感受，帮助客户更好的进入冥想状态'
  },
  {
    chatId: '7881300699060567_1688857949631398',
    chatHistoryKey: '是刚才看到你们的视频了就点进去了？',
    minValidSlotCount: 0,
    maxValidSlotCount: 999,
    meditationExperience:'',
    experience: '客户仅因为对冥想好奇而加入学习，可以询问客户被冥想的什么吸引了，氛围还是声音'
  }
]


describe('UploadExperience', () => {

  it('create elastic search index', async () => {
    const indexConfig = {
      'properties': {
        'embedding': { 'type': 'dense_vector', 'index': true, 'dims': 2048 },
        'metadata.job': { 'type': 'keyword' },
        'metadata.minValidSlotCount': { 'type': 'integer' },
        'metadata.maxValidSlotCount': { 'type': 'integer' },
        'metadata.meditationExperience': { 'type': 'keyword' },
        'metadata.experience': { 'type': 'text' },
        'metadata.scene': { 'type': 'keyword' },
        'metadata.chatHistory': { 'type': 'text' },
        // 'scene': { 'type': 'text', 'analyzer': 'ik_max_word', 'search_analyzer': 'ik_smart' }
      }
    }
    await ElasticSearchService.createIndex('moer_experience', indexConfig)
  }, 9e8)

  it('should upload experience', async () => {

    const exps: IChatExperience[] = []

    for (const item of experience) {
      const chatHistory = await getChatHistoryStr(item.chatId, item.chatHistoryKey)
      exps.push({
        chatHistory: chatHistory,
        job: item.job,
        minValidSlotCount: item.minValidSlotCount,
        maxValidSlotCount: item.maxValidSlotCount,
        meditationExperience: item.meditationExperience,
        experience: item.experience
      })
    }

    await UploadExperience.uploadChatExperience(exps)
  }, 9e8)
})

async function getChatHistoryStr(chatId: string, message: string) {
  let chatHistory = await ChatHistoryTool.getChatHistoryByMsg(chatId, message, 4, 10)
  chatHistory = filterChatHistory(chatHistory).filter((msg) => msg !== undefined)

  return ChatHistoryService.formatHistoryHelper(chatHistory)
}

function filterChatHistory(chatHistory: IDBBaseMessage[]) {

  chatHistory = chatHistory.slice(1, chatHistory.length)

  // 过滤聊天间隔超过60min的对话
  for (let i = 0; i < chatHistory.length - 1;i++) {
    if (chatHistory[i].created_at.getTime() - chatHistory[i + 1].created_at.getTime() > 60 * 60 * 1000) {
      chatHistory = chatHistory.slice(i, chatHistory.length)
      break
    }
  }

  chatHistory =  chatHistory.map((message) => {
    if (message.role === 'assistant' && message.content.length > 60 && message.short_description) {
      message.content = message.short_description
    }
    return message
  })

  return chatHistory.filter((message) => {
    return !(message.role === 'assistant' && message.content.length > 40)
  })
}