import { Config } from '../../config/config'
import { WorkFlow } from '../moer/components/flow/flow'
import { ChatHistoryService } from '../moer/components/chat_history/chat_history'
import { MessageSender } from '../moer/components/message/message_send'
import { Chat, ChatDB } from '../moer/database/chat'
import { getChatId, getUserId } from '../../config/chat_id'
import { ChatStatStoreManager } from '../moer/storage/chat_state_store'
import { HumanTransfer, HumanTransferType } from '../moer/components/human_transfer/human_transfer'
import { InterruptError } from '../moer/components/message/interrupt_handler'
import chalk from 'chalk'
import { isPastUser } from '../moer/components/flow/helper/pastUser'
import logger from '../../model/logger/logger'
import { PrismaMongoClient } from '../../model/mongodb/prisma'
import { sleep } from '../../lib/schedule/schedule'
import { SendWelcomeMessage } from '../moer/components/flow/schedule/task/sendWelcomeMessage'
import { TaskName } from '../moer/components/flow/schedule/type'
import { clearTasks, startTasks } from '../moer/components/flow/schedule/task_starter'
import { DataService } from '../moer/getter/getData'
import { isScheduleTimeAfter } from '../moer/components/schedule/creat_schedule_task'
import { GroupNotification } from '../moer/notification/group'


export class MessageReplyService {

  public static async reply(text: string[], senderId: string) {
    const chat_id = getChatId(senderId)
    const chat = await ChatDB.getById(chat_id) as Chat
    let contactName = chat_id
    if (chat && chat.contact && chat.contact.wx_name) { contactName = chat.contact.wx_name }

    try {
      if (!text.join('').trim()) { return }

      if (await isPastUser(senderId)) { return }

      const userMessage = text.join('\n')

      if (userMessage.toLowerCase() === 'clear') {
        await MessageSender.sendById({
          user_id: senderId,
          chat_id: chat_id,
          ai_msg: '聊天已重置'
        })
        await ChatHistoryService.clearChatHistory(chat_id)
        await ChatStatStoreManager.clearState(chat_id)
        if (chat) {
          await ChatDB.setHumanInvolvement(chat_id, false)
          // 移除掉 moerId
          await ChatDB.removeMoerId(chat_id)
        }

        await clearTasks(chat_id)
        await startTasks(getUserId(chat_id), chat_id, true)

        await new SendWelcomeMessage().process({
          userId: senderId,
          chatId: chat_id,
          name: TaskName.SendWelcomeMessage
        })

        await DataService.saveChat(chat_id, senderId)
        return
      }

      if (Config.setting.onlyReceiveMessage) { // mode
        console.log(chalk.redBright('仅接收消息模式'))
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        return
      }

      const currentTime = await DataService.getCurrentTime(chat_id)

      if (senderId === '7881301574190152' || (isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' }) && !Config.setting.localTest)) { // 上课周 周三 8点后通知
        GroupNotification.notifyUserReply(senderId, userMessage)
      }

      // 添加聊天日志
      if (await ChatDB.isHumanInvolvement(chat_id)) { // 人工参与，则不进行回复
        logger.log({ chat_id }, '联系人交给人工处理')
        // 存储下客户消息
        await ChatHistoryService.addUserMessage(chat_id, userMessage)
        await GroupNotification.notify(`${contactName} 客户AI未开启，请人工处理
客户说：${userMessage}`)
        return
      }

      // 下面开始处理消息
      if (userMessage.length >= 100) { // 客户发送的消息比较长的话，mock 一下人工阅读的时间
        await sleep(10 * 1000) // 延缓输出
      }

      await ChatStatStoreManager.initState(chat_id)

      await WorkFlow.step(chat_id, senderId, userMessage)

      const userMsgCount = await ChatHistoryService.getUserMessageCount(chat_id)
      if (userMsgCount == 1) {
        await MessageReplyService.countUserMsgGreaterThanOne(chat_id)
      }
    } catch (e) {
      if (!(e instanceof InterruptError)) {
        try {
          if (e instanceof Error) {
            // 将错误的堆栈信息打印到自定义 logger 中
            logger.error(`Error: ${e.message}\nStack Trace: ${e.stack}`)
          } else {
            // 如果 e 不是 Error 实例，捕获当前的堆栈信息
            const stack = new Error().stack
            logger.error(`消息回复失败: ${stack}`)
          }
          await HumanTransfer.transfer(chat_id, senderId, HumanTransferType.MessageSendFailed)
        } catch (e) {
          logger.error('转人工失败', e)
        }
      }
    } finally {
      try {
        if (!await isPastUser(senderId)) {
          await DataService.saveChat(chat_id, senderId) // 注意，出错了，客户也要创建
        }
      } catch (e) {
        console.error(e)
        logger.error('创建客户失败', senderId, e)
      }
    }
  }


  /**
   * 存储客户消息大于 1 的个数
   * @param chat_id
   */
  public static async countUserMsgGreaterThanOne(chat_id: string) {
    try {
      const userCount = await PrismaMongoClient.getInstance().user_count.findUnique({ where: { id: chat_id } })
      if (!userCount) {
        await PrismaMongoClient.getInstance().user_count.create({
          data: {
            id: chat_id,
            count: 1,
            created_at: new Date()
          }
        })
      }
    } catch (e) {
      logger.error('Chat ID重复:', e) // 日志记录错误信息
    }
  }
  //
  // public static async sendEmoticon(from_user: string, emojiType: string) {
  //   const chat_id = getChatId(from_user)
  //   const emoticonUrl = await EmoticonHelper.getEmoticon(emojiType, chat_id)
  //
  //   await WechatMessageSender.sendById({
  //     user_id: from_user,
  //     chat_id: from_user + Config.setting.wechatConfig?.id,
  //     ai_msg: `[[表情：[${  emojiType  }]]]`,
  //     send_msg: {
  //       type: IWecomMsgType.Emoticon,
  //       imageUrl: emoticonUrl
  //     }
  //   })
  // }
}