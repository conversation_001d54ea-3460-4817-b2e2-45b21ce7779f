import { IReceivedChatHistoryMsg, IReceivedMessage } from '../../lib/juzi/type'
import { FileHelper } from '../../lib/file'
import path from 'path'

let count = 0

export async function recordChatHistory(message: IReceivedMessage) {
  const payload = message.payload as IReceivedChatHistoryMsg
  const names = payload.content.split('和')
  const startTime = payload.chatHistoryList[0].time
  const endTime = payload.chatHistoryList[payload.chatHistoryList.length - 1].time

  // 构建 对象，写入文件
  const obj = {
    name: payload.content,
    chatHistory: payload.chatHistoryList,
    counselor: names[1],
    user: names[0],
    startTime: startTime,
    startTimeStr: new Date(startTime * 1000).toLocaleString(),
    endTime: endTime,
    endTimeStr: new Date(endTime * 1000).toLocaleString(),
  }

  count++
  await FileHelper.writeFile(`${path.join(__dirname, 'historys', `${count}.json`)}`, JSON.stringify(obj, null, 4))
}