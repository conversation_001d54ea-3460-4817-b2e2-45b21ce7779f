import { Config } from '../../../config/config'
import { DataService } from '../../moer/getter/getData'
import { sleep } from '../../../lib/schedule/schedule'
import logger from '../../../model/logger/logger'
import { AsyncLock } from '../../../lib/lock/lock'
import { calculateJaccardSimilarity } from '../../../lib/text/text_similarity'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      id: '1688855135509802',
      botUserId: 'momoLiaoLiuXue',
      name: '暴叔',
      notifyGroupId: '',

      classGroupId: 'x',
      courseNo: 1
    }
  })

  it('123sadas', async () => {
    const lock = new AsyncLock() // 等待主线回复完再执行

    await lock.acquire('fk', async () => {
      logger.error('fk')
      // await sleep(3 * 1000)
      // console.log('fk1')
    })

    lock.acquire('fk', async () => {
      await sleep(3 * 1000)
      console.log('fk2')
    })

    await sleep(7 * 1000)

  }, 60000)

  it('should pass', async () => {
    await DataService.saveChat('7881302136112713_1688855025632783', '7881302136112713')
  }, 60000)

  it('123', async () => {
    //     console.log(await HomeworkTemplate.matchDay2Template('', `同学可以分享自己财富果园的画面，发在【班级群】里，因为每个人都不一样，后续我会逐一解读哦。
    // 比如一:大门新旧日、材质、颜色:有个芦苇的弓形门，下面有个及腰的木门，往内推，颜色就是自然的原木色，大门不新也不旧。
    // 有无围栏:有围栏，是种的及腰的植物当的栅栏。
    // 主树是什么:主树是银杏周围的树是什么:周围是苹果树和橙子树。
    // 秋天动作、有无变现:秋天有收拾果实，没有变现。
    // 呼吸顺畅吗:顺畅。
    // 四季循环变化怎么样:很清晰，春天给它施肥 夏天给遮阳 秋天捡果子 冬天保护树干`))

    const simimlar = calculateJaccardSimilarity(`同学可以分享自己财富果园的画面，发在【班级群】里，因为每个人都不一样，后续我会逐一解读哦。
    比如一:大门新旧日、材质、颜色:有个芦苇的弓形门，下面有个及腰的木门，往内推，颜色就是自然的原木色，大门不新也不旧。
    有无围栏:有围栏，是种的及腰的植物当的栅栏。
    主树是什么:主树是银杏周围的树是什么:周围是苹果树和橙子树。
    秋天动作、有无变现:秋天有收拾果实，没有变现。
    呼吸顺畅吗:顺畅。
    四季循环变化怎么样:很清晰，春天给它施肥 夏天给遮阳 秋天捡果子 冬天保护树干`, `同学可以分享自己财富果园的画面，发在【班级群】里，因为每个人都不一样，后续我会逐一解读哦。

比如👉：
大门新旧、材质、颜色：
有无围栏：
主树是什么：
周围的树是什么：
秋天动作、有无变现：
呼吸顺畅吗：
四季循环变化怎么样：`)
    console.log(simimlar)
  }, 60000)
})