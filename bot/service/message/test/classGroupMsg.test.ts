import { Config } from '../../../config/config'
import { MessageSender } from '../../moer/components/message/message_send'
import { IWecomMsgType } from '../../../lib/juzi/type'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      notifyGroupId: 'R:10829337560927503',
      classGroupId: 'xx',
      courseNo: 1
    }
  })
  it('测试语音', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881300846030208',
      ai_msg: 'asdas',
      send_msg:  {
        type: IWecomMsgType.Voice,
        voiceUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/ocr_test/test.silk',
        duration:7
      }
    })

  }, 30000)

  it('测试发送图片', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881302505051440',
      ai_msg: 'asdas',
      send_msg:  {
        type: IWecomMsgType.Image,
        url: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/1d620350-327c-4640-96da-5380955c6903/0b71bae6-1970-4aad-8ed1-bdedb72f26ed.jpg'
      }
    })

  }, 30000)

  it('测试发送表情包', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881302505051440',
      ai_msg: 'asdas',
      send_msg:  {
        type: IWecomMsgType.Image,
        url: 'https://wework.qpic.cn/wwpic3az/wwwx_d3e41be6580c7fb201c5a855ae32dc94/0'
      }
    })
  }, 30000)

  it('测试发送卡片', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881302505051440',
      ai_msg: 'asdas',
      send_msg:  {
        type: IWecomMsgType.Link,
        sourceUrl: 'http://mp.weixin.qq.com/s?__biz=MzUzNzU3Njg3MQ==&mid=2247544871&idx=4&sn=6f53fbe943bc1f541733a37c05bea165&chksm=fae6a4efcd912df938087c7b41c8bb027faaabc271c1290bd7e166598ac3158eeebce1f14d72&mpshare=1&scene=1&srcid=1010GYnj0kIBt7hEDeBQghU1&sharer_shareinfo=f166ba12cf6beadfece3669c954b7788&sharer_shareinfo_first=faaaf0d5b2f9e586e9a8302b576d3bf8#rd',
        title: 'More·新手冥想指南｜科学冥想技巧，常见问题解答',
        summary: '九大板块专业指导，新手冥想入门手册。',
        imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/56a85890-5fcc-4f03-8434-45544f4acbbc/9c72a0b6-885e-401a-8aad-7eea39f486cc.jpg'
      }
    })
  }, 30000)

  it('测试发送文字', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881302505051440',
      ai_msg: 'asdas',
      send_msg:  {
        type: IWecomMsgType.Text,
        text: '正式课在下周一晚上8点开始上课的，没有学习过小讲堂的同学，今天记得抽10分钟完成小讲堂学习哈，要抓紧了哦，开营前要学习完~ ，对后续吸收老师讲得内容很有帮助哈！\n\n《小讲堂》听课链接👇'
      }
    })
  }, 30000)

  it('测试发送视频', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881302505051440',
      ai_msg: 'asdas',
      send_msg:  {
        type: 13,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/class_group_files/m9BxDWryHbkYhxkMJ72poe/1730779064641_434.mp4'
      }
    })
  }, 30000)

  // it('测试json写入', async () => {
  //   const scriptMsg:ISendMedia | ISendText<string> = { 'description':'',
  //     'content':'同学们早上好，本次【5天冥想入门营】班级群已经建立好，记得留意一下群信息喔😊 @所有人 ' }
  //   await GlobalMessageHandlerService.appendMessageToJsonFile(scriptMsg)
  //
  // }, 30000)

})