/**
 * 注意需要持久化的变量不要用这个类，需要持久化的请考虑使用 ChatStateStore 中的 State 是否可以满足需求。
 * ChatState 存储的变量只会存储到内存中，每次重启会丢失。只适合用于临时存储。
 */
export class ChatState<T> {
  private _state: Map<string, T>

  constructor() {
    this._state = new Map<string, T>()
  }

  public has(chat_id: string): boolean {
    return this._state.has(chat_id)
  }

  public get(chat_id: string): T | undefined {
    return this._state.get(chat_id)
  }

  public set(chat_id: string, value: T) {
    this._state.set(chat_id, value)
  }

  public delete(chat_id: string) {
    this._state.delete(chat_id)
  }

}