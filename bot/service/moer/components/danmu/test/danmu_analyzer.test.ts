import { DanmuAnalysisResultListToChatHistoryWithRoleAndDate } from '../danmu_analyzer'

describe('测试danmu相关功能呢', () => {
  test('测试弹幕分析结果转历史记录', () => {
    expect(DanmuAnalysisResultListToChatHistoryWithRoleAndDate([{
      question:'你是谁',
      answers:['我是1', '我是2']
    }])).toEqual([
      {
        role: 'assistant',
        date: '',
        message: '你是谁'
      },
      {
        role: 'user',
        date: '',
        message: '我是1'
      },
      {
        role: 'user',
        date: '',
        message: '我是2'
      },
    ])

  })
})