import { <PERSON>mu<PERSON>nal<PERSON><PERSON> } from '../danmu_analyzer'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../danmu'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const qas = await DanmuAnalyzer.analyzeDanmu('977300', '5405060', 1)

    await (<PERSON><PERSON><PERSON><PERSON><PERSON> as any).updateMemory('1', qas, 1)

    // console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(46), null, 4))
    // const rawDanmus = await DanmuDB.getDanmusByMoerIdAndLiveId('977300', '5405070')
    // const rawDanmus = await DanmuDB.getDanmusByMoerIdAndLiveId('977300', '5405074')
  }, 60000)
})