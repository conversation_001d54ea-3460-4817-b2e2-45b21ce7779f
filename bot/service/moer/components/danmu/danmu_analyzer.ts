import { Dan<PERSON>, DanmuDB } from '../../database/danmu'
import { DateHelper } from '../../../../lib/date/date'
import dayjs from 'dayjs'
import { RegexHelper } from '../../../../lib/regex/regex'
import { ChatHistoryWithRoleAndDate } from '../flow/helper/slotsExtract'

interface QuestionTemplate {
    time: number
    question: string
}

export interface DanmuAnalysisResult {
    question: string
    answers: string[]
}

export function DanmuAnalysisResultListToChatHistoryWithRoleAndDate(danmu: DanmuAnalysisResult[]): ChatHistoryWithRoleAndDate[] {
  const res:ChatHistoryWithRoleAndDate[] = []
  for(const singleDanmu of danmu) {
    res.push({
      role:'assistant',
      message:singleDanmu.question,
      date:''
    })
    for(const ans of singleDanmu.answers) {
      res.push({
        role:'user',
        message:ans,
        date:''
      })
    }
  }
  return res
}

export class DanmuAnalyzer {
  // 静态常量，存储不同天数的问题列表
  private static readonly QUESTIONS_BY_DAY = {
    1: [
      { time: 5, question: '你当前想要解决的问题是什么?' },
      { time: 9, question: '你觉得冥想的作用是什么?' },
      { time: 13, question: '心里当前的烦恼和卡点是什么？' },
      { time: 21, question: '会想起来谁会让你觉得心里有点卡。' },
      { time: 31, question: '练习冥想多久，主要练习得是单盘还是双盘。' },
      { time: 64, question: '最大的压力是来自于谁？' },
      { time: 67, question: '身体上不舒服（痛）的点' },
    ],
    2: [
      { time: 1, question: '对冥想的第一感觉，初体验是什么?' },
      { time: 4, question: '当下的财富卡点是什么？' },
      { time: 17, question: '你正在当下最感兴趣的、最关注、最在意的是什么？' },
      { time: 62, question: '每个月最大的一笔花销是用在什么地方？' },
      { time: 66, question: '练习冥想完成跃迁的时候，对冥想的感觉是什么？' },
    ],
    3: [
      { time: 26, question: '你有没有觉得你的爸妈、伴侣、孩子有什么问题？' },
      // { time: 33, question: '有谁在重男轻女的环境下长大的，扣个1？' },
      { time: 51, question: '有没有同学想系统的学习冥想课？' },
      { time: 123, question: '这次的冥想营大家感觉到有收获吗？' },
      { time: 136, question: '如果现在结营了，你们舍不舍得呀？' },
      { time: 140, question: '今天还没来得及教的呼吸测试，有想学的同学吗？' },
    ],
    4: []
  }

  // 静态常量，存储要移除的固定短语
  private static readonly IGNORED_PHRASES: string[] = [
    '老师好', '我是', '我也是', '火神爷爷', '冰雪公主',
    '结果之后还有结果', '谢谢老师', '感恩老师', '我是学霸',
    '感恩爱你', '感恩我爱你', '不见不散', '我是墨宝', '再见', '你好',
    '价值选择', '全方位富足', '感谢',
    '签到', '领导力时刻', '做对选择', '更好的做自己', '禅悟',
    '自律', '愿景必达', '大自在', '重要', '痛苦有用',
    '全息视角', '念头不等于事实', '不怕念起，只怕觉迟', '觉禅',
    '死磕冥想', '思言行', '下对订单', '红靴子', '英雄之旅',
  ]

  private static readonly PRIVACY_FILTER: string[] = [ // 过滤掉隐私信息
    '卵巢', '子宫', '阴道', '乳腺', '乳头',  '睾丸', '阴囊'
  ]

  static getCourseStartTime(weekday: number): Date { return dayjs()
    .day(weekday)
    .hour(20)
    .minute(0)
    .second(0)
    .millisecond(0)
    .toDate()
  }

  static async analyzeDanmu(userId: string, liveId: string, day: number): Promise<DanmuAnalysisResult[]> {
    // 获取原始弹幕数据
    const rawDanmus = await DanmuDB.getDanmusByMoerIdAndLiveId(userId, liveId)

    if (!rawDanmus.length) {
      return []
    }

    // 获取当天的问题模板
    const questionTemplates = this.QUESTIONS_BY_DAY[day]
    if (!questionTemplates) {
      throw new Error(`没有为第${day} 天定义问题模板`)
    }

    // 处理并分析弹幕
    return this.processDanmuAnalysis(rawDanmus as Danmu[], questionTemplates, day)
  }

  private static processDanmuAnalysis(
    rawDanmus: Danmu[],
    questionTemplates: QuestionTemplate[],
    day: number
  ): DanmuAnalysisResult[] {
    const analysisResults: DanmuAnalysisResult[] = []
    const courseStartTime = this.getCourseStartTime(day)

    for (const questionTemplate of questionTemplates) {
      const { time, question } = questionTemplate
      const answers = this.extractRelevantAnswers(
        rawDanmus,
        courseStartTime,
        time
      )

      if (answers.length) {
        analysisResults.push({
          question,
          answers
        })
      }
    }

    return analysisResults
  }

  /**
   * 提取在问题时间范围内的弹幕，返回 Q -> As 对
   * @param rawDanmus
   * @param courseStartTime
   * @param questionTimeInMinutes
   * @private
   */
  private static extractRelevantAnswers(
    rawDanmus: Danmu[],
    courseStartTime: Date,
    questionTimeInMinutes: number
  ): string[] {
    const uniqueAnswers = new Set<string>()

    for (const danmu of rawDanmus) {
      // 过滤弹幕
      if (this.shouldIgnoreDanmu(danmu.content)) continue

      const danmuTime = new Date(danmu.time)
      const minuteDifference = DateHelper.diff(courseStartTime, danmuTime, 'minute')

      // 检查是否在问题的时间范围内（后3分钟）
      if (
        minuteDifference >= questionTimeInMinutes &&
                minuteDifference < questionTimeInMinutes + 3
      ) {

        // 弹幕做下处理替换
        const danmuContent = DanmuAnalyzer.processDanmuContent(danmu.content)
        uniqueAnswers.add(danmuContent)
      }
    }

    return Array.from(uniqueAnswers)
  }

  private static shouldIgnoreDanmu(content: string): boolean {
    return this.IGNORED_PHRASES.includes(content) ||
            RegexHelper.isNumericString(content) ||
        this.PRIVACY_FILTER.some((phrase) => content.includes(phrase))
  }

  private static processDanmuContent(content: string) {
    return content.replaceAll('冰雪公主', '内伤型，把委屈憋在心里').replaceAll('火神爷爷', '爆炸愤怒型，把愤怒发泄出去')
      .replaceAll('冰雪', '内伤型，把委屈憋在心里')
      .replaceAll('火神', '爆炸愤怒型，把愤怒发泄出去')
  }
}