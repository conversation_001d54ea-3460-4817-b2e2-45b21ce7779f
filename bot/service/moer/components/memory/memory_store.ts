import { ChatHistoryService, IDBBaseMessage } from '../chat_history/chat_history'
import { LLM } from '../../../../lib/ai/llm/LLM'
import logger from '../../../../model/logger/logger'
import { Document } from 'langchain/document'
import { AzureOpenAIEmbedding } from '../../../../lib/ai/llm/openai_embedding'
import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import { MemoryMergePromptOnline, MemorySummaryPromptOnline } from '../../prompt/moer/userMemorySummary'
import { ExtractUserSlotsV2 } from '../flow/helper/slotsExtract'
import { getUserId } from '../../../../config/chat_id'
import ElasticSearchService, { ElasticSearchClient } from '../../../../model/elastic_search/elastic_search'
import { Config } from '../../../../config/config'
import dayjs from 'dayjs'

interface ChatSessionGroup {
    content: string[]
    lastMessageTimestamp: Date
}

interface FormattedSummarizedMemory {
    chat_id: string
    summary: string
    timestamp: string
}

interface IMemoryMetaData {
  chat_id: string
  summary: string
  timestamp: string // ISO 时间字符串
}

/**
 * 每5轮一次，将该客户近10轮聊天与历史memory送入【总结LLM】，让模型判断是否需要归纳一条新的memory入库，
 * 若不需要，则不处理，反之，将归纳好的memory入库
 */
export class MemoryStore {
  public static indexName = 'moer_user_memory_2048d'

  /**
   * 每一次客户说话都判断是否要取前10轮的聊天记录，如果要，就取出来
   * @param chat_id
   */
  public static async processChatHistory(chat_id: string): Promise<ChatSessionGroup> {
    const fullChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat_id)

    // 从最新消息开始，收集最近的10轮对话
    const rounds: IDBBaseMessage[][] = []
    let currentRound: IDBBaseMessage[] = []
    let roundCount = 0
    let assistantCount = 0

    // 从后往前，增加聊天记录。每轮 AI 的话，最多收集 5个
    for (let i = fullChatHistory.length - 1; i >= 0 && roundCount < 10; i--) {
      const message = fullChatHistory[i]
      //currentRound.unshift(message)

      if (message.role === 'user' && i !== fullChatHistory.length - 1) {
        rounds.unshift(currentRound)
        currentRound = []
        roundCount++
        assistantCount = 0
      }
      if (message.role === 'assistant') {
        assistantCount++
        if (assistantCount <= 5) {
          currentRound.unshift(message)
        }
      } else {
        currentRound.unshift(message)
      }
    }

    // 如果最后一轮不完整，也将其添加
    if (currentRound.length > 0) {
      rounds.unshift(currentRound)
    }

    // 处理收集到的轮次
    const processedRounds = rounds.map((round) => {
      const formattedMessages = round.map((message) => {
        return  `${message.role ===  'assistant' ? Config.setting.AGENT_NAME : '客户'}: ${message.content}`
      }
      )
      return formattedMessages.join('\n\n')
    })

    // 获取最后一条消息的时间戳
    const lastMessage = fullChatHistory[fullChatHistory.length - 1]
    const lastMessageTimestamp = new Date(lastMessage.created_at)

    // 返回处理后的结果
    return {
      content: processedRounds,
      lastMessageTimestamp: lastMessageTimestamp
    }
  }

  /**
   * 将最近的10轮聊天记录重写为memory
   * @param processedRecentChatHistory
   * @param chat_id
   * @param round_id
   */
  public static async getNewMemoryByChatHistory(processedRecentChatHistory: ChatSessionGroup, chat_id: string, round_id: string): Promise<FormattedSummarizedMemory> {
    const llm = new LLM({ model: 'gpt-4.1-mini', meta: { chat_id: chat_id, promptName: MemorySummaryPromptOnline.name, round_id: round_id } })

    if (!processedRecentChatHistory.content.length) {
      return {
        chat_id: chat_id,
        summary: 'null',
        timestamp: new Date().toISOString()
      }
    }

    const processChatHistoryToString = processedRecentChatHistory.content.join('\n')

    // 将聊天记录总结，生成新记忆
    const formattedSummaryPrompt = await MemorySummaryPromptOnline.format(processChatHistoryToString)
    let summarizedMemory = await llm.predict(formattedSummaryPrompt)

    if (summarizedMemory.includes('null')) {
      return {
        chat_id: chat_id,
        summary: 'null',
        timestamp: processedRecentChatHistory.lastMessageTimestamp.toISOString()
      }
    }

    // 删除对于总结的总结
    summarizedMemory = summarizedMemory.split('整体来看')[0]

    // 将新生成的记忆跟之前的记忆对比，进行去重，重复的部分
    const previousMemory = await this.getMemoriesByChatId(MemoryStore.indexName, chat_id)
    if (previousMemory.join() === '') { // 之前没有记忆直接返回
      return {
        chat_id: chat_id,
        summary: summarizedMemory,
        timestamp: processedRecentChatHistory.lastMessageTimestamp.toISOString()
      }
    }

    const formattedMergePrompt = await MemoryMergePromptOnline.format(summarizedMemory, previousMemory.join('\n---\n'))

    const mergedMemory = await llm.predict(formattedMergePrompt)
    if (mergedMemory.includes('null')) {
      return {
        chat_id: chat_id,
        summary: 'null',
        timestamp: processedRecentChatHistory.lastMessageTimestamp.toISOString()
      }
    }

    // 将总结和时间戳添加到结果数组
    const summarization =  {
      summary: mergedMemory,
      timestamp: processedRecentChatHistory.lastMessageTimestamp
    }

    return {
      chat_id: chat_id,
      summary: summarization.summary,
      timestamp: summarization.timestamp.toISOString()
    }
  }

  /**
   * 将最近的10轮聊天记录重写为格式化的 memory,并推送到 elastic search 的库中
   * @param chat_id
   * @param round_id
   * @param force
   */
  public static async extractMemoriesAndUserSlots(chat_id: string, round_id: string, force = false) {
    try {
      const userMessageCount = await ChatHistoryService.getUserMessageCount(chat_id)

      if (!force && (userMessageCount === 0 || userMessageCount % 5 !== 0)) {
        return
      }

      await Promise.allSettled([
        MemoryStore.extractUserSlots(chat_id, round_id),
        MemoryStore.extractMemory(chat_id, round_id)
      ])
    } catch (e: any) {
      logger.error('将最近的10轮聊天记录重写为memory,并推送到 elastic search 的库时，出现错误', e)
    }
  }

  public static async extractUserSlots(chatId:string, roundId:string, roundNum:number = 6, model = 'o4-mini') {
    let chatHistory = await ChatHistoryService.getRecentConversationsAndStartWithAi(chatId, roundNum, 'user')
    if (chatHistory.length > 30) {
      chatHistory = chatHistory.slice(-30)
    }

    // 提取一下 客户槽位
    await ExtractUserSlotsV2.extractUserSlotsFromChatHistory(
      chatHistory.map((message) => ({ role:message.role, date:dayjs(message.created_at).format('YYYY/MM/DD HH:mm:ss'), message:message.content })),
      chatId,
      false,
      model,
      { chat_id: chatId, user_id: getUserId(chatId), roundId }
    )
  }

  public static async addMemoryToVectorDB(chat_id: string, summary: string, timestamp: string) {
    const clientArgs: ElasticClientArgs = {
      client: ElasticSearchClient.getInstance(),
      indexName: MemoryStore.indexName,
    }

    const doc = new Document({
      metadata: {
        chat_id: chat_id,
        summary: summary,
        timestamp: timestamp
      },
      pageContent: summary,
    })

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
    await vectorStore.addDocuments([doc])
  }

  static async getMemoriesByChatId(index: string, chat_id: string): Promise<string[]> {
    try {
      const query = {
        term: {
          'metadata.chat_id': chat_id
        }
      }

      const response = await ElasticSearchService.search(
        index,
        query,
        100
      )

      const metadata: IMemoryMetaData[] = response.map((hit: any) => hit._source.metadata)

      return metadata.map((data) => data.summary)
    } catch (error) {
      console.error('Error searching memory:', error)
      throw error
    }
  }

  static async clearMemory(chat_id: string) {
    const query = {
      term: {
        'metadata.chat_id': chat_id
      }
    }

    const response = await ElasticSearchService.search(
      this.indexName,
      query,
      100
    )

    await ElasticSearchService.deleteDocuments(
      this.indexName,
      response.map((hit) => hit._id as string)
    )

  }

  public static async extractMemory(chat_id: string, round_id: string) {
    const processChatHistory = await MemoryStore.processChatHistory(chat_id)

    // 使用 generateFormatMemory 方法生成格式化的摘要
    const memory = await this.getNewMemoryByChatHistory(processChatHistory, chat_id, round_id)

    if (memory.summary === 'null') {
      return
    }

    await this.addMemoryToVectorDB(chat_id, memory.summary, memory.timestamp)
  }
}