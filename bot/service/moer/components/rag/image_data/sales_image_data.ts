import { Document } from 'langchain/document'
import { ImageSearchResult } from '../moer_image_general'


const painPointImageInfo: Document[] = [
  {
    pageContent: '财富显化',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%8c%e8%b4%a2%e5%af%8c%e6%98%be%e5%8c%962.jpg',
      chunk: '财富显化2',
      tag:'sales_case',
      q: '财富显化',
    }
  },
  {
    pageContent: '财富显化',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e8%b4%a2%e5%af%8c%e6%98%be%e5%8c%96.jpg',
      chunk: '财富显化',
      tag:'sales_case',
      q: '财富显化',
    }
  },
  {
    pageContent: '成长精进',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%88%90%e9%95%bf%e7%b2%be%e8%bf%9b.jpg',
      chunk: '成长精进',
      tag:'sales_case',
      q: '成长精进',
    }

  },
  {
    pageContent: '疗愈身体的健康',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e7%96%97%e6%84%88%e8%ba%ab%e4%bd%93%e7%9a%84%e5%81%a5%e5%ba%b7.jpg',
      chunk: '疗愈身体的健康',
      tag:'sales_case',
      q: '疗愈身体的健康',
    }
  },
  {
    pageContent: '情绪管理，身体健康',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%83%85%e7%bb%aa%e7%ae%a1%e7%90%86%ef%bc%8c%e8%ba%ab%e4%bd%93%e5%81%a5%e5%ba%b7.jpg',
      chunk: '情绪管理，身体健康',
      tag:'sales_case',
      q: '情绪管理，身体健康',
    }
  },
  {
    pageContent: '情绪减压释放',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%83%85%e7%bb%aa%e5%87%8f%e5%8e%8b%e9%87%8a%e6%94%be.jpg',
      chunk: '情绪减压释放',
      tag:'sales_case',
      q: '情绪减压释放',
    }

  },
  {
    pageContent: '身体健康和成事专注力',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e8%ba%ab%e4%bd%93%e5%81%a5%e5%ba%b7%e5%92%8c%e6%88%90%e4%ba%8b%e4%b8%93%e6%b3%a8%e5%8a%9b.jpg',
      chunk: '身体健康和成事专注力',
      tag:'sales_case',
      q: '身体健康和成事专注力',
    }
  },
  {
    pageContent: '释放压力焦虑，提升觉知',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e9%87%8a%e6%94%be%e5%8e%8b%e5%8a%9b%e7%84%a6%e8%99%91%ef%bc%8c%e6%8f%90%e5%8d%87%e8%a7%89%e7%9f%a5.jpg',
      chunk: '释放压力焦虑，提升觉知',
      tag:'sales_case',
      q: '释放压力焦虑，提升觉知',
    }
  },
  {
    pageContent: '睡眠提升',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e7%9d%a1%e7%9c%a0%e6%8f%90%e5%8d%87.jpg',
      chunk: '睡眠提升',
      tag:'sales_case',
      q: '睡眠提升',
    }
  },
  {
    pageContent: '提升觉知，改善健康，缓解压力，财富增长',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%8f%90%e5%8d%87%e8%a7%89%e7%9f%a5%ef%bc%8c%e6%94%b9%e5%96%84%e5%81%a5%e5%ba%b7%ef%bc%8c%e7%bc%93%e8%a7%a3%e5%8e%8b%e5%8a%9b%ef%bc%8c%e8%b4%a2%e5%af%8c%e5%a2%9e%e9%95%bf.jpg',
      chunk: '提升觉知，改善健康，缓解压力，财富增长',
      tag:'sales_case',
      q: '提升觉知，改善健康，缓解压力，财富增长',
    }
  },
  {
    pageContent: '提升能量，提升专注力',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%8f%90%e5%8d%87%e8%83%bd%e9%87%8f%ef%bc%8c%e6%8f%90%e5%8d%87%e4%b8%93%e6%b3%a8%e5%8a%9b.jpg',
      chunk: '提升能量，提升专注力',
      tag:'sales_case',
      q: '提升能量，提升专注力',
    }
  },
  {
    pageContent: '提升自身专注力',
    metadata:{
      a: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/painPoints/%e7%97%9b%e7%82%b9%ef%bc%9a%e6%8f%90%e5%8d%87%e8%87%aa%e8%ba%ab%e4%b8%93%e6%b3%a8%e5%8a%9b.jpg',
      chunk: '提升自身专注力',
      tag:'sales_case',
      q: '提升自身专注力',
    }
  }
]

const commonCaseImageInfo: ImageSearchResult[] = [
  {
    description: '学费花的太值得了',
    url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/common/%e9%80%9a%e7%94%a8%ef%bc%9a%e5%ad%a6%e8%b4%b9%e8%8a%b1%e7%9a%84%e5%a4%aa%e5%80%bc%e5%be%97%e4%ba%86.jpg'
  },
  {
    description: '班主任的服务负责',
    url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/common/%e9%80%9a%e7%94%a8%ef%bc%9a%e7%8f%ad%e4%b8%bb%e4%bb%bb%e7%9a%84%e6%9c%8d%e5%8a%a1%e8%b4%9f%e8%b4%a3.jpg'
  },
  {
    description: '财富显化；认可助教老师服务',
    url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/caseImage/common/%e9%80%9a%e7%94%a8%ef%bc%9a%e8%b4%a2%e5%af%8c%e6%98%be%e5%8c%96%ef%bc%9b%e8%ae%a4%e5%8f%af%e5%8a%a9%e6%95%99%e8%80%81%e5%b8%88%e6%9c%8d%e5%8a%a1.jpg'
  }]

export class RagImageData {
  public static getAllPainPointImageInfo(): ImageSearchResult[] {
    return painPointImageInfo.map((item) => {
      return {
        description: item.pageContent,
        url: item.metadata.a
      }
    })
  }

  public static getPainPointImageInfoByChunk(chunk: string): ImageSearchResult {
    const result = painPointImageInfo.find((item) => item.metadata.chunk === chunk)
    if (!result) {
      return {
        description: '',
        url: ''
      }
    }
    return {
      description: result.pageContent,
      url: result.metadata.a
    }
  }

  public static getCommonCaseImageInfo(): ImageSearchResult[] {
    return commonCaseImageInfo
  }

  public static getCommonCaseImageInfoByDescription(description: string): ImageSearchResult {
    const result = commonCaseImageInfo.find((item) => item.description === description)
    if (!result) {
      return {
        description: '',
        url: ''
      }
    }
    return result
  }
}