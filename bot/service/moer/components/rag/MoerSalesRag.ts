import ElasticSearchService, { IElasticEmbeddingRes } from '../../../../model/elastic_search/elastic_search'
import { RAGHelper } from '../../../../model/rag/rag'
import { StringHelper } from '../../../../lib/string'
import logger from '../../../../model/logger/logger'
import { ChatStateStore } from '../../storage/chat_state_store'
import { ExtractUserSlotsV2 } from '../flow/helper/slotsExtract'

export interface SalesRagResult {
    q: string
    a: string
    casePrompt?: string
}

export enum SalesRagDoc {
  General = '痛点销售案例',
  PainPointCourseValue = '痛点-冥想价值问题'
}

export class MoerSalesRag {
  public static index = 'moer_sales_rag_2048d'

  public static async search(inputQuery:string, queryDocs: string[], resNum: number) {

    const searchResult = await this.embeddingSearch(inputQuery, queryDocs, 10, 0.7)
    const reRankResults = await this.reRank(inputQuery, searchResult, resNum)
    return reRankResults.map((qa) => {
      if (StringHelper.isEmpty(qa.casePrompt)) {
        return `问题：${qa.q}\n参考话术：${qa.a}`
      }
      return `问题：${qa.q}\n解决方案:${qa.casePrompt}\n参考话术：${qa.a}`
    }).join('\n\n')
  }

  public static async getSalesRagContext(inputQuery:string, queryDocs: string[], resNum: number = 2) {
    const searchResult = await this.search(inputQuery, queryDocs, resNum)
    if (searchResult.length > 0) {
      const dynamicPrompt = `参考问题与解决方案：
${searchResult}`
      logger.trace('recall sales case:', dynamicPrompt)
      return dynamicPrompt
    }
    return ''
  }

  public static async searchByPainPoint(chatId: string, queryDocs: string[], resNum: number) {


    const chatState = ChatStateStore.get(chatId)
    const userSlot = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)
    const paintPoint = userSlot.getStringByTopic('痛点')

    const searchText = paintPoint

    const searchResult = await this.embeddingSearch(searchText, queryDocs, 1, 0)
    return searchResult.map((qa) => {
      return{
        q: qa.pageContent,
        a: qa.metadata.a,
        casePrompt: qa.metadata.casePrompt
      } as SalesRagResult
    })
  }


  private static async embeddingSearch(query: string, queryDocs: string[], resNum: number, threshold: number): Promise<IElasticEmbeddingRes[]> {
    // 构建查询 filter
    const filter = {
      bool: {
        should: queryDocs.map((doc) => ({ term: { 'metadata.doc': doc } })),
      },
    }

    // Embedding Search
    return await ElasticSearchService.embeddingSearch(
      this.index,
      query,
      resNum,
      threshold,
      filter
    )
  }

  private static async reRank(inputQuery: string, embeddingRes: IElasticEmbeddingRes[], resNum: number):Promise<SalesRagResult[]> {
    const retrievedQAs = embeddingRes
      .flat()
      .map((item) => {
        return {
          q: item.pageContent,
          a: item.metadata.a,
          casePrompt: item.metadata.casePrompt,
        }
      })
    const reRankResults = await RAGHelper.reRank(inputQuery, retrievedQAs, { top_n: resNum, threshold: 0.1 })
    return reRankResults.map((qa) => {
      const index = retrievedQAs.findIndex((item) => item.q === qa.q)
      return {
        q: qa.q,
        a: qa.a,
        casePrompt: retrievedQAs[index].casePrompt
      }
    })
  }

}