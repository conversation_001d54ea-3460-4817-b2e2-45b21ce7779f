import { Config } from '../../../../config/config'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../lib/xml/xml'
import ElasticSearchService from '../../../../model/elastic_search/elastic_search'
import logger from '../../../../model/logger/logger'
import RerankService from '../../../../lib/rerank/rerank_service'
import { DataService } from '../../getter/getData'
import { ChatHistoryService } from '../chat_history/chat_history'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { QueryRewritePrompt } from '../../prompt/moer/userQueryRewrite'
import { DateHelper } from '../../../../lib/date/date'
import { MoerGeneralRAG } from './moer_general'

interface IElasticEmbeddingRes {
  pageContent: string
  metadata: Record<string, any>
  score: number
}
interface RerankResultQuestions {
  questions: string
  scores: number
}

interface IEmbeddingOptions {
  subQueryOutputNumber?: number // 每个子查询近似搜索返回的个数
  outputNumber?: number // 经过 ReRank 最后输出的数量限制，最后 RAG 的结果会 <= 这个值
  lowestScore: number // 最低的 筛选阈值分数
  filter?: object
}


interface IRerankOptions {
  model?: 'BAAI/bge-reranker-v2-m3' | 'netease-youdao/bce-reranker-base_v1'
  top_n: number // 返回的结果数量
  return_documents?: boolean // 是否返回document里的内容
  max_chunks_per_doc?: number // 每个文档最多返回的chunk数量
  overlap_tokens?: number // 重叠的token数量
}

interface IEmbeddingSearchRes {
  rewriteQuestions: string
  question: string
  answer: string
  embeddingScore: number
  rerankScores: number
}

export class MoerRag {
  public static nonRAGMatchIndex = 'moer_non_rag_queries_2048d'

  public static async isRagEmbeddingSearch(query: string): Promise<string[]> {
    // 将查询拆分成多个部分
    const queryParts = query.split(/[，。\s]+/).filter((part) => part.trim() !== '')
    const unmatchedParts: string[] = []

    for (const part of queryParts) {
      const ragResults = await ElasticSearchService.embeddingSearch(MoerRag.nonRAGMatchIndex, part, 1, 0.8)

      if (ragResults.length === 0) {
        // 没有搜索到了，输出，后面要rag
        unmatchedParts.push(part)
      }
      else {
        logger.trace(`被删除的部分query "${part}":`, ragResults)
      }
    }
    return unmatchedParts
  }

  public static async queryReWrite(query: string, prompt: string) {
    const llm = new LLM({ model: 'gpt-4.1-mini' })
    try {
      const extractedRawQuery = await llm.predict(prompt)
      const xmlQuery = XMLHelper.extractContents(extractedRawQuery, 'query')
      if (!xmlQuery) {
        return [query]
      }
      return [query, ...xmlQuery]
    } catch (error) {
      logger.error('Error in query rewrite:', error)
      return [query]
    }
  }

  /**
   *
   * 将 Embedding 检索后的内容进行重排序
   * @param rewroteQuery 重写的客户问题(string)
   * @param retrievedQuestions embedding search得到的k个问题
   * @param options
   * @private
   */
  public static async reRank(rewroteQuery: string, retrievedQuestions: string[], options?: IRerankOptions): Promise<RerankResultQuestions[]> {
    const rerankService = new RerankService()

    try {
      const response = await rerankService.createRerank({
        model: options?.model ? options?.model : 'BAAI/bge-reranker-v2-m3',
        query: rewroteQuery,
        documents: retrievedQuestions,
        top_n: options?.top_n,
        return_documents: options?.return_documents,
        max_chunks_per_doc: options?.max_chunks_per_doc,
        overlap_tokens: options?.overlap_tokens,
      })

      // 根据相关性得分对结果进行排序
      const sortedResults = response.results.sort((a, b) => b.relevance_score - a.relevance_score)

      // 返回排序后的文档
      const rerankResult: RerankResultQuestions[] = []
      const seenQuestions = new Set<string>()

      for (let i = 0; i < sortedResults.length; i++) {
        const question = retrievedQuestions[sortedResults[i].index]
        if (!seenQuestions.has(question)) {
          rerankResult.push({
            questions: retrievedQuestions[sortedResults[i].index],
            scores: sortedResults[i].relevance_score
          })
          seenQuestions.add(question)
        }
      }
      return rerankResult

    } catch (error) {
      console.error('Error in reRanker:', error)

      // 添加fallback机制: 确保即使在重新排序操作失败的情况下，函数仍然能返回一个有效的结果。
      const top_n = options?.top_n || 3 // 如果没有指定top_n，默认使用3
      const fallbackScores = [0.9, 0.85, 0.85] // 预设的分数
      const errorResult: RerankResultQuestions[] = []

      for (let i = 0; i < Math.min(top_n, retrievedQuestions.length); i++) {
        errorResult.push({
          questions: retrievedQuestions[i],
          scores: i < fallbackScores.length ? fallbackScores[i] : fallbackScores[fallbackScores.length - 1]
        })
      }
      return errorResult
    }
  }

  /**
   * 使用 rewrite embeddingSearch rerank，得到最后的近似结果
   * @param index
   * @param query rewrite 后，也就是重写后的单个客户问题
   * @param options 想要保留的第一步embedding search的结果数量 和 最后得到的rerank后的数量 rerankReturnNumber
   * @private
   */

  public static async embeddingSearch(index: string, query: string, chat_id: string, options?: IEmbeddingOptions): Promise<IEmbeddingSearchRes[]> {
    const ragResults = await ElasticSearchService.embeddingSearch(index, query, options?.subQueryOutputNumber, options?.lowestScore, options?.filter)
    // 把ragResults的document和score拿出来变成object
    const ragResultsObjects: IElasticEmbeddingRes[] = []
    if (ragResults.length === 0) {
      return []
    } else {
      for (const ragResult of ragResults) {
        ragResultsObjects.push({
          pageContent: ragResult.pageContent,
          metadata: ragResult.metadata,
          score: ragResult.score
        })
      }
    }
    //拿出 rag 后的 q
    const retrievedQuestions: string[] = []
    const embeddingScore:number[] = []
    const docName:string[] = []
    for (const ragResultsObject of ragResultsObjects) {
      const retrievedQuestion = ragResultsObject.pageContent  // 这里 pageContent 为 问题
      retrievedQuestions.push(retrievedQuestion)
      embeddingScore.push(ragResultsObject.score)
      docName.push(ragResultsObject.metadata.doc)
    }
    logger.trace(chat_id, 'rag使用的文档：', docName)

    //在这里拿原本的 q 对 （rag 后的结果中的 q） 进行 rerank
    const reRankQ = await this.reRank(query, retrievedQuestions, { top_n: options?.outputNumber ? options?.outputNumber : 3 })
    //使用 rerank 后的 q 找到 metadata 中相匹配的 a(ragResults.metadata.a)，然后把 q 和 a 拼成一个 dictionary
    const seenQuestions = new Set<string>()
    const uniqueResults: IEmbeddingSearchRes[] = []
    for (const item of reRankQ) {
      if (seenQuestions.has(item.questions)) {
        continue
      }
      const matchingRagResult = ragResultsObjects.find(
        (ragResultsObject) => ragResultsObject.pageContent === item.questions)
      if (matchingRagResult) {
        uniqueResults.push({
          rewriteQuestions: query,
          question: item.questions,
          answer: matchingRagResult.metadata.a,
          embeddingScore: matchingRagResult.score,
          rerankScores: item.scores
        })
        seenQuestions.add(item.questions)
      }
    }
    return uniqueResults
  }

  /**
   * 通过天数，限制 filter 的内容
   * @param chat_id
   * @param options
   */
  public static async getHierarchyFilter(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    const { is_course_week: isCourseWeek, day: currentDay, time: currentHour } = currentTime
    // 第一周，也就是非课程周的 rag 所需文档
    const firstWeekRagDocList = [
      '常规问题全局.xlsx',
      '常规问题周三八点前.xlsx',
      '冥想问题.xlsx',
      '系统班全通班逐字稿',
      '课后问题回访FAQ.docx',
      '开营班会'
    ]
    // 按天分的 课程周的 rag 所需文档
    const secondWeekDay1RagDocList = [
      '第一天课程-情绪减压.docx',
      '冥想问题.xlsx',
      '常规问题全局.xlsx',
      '常规问题周三八点前.xlsx',
      '系统班全通班逐字稿',
      '课后问题回访FAQ.docx'
    ]
    const secondWeekDay2RagDocList:string[] = [
      '第一天课程-情绪减压.docx',
      '第二天课程-财富果园.docx',
      '冥想问题.xlsx',
      '常规问题全局.xlsx',
      '常规问题周三八点前.xlsx',
      '系统班全通班逐字稿',
      '课后问题回访FAQ.docx'
    ]
    const secondWeekDay3RagDocList:string[] = [
      '第一天课程-情绪减压.docx',
      '系统班全通班逐字稿',
      '第二天课程-财富果园.docx',
      '第三天课程-效能提升.docx',
      '冥想问题.xlsx',
      '常规问题全局.xlsx',
      '常规问题周三八点前.xlsx',
      '课后问题回访FAQ.docx']
    const saleRagDocList:string[] = [
      '第一天课程-情绪减压.docx',
      '第二天课程-财富果园.docx',
      '第三天课程-效能提升.docx',
      '冥想问题.xlsx',
      '常规问题全局.xlsx',
      '课后问题回访FAQ.docx',
      '系统班全通班逐字稿',
      '销售文档',
      '销售问题.xlsx']

    let docsToInclude: string[] = []

    if (!isCourseWeek) { // 非课程周
      docsToInclude = firstWeekRagDocList
    } else {
      // 课程周
      switch (currentDay) {
        case 1:
          docsToInclude = secondWeekDay1RagDocList
          break
        case 2:
          docsToInclude = secondWeekDay2RagDocList
          break
        case 3:
          if (DateHelper.isTimeAfter(currentHour, '20:00:00')) {
            docsToInclude = saleRagDocList
          } else {
            docsToInclude = secondWeekDay3RagDocList
          }
          break
        case 4:
          docsToInclude = saleRagDocList
          break
        case 5:
          docsToInclude = saleRagDocList
          break
      }
    }
    return {
      'bool': {
        'should': docsToInclude.map((doc) => ({ 'term': { 'metadata.doc': doc } }))
      }
    }
  }



  /**
   * 通过天数，限制 filter 的内容，从而限制 embedding search 的结果。具体如何限制参考 getHierarchyFilter
   * @param index
   * @param queryRewriteResult
   * @param chat_id
   * @param options
   */
  public static async hierarchicalEmbeddingSearch(index: string, queryRewriteResult: string, chat_id:string, options?: IEmbeddingOptions): Promise<IEmbeddingSearchRes[]> {
    const scheduleTime = await DataService.getCurrentTime(chat_id)
    const { is_course_week: isCourseWeek, day: currentDay, time: currentHour } = scheduleTime
    const filterHierarchical = await this.getHierarchyFilter(chat_id)
    // 第一层 filter
    const firstLayerOptions = {
      ...options,
      lowestScore: 0.8,
      similaritySearchReturnNumber: 10,
      rerankReturnNumber: 2,
      filter: filterHierarchical
    }
    // 判断是否是课程周，只是用来打印log
    let currentWeek = 1
    if (isCourseWeek) {
      currentWeek = 2
    }
    // embedding search 只从第一层 filter 中找
    const embeddingMustCorrect = await this.embeddingSearch(index, queryRewriteResult, chat_id, firstLayerOptions)
    logger.trace('从阶段库中找到的 embedding search 结果：', JSON.stringify(embeddingMustCorrect, null, 2))
    if (embeddingMustCorrect.length > 0) {
      logger.trace(chat_id, `当下是第${currentWeek}周, 第${currentDay}天，时间为${currentHour}`)
      return embeddingMustCorrect
    }
    // 如果第一层 filter 中没有，则 log 到日志上
    logger.trace({ chat_id }, `当下是第${currentWeek}周, 第${currentDay}天，时间为${currentHour}。问题不在阶段库中：`, queryRewriteResult)
    try {
      await PrismaMongoClient.getInstance().rag_supplement_questions.create({
        data: {
          timestamp: new Date(),
          database: MoerGeneralRAG.index,
          chat_id: chat_id,
          msg: `当下是第${currentWeek}周, 第${currentDay}天，时间为${currentHour}。问题不在阶段库中：${queryRewriteResult}`,
          scheduleTime: JSON.stringify(scheduleTime)
        }
      })
    } catch (error) {
      logger.error('补充问题写入 rag_supplement_questions 时出错：', error)
    }
    return []
  }

  /**
   * 通用 RAG， 用于日常问题回答
   * @param inputQuery
   * @param chat_id
   * @param options
   */

  public static generalRAG(inputQuery: string, chat_id: string, options?: IEmbeddingOptions) {
    if (!inputQuery || (inputQuery.startsWith('[') && inputQuery.startsWith(']'))) {
      return ''
    }
    return this.ragImplementation(MoerGeneralRAG.index, inputQuery, chat_id, options)
  }

  public static async formatRagResult(ragResult: IEmbeddingSearchRes[][]): Promise<string> {
    // 创建一个集合来跟踪已经出现过的问答
    const seen = new Set()
    return ragResult.flatMap((group) =>
      group.map((item, index) => {
        // 将问题和答案组合为一个键用于检查
        const key = `${item.question}-${item.answer}`
        // 如果问题和答案已经出现过，跳过此条目
        if (seen.has(key)) {
          return null
        }

        // 如果没有出现过，将其添加到集合并返回格式化的字符串
        seen.add(key)
        return `问题：${item.question}\n答案：${item.answer}`
      })
    )
    // 过滤掉空值（即重复项）
      .filter(Boolean)
      .join('\n\n')
  }

  /**
   * RAG 主逻辑: 主流程，将查询重写为 两个子查询，将原问题+生成的两个子查询去查询得出 options.subQueryOutputNumber 个结果，
   * 最后将所有查询进行重排序，返回 topK 个结果
   * @param index
   * @param inputQuery
   * @param chat_id
   * @param options
   */
  public static async ragImplementation(index: string, inputQuery: string, chat_id: string, options?: IEmbeddingOptions) {
    // 1. 获取对话历史
    const recentConversations = await ChatHistoryService.getRecentConversations(chat_id, 3)
    const chatHistory = ChatHistoryService.formatHistoryHelper(recentConversations)

    // 2. 查询重写
    const queryRewriteResults = await this.performQueryRewrite(inputQuery, chatHistory)
    logger.trace({ chat_id }, `客户问题重写结果: ${JSON.stringify(queryRewriteResults) })`)

    // 3. 执行搜索
    const searchResults = await this.performParallelSearch(
      index,
      queryRewriteResults,
      chat_id,
      options
    )

    // 4. 格式化结果
    const formattedResults = await this.formatRagResult(searchResults)
    if (!formattedResults) {
      logger.trace({ chat_id }, '在问题库中未找到匹配结果：', inputQuery)
    }

    return formattedResults
  }


  private static async performQueryRewrite(
    query: string,
    chatHistory: string
  ): Promise<string[]> {
    const prompt = await QueryRewritePrompt.format(query, chatHistory)
    return this.queryReWrite(query, prompt)
  }

  // 继续 RagService.ts
  private static async performParallelSearch(
    index: string,
    queries: string[],
    chat_id: string,
    options?: IEmbeddingOptions
  ): Promise<IEmbeddingSearchRes[][]> {
    const searchPromises = queries.map((query) =>
      this.hierarchicalEmbeddingSearch(index, query, chat_id, options)
    )

    return Promise.all(searchPromises)
  }

}