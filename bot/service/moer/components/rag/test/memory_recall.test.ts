import ElasticSearchService from '../../../../../model/elastic_search/elastic_search'
import { KeywordExtractionPrompt } from '../../../prompt/moer/keyword_extraction'
import { MemoryRecall } from '../../memory/memory_search'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { UUID } from '../../../../../lib/uuid/uuid'

describe('memory_recall', () => {
  it('keyword_extraction', async () => {
    // const user_id = UUID.short()
    // const chat_id = getChatId(user_id)
    //
    // await WorkFlow.step(chat_id, user_id, '我这些天生病了')
    // await WorkFlow.step(chat_id, user_id, '情绪不稳定，有天竟然把一个月的睡眠药一次性吃了')
    // await WorkFlow.step(chat_id, user_id, '系统班的时长是多久')
    const chat_id = '7881301241917568_1688857003605938'
    const query =  '我他妈心情很烦躁怎么办，不想上班，而且还阳痿'
    const recentConversations = await ChatHistoryService.getRecentConversations(chat_id, 5)
    const chat_history = ChatHistoryService.formatHistoryHelper(recentConversations)
    const keywords = await MemoryRecall.keywordExtraction(query, await KeywordExtractionPrompt.format(query, chat_history))
    console.log('chat_history', chat_history)
    console.log('extracted keywords', keywords)
  }, 30000)

  it('memory_recall', async () => {
    const chat_id = '7881302298050442_1688858254705213'
    const query =  '我他妈心情很烦躁怎么办，不想上班，而且还阳痿'
    const memory = await MemoryRecall.memoryRecall(query, chat_id)
    const memoryContext = `## 历史记忆\n${memory}`
    console.log(memoryContext)
  }, 30000)

  it('测试空 memory', async () => {
    const memory = await MemoryRecall.memoryRecall('卧槽', UUID.short())
    console.log(memory)
  }, 60000)

  it('embedding_threshold', async () => {
    try {
      const query =  '胃口明显改善，正在调理，'
      const filter = {
        'bool': { 'should':[
          // { 'term':{ 'metadata.chat_id':'7881302298050442_1688855598572342' } }  // Horus
          { 'term':{ 'metadata.chat_id':'7881299791948506_1688857003605938' } }  // 天山雪莲
        ] } }
      const results = await ElasticSearchService.embeddingSearch('user_memory', query, 5, 0.0, filter)
      console.log('results', results)
    } catch (error) {
      console.error('Error in embedding search:', error)
    }
  }, 30000)
})
