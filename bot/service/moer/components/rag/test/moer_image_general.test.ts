import { MoreImageGeneral } from '../moer_image_general'
import { ChatStateStore, ChatStatStoreManager } from '../../../storage/chat_state_store'
import { DataService } from '../../../getter/getData'
import path from 'path'
import { FileHelper } from '../../../../../lib/file'

describe('Test', function () {
  beforeAll(() => {

  })

  it('1.TestImageSearch', async () => {
    ChatStateStore.get('test').userSlots.pain_points = undefined
    const result = await MoreImageGeneral.getSalesCaseImage('test')
    console.log(result)
  }, 60000)

  it('2.TestSearchSalesCaseImage', async () => {
    const result = await MoreImageGeneral.searchSalesCaseImage('班主任的服务负责', ['sales_case'], 1)
    console.log(result)
  }, 60000)

  it('2.generateTestData', async () => {
    const dataSet: any[] = []
    const userNameList = ['金生缘3933', '乌云格日勒', '亲亲宝贝', '杨依灵（6722）', '素颜暖笑', '<PERSON>', '朴恩花🌸', '妙诚', '天下太平', 'Vera', '李静18502928671']
    for (const userName of userNameList) {
      const oneData = {}
      const chatIds = await DataService.getChatByWechatName(userName)
      if (!chatIds) {
        return
      }
      const chat = chatIds[0] as any
      const chatId:string = chat._id
      await ChatStatStoreManager.initState(chatId)
      const painPoint = ChatStateStore.get(chatId).userSlots.pain_points
      oneData['chatId'] = chatId
      oneData['painPoint'] = painPoint
      dataSet.push(oneData)
    }
    const filePath = path.join(__dirname, 'dataSet/imageRagDataSet.json')
    await FileHelper.writeFile(filePath, JSON.stringify(dataSet, null, 2))
  }, 120000)

  it('3.runTestData', async () => {
    // const resultSet: any[] = []
    // const data: any[] = JSON.parse(await FileHelper.readFile(path.join(__dirname, 'dataSet/imageRagDataSet.json')))
    // for (const item of data) {
    //   ChatStateStore.get(item.chatId).userSlots.pain_points = item.painPoint
    //   item.actual = await MoreImageGeneral.searchByUserSlot(ChatStateStore.get(item.chatId).userSlots, ['sales_case'])
    //   resultSet.push(item)
    // }
    // const filePath = path.join(__dirname, 'dataSet/imageRagTestResult.json')
    // await FileHelper.writeFile(filePath, JSON.stringify(resultSet, null, 2))
  }, 120000)



})