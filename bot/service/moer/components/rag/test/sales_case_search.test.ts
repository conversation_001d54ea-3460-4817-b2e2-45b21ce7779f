import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import { AzureOpenAIEmbedding } from '../../../../../lib/ai/llm/openai_embedding'
import ElasticSearchService, { ElasticSearchClient } from '../../../../../model/elastic_search/elastic_search'
import { MoerSalesRag, SalesRagDoc } from '../MoerSalesRag'
import { DataService } from '../../../getter/getData'
import { ChatStatStoreManager } from '../../../storage/chat_state_store'


describe('Test', function () {
  it('TestSearchScore', async function () {
    const query = '最近身体不好，很累'
    const clientArgs: ElasticClientArgs = {
      client: ElasticSearchClient.getInstance(),
      indexName: 'moer_sales_rag_2048d',
    }

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
    const embeddingSearchResult = await vectorStore.similaritySearchWithScore(query, 10)
    console.log(embeddingSearchResult)
  })

  it('所有库搜索', async () => {
    const query = '红色大门'
    const indexs = ['moer_rag_2', 'moer_image_rag', 'moer_sales_rag', 'moer_non_rag_queries', 'moer_user_memory', 'wealth_orchard']
    for (const index of indexs) {
      const new_index = `${index  }_2048d`

      console.log(JSON.stringify(await ElasticSearchService.embeddingSearch(new_index, query, 10, 0), null, 4))
    }
  }, 60000)

  it('TestSearch', async () => {
    const query = '最近身体不好，很累'
    const res = await MoerSalesRag.search(query, ['痛点销售案例'], 5)
    console.log(res)
  }, 60000)

  it('TestSearchByPainPoint', async () => {
    const chats = await DataService.getChatByWechatName('紫璃')
    const len = Number(chats.length)
    const chatId = chats[len - 1] as any
    const chat_id = chatId._id
    await ChatStatStoreManager.initState(chat_id)
    const res = await MoerSalesRag.searchByPainPoint(chat_id, [SalesRagDoc.PainPointCourseValue], 5)
    console.log(res)
  }, 60000)
})