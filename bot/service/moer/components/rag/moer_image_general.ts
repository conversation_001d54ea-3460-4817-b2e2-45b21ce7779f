import ElasticSearchService from '../../../../model/elastic_search/elastic_search'
import { RagImageData } from './image_data/sales_image_data'
import { ChatStateStore } from '../../storage/chat_state_store'
import { StringHelper } from '../../../../lib/string'
import { ExtractUserSlotsV2 } from '../flow/helper/slotsExtract'


export interface ImageSearchResult {
  url: string
  description: string
}

export class MoreImageGeneral {

  public static index = 'moer_image_rag_2048d'

  public static async searchByUserSlot(userSlot: string, tag: string[]): Promise<ImageSearchResult[]> {
    if (!userSlot) {
      return []
    }

    const filter = {
      bool: {
        should: tag.map((tag) => ({ term: { 'metadata.tag': tag } })),
      },
    }
    const searchResult = await ElasticSearchService.embeddingSearch(
      this.index,
      userSlot,
      3,
      0.2,
      filter
    )
    return searchResult.map((item) => {
      return {
        description: this.addSquareBracket(item.metadata.chunk),
        url: item.metadata.a
      }
    })
  }

  public static async getSalesCaseImage(chat_id: string): Promise<ImageSearchResult[]> {
    const chatState = ChatStateStore.get(chat_id)
    const userSlot = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, false)
    const meditationExperience = userSlot.getStringByTopic('过往冥想经验')
    const goalsAndNeeds = userSlot.getStringByTopic('冥想目标')
    const paintPoint = userSlot.getStringByTopic('痛点')
    const res = [meditationExperience, goalsAndNeeds, paintPoint].filter((item) => item != '')

    if (res.length == 0) {
      return this.getDefaultSalesCase()
    }

    const searchResult = await this.searchByUserSlot(res.join(' '), ['sales_case'])
    return searchResult.concat(this.getCommonCaseRandomly(2))
  }

  public static async searchSalesCaseImage(chunk: string, tag:string[], resNum: number): Promise<ImageSearchResult[]> {
    if (StringHelper.isEmpty(chunk)) {
      return []
    }
    const filter = {
      bool: {
        should: tag.map((tag) => ({ term: { 'metadata.tag': tag } })),
      },
    }
    const searchResult = await ElasticSearchService.embeddingSearch(
      this.index,
      chunk,
      resNum,
      0.9,
      filter
    )
    return searchResult.map((item) => {
      return {
        description: this.addSquareBracket(item.metadata.chunk),
        url: item.metadata.a
      }
    })
  }


  private static getDefaultSalesCase(): ImageSearchResult[] {
    return [RagImageData.getPainPointImageInfoByChunk('释放压力焦虑，提升觉知'), RagImageData.getPainPointImageInfoByChunk('疗愈身体的健康'), RagImageData.getPainPointImageInfoByChunk('财富显化2'), RagImageData.getPainPointImageInfoByChunk('睡眠提升'), RagImageData.getCommonCaseImageInfoByDescription('学费花的太值得了')]
  }

  private static getCommonCaseRandomly(num: number): ImageSearchResult[] {
    const commonCase:ImageSearchResult[] = RagImageData.getCommonCaseImageInfo()
    if (num >= commonCase.length) {
      return commonCase
    }
    const shuffled = commonCase.sort(() => 0.5 - Math.random())
    return shuffled.slice(0, num)
  }

  private static addSquareBracket(content: string): string {
    return `[${content}]`
  }
}