import { LLM } from '../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../lib/xml/xml'
import logger from '../../../../model/logger/logger'
import ElasticSearchService from '../../../../model/elastic_search/elastic_search'
import { WealthKeyExtract } from '../../prompt/moer/wealthKeysExtract'

export class WealthOrchardRag {

  /**
     * 单独用于 财富果园解读 的关键信息提取
     * @param userInput 客户的原始输入，也就是 财富果园看到的场景
     * @param prompt
  */
  public static async wealthKeysExtract(userInput: string, prompt: string) {
    const llm = new LLM()
    try {
      const extractedRawQuery = await llm.predict(prompt)

      const xmlSubCategory = XMLHelper.extractContents(extractedRawQuery, 'subCategory')
      if (!xmlSubCategory) {
        return [userInput]
      }
      return xmlSubCategory
    } catch (error) {
      logger.error('Error in query rewrite:', error)
      throw error
    }
  }

  /**
     * 对提取到的客户的财富果园画面的关键信息进行embedding search
     * @param subcategories 客户描述中提取到的关键信息
     */
  public static async embeddingSearchWealth(subcategories: string[]) {
    let combinedContent = ''
    for (const subcategory of subcategories) {
      const ragResult = await ElasticSearchService.embeddingSearch('wealth_orchard_2048d', subcategory, 1, 0.8)
      if (ragResult.length > 0) {
        const matchedDocument = ragResult[0]
        const content = matchedDocument.metadata.a
        if (content) {
          combinedContent += `${subcategory}: ${content}\n\n`
        }
      }
    }
    return combinedContent.trim()
  }

  /**
     * 对提取到的客户的财富果园画面的关键信息进行 强匹配，如果匹配不上，用embedding search兜住
     * @param subcategories 客户描述中提取到的关键信息
     */

  public static async exactMatchSearchWealth(subcategories: string[]) {
    let combinedContent = ''

    for (const subcategory of subcategories) {
      try {
        const result = await ElasticSearchService.search('wealth_orchard_exact', {
          term: {
            subCategory: subcategory
          }
        }, 1)

        if (result.length > 0) {
          const matchedDocument = result[0]._source as { content: string } | undefined
          if (matchedDocument && matchedDocument.content) {
            combinedContent += `${subcategory}: ${matchedDocument.content}\n\n`
          }
        } else {
          // 没有匹配时，调用 embeddingSearchWealth
          const embeddingResult = await this.embeddingSearchWealth([subcategory])
          if (embeddingResult) {
            combinedContent += `${subcategory}: ${embeddingResult}\n\n`
          }
        }
      } catch (error) {
        console.error(`Error searching for subcategory ${subcategory}:`, error)
        // 继续处理下一个 subcategory
      }
    }

    return combinedContent.trim()
  }

  public static async wealthOrchardRag(userInput: string) {
    const subcategories = await this.wealthKeysExtract(userInput, await WealthKeyExtract.format(userInput))
    logger.trace(`Extracted subcategories: ${subcategories}`)
    return await this.exactMatchSearchWealth(subcategories)
  }

}