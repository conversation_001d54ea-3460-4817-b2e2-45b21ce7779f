import { PromptTemplate } from '@langchain/core/prompts'
import { DataService } from '../../../getter/getData'
import { isScheduleTimeBefore } from '../../schedule/creat_schedule_task'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import logger from '../../../../../model/logger/logger'


export class PlannerMaterialRag {

  public static materialIndex = 'moer_material'


  public static async extractMaterial(chatId: string, plan: string, roundId: string) {

    const timeStr = await PlannerMaterialRag.getTimeInfo(chatId)
    const prompt = await PlannerMaterialRag.extractMaterialFromPlanPrompt(plan, timeStr)

    const response = await LLM.predict(prompt, { responseJSON: true, model:'gpt-5-mini', reasoningEffort:'low', meta:{ round_id:roundId } })

    const data = JSON.parse(response)


    logger.trace('plan', chatId, data.titles.join(','))
  }


  public static async sendMaterial(chatId: string, materials: string[]) {

    // for (let i = 0; i < materials.length; i++) {
    //
    // }

  }


  private static async searchMaterialFromES(query: string) {

  }




  private static async extractMaterialFromPlanPrompt(plan: string, timeStr: string) {

    const prompt = `你是一个“素材调用助手”。  
你的任务是：根据给定的 **目标描述/服务目的**，自动联想出可能需要的素材，并返回对应素材库中素材的 title 列表，用于后续搜索和调用。  

# 目标描述
{plan}

# 当前时间
{timeStr}

# 参考素材（包括但不限于）
 - 入门营课程的服务相关：课程表（5天入门营课程安排总览，一般在客户刚加入的时候分享帮助了解），课程时刻表（针对每一节课每个时间段老师所讲内容的预告，一般在课前的时候使用），课程总结大纲，课程相关音频视频，上课方式指引等
 - 冥想相关科普信息补充：.....
 - 入门营学员异议处理：....
 - 入门营学习案例：.....
 - 高阶课学员案例：.....

# 使用规则：
1. 目标输入可能来自「课程规划器」生成的 task

2. 你需要基于目标的语义，匹配到最相关的素材标题。  
   - **相似或重复的素材只保留一个**，确保输出列表中没有功能重叠的素材。  
   - **每类需求只输出 1 个最能代表的素材**，保证覆盖目标需求即可。  
   - 输出的素材标题必须是一句精简、完整的话，**不得包含括号说明**。

3. 输出时保持结构化：
   - \`reason\`（简要说明为什么选择这些素材，便于调试）
   - \`titles\`（一组 title，用于在素材库中搜索）

### 以JSON格式输出：
{{
    "reason": "简要逻辑"
    "titles": ["素材title1", "素材title2", "素材title3"],
}}
`
    const promptTemplate = PromptTemplate.fromTemplate(prompt)


    return await promptTemplate.format({
      plan, timeStr
    })
  }


  private static async getTimeInfo(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (!currentTime.is_course_week) {
      return '当前非上课周'
    }

    if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })) {
      return '当前第一节课，情绪减压课前'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 1, time: '23:59:00' })) {
      return '当前第一节课，情绪减压课后'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 2, time: '20:00:00' })) {
      return '当前第二节课，财富果园课前'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 2, time: '23:59:00' })) {
      return '当前第二节课，财富果园课后'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 3, time: '20:00:00' })) {
      return '当前第三节课，红靴子课前'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 3, time: '23:59:00' })) {
      return '当前第三节课，红靴子课后'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 4, time: '20:00:00' })) {
      return '当前第四节课，蓝鹰预演课前'
    } else {
      return '当前第四节课，蓝鹰预演课后'
    }

  }




}