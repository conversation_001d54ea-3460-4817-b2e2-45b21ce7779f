import { IKnowledgeRagTool, KnowledgeRagTool, RagToolExecuteParams } from './knowledge_rag_tool'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { Config } from '../../../../../../config/config'


interface PlanStep {

    reasoning: string
    plan: string
    op: string
    input: RagToolExecuteParams
    fallbacks?: string[]
}


interface Plan {
    steps: PlanStep[]
}


export class KnowledgeRag {


  public static async search(strategy: string, chatId: string, roundId: string) {

    const plan = await KnowledgeRag.generatePlan(strategy, await KnowledgeRagTool.getTools(), chatId, roundId)
    const evidence = await KnowledgeRag.executePlan(plan)

    this.logMessage('Evidence:', evidence)

    return evidence
  }


  private static async generatePlan(strategy: string, tools: IKnowledgeRagTool[], chatId: string, roundId: string): Promise<Plan> {
    // Compose a human friendly list of tool names for the prompt
    const toolList = tools
      .map((t) => `* ${t.name}: ${t.description}`)
      .join('\n')
    const plannerPrompt = `你是一个计划专家，需要根据回复策略与客户的聊天记录生成可执行计划。请按以下格式输出 JSON，不要写任何其他文字：

{{"steps": [{{"reasoning": "<简短阐述为什么需要此步骤>","plan":"<步骤描述>" "op": "<工具键名>", "input": "" }}, ...]}}

请注意：
1. 将复杂问题拆解为若干个步骤，每个步骤只调用一个工具。
2. 全流程最多安排3个步骤；若一个数据源即可充分回答，则使用1步；若存在不确定性/需验证，优先在不同数据源上补充查询（避免重复同一来源）。
3. op 字段必须是可用工具名称。
4. input 字段是传给工具的字符串。
5. 在选择步骤时，尽可能覆盖不同的数据源/索引类型（例如：结构化检索、非结构化文档、实时信息等），以获得更优答案。
6. 对每一步的 reasoning 要说明选择该数据源的理由与增益（验证、补全、时效性等）。

可用工具：
{toolList}

回复策略：
{strategy}
`

    const promptTemplate = SystemMessagePromptTemplate.fromTemplate(plannerPrompt)

    const response = await LLM.predict(promptTemplate, {
      meta: { round_id: roundId },
      responseJSON: true,
      reasoningEffort: 'low'
    },
    { toolList, strategy })

    this.logMessage('Rag Planner response:', response)

    // The LLM returns a JSON string; parse it into the Plan type.  In
    // case of malformed output, we fall back to an empty plan to
    // prevent runtime exceptions.s
    try {
      return this.plannerResponseToPlan(response, strategy, chatId, roundId)
    } catch (err) {
      console.warn('Failed to parse planner response', err)
      return { steps: [] }
    }
  }

  private static async executePlan(plan: Plan) {
    let result = ''
    for (const step of plan.steps) {
      const tool = await KnowledgeRagTool.getToolByKey(step.op)
      if (!tool) {
        continue
      }
      const toolResult = await tool.execute(step.input)
      result += `
操作：${step.op}
结果：${toolResult}`
    }

    return result
  }


  private static async plannerResponseToPlan(response: string, strategy: string, chatId: string, roundId: string) {
    const jsonData = JSON.parse(response)
    return {
      steps: jsonData.steps.map((step: any) => {
        const reasoning = step.reasoning
        const op = step.op
        const plan = step.plan

        return {
          reasoning,
          plan,
          op,
          input: {
            chatId,
            roundId,
            strategy,
            searchKey:step.input
          },
        }
      })
    } as Plan
  }


  private static logMessage(key: string, data: any) {
    if (Config.setting.localTest) {
      console.log(key, data)
    }
  }



}