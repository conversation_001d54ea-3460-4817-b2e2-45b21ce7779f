import { KnowledgeRag } from '../knowledge_rag'
import { DataService } from '../../../../../getter/getData'


describe('KnowledgeRagTest', () => {


  it('testSearch', async () => {
    const strategy = '15:30 提前服务：1) 再次确认他偏好的APP首页直播入口与回放入口的最简路径说明；2) 发送“果园冥想不上火指南”（无画面=正常、跟呼吸/体感走、抓关键词即可）、“记录模板：果实/果树/大门/呼吸是否顺、四季变化+当下情绪/身体部位体感”与示例；3) 若需陪孩子，给出耳机旁听+课后10分钟内速记方案。'

    DataService.getCurrentTime = async () => {
      return {
        day: 2,
        is_course_week: true,
        time: '09:00:00'
      }
    }


    const res = await KnowledgeRag.search(strategy, 'chat_id', 'round_id')

    console.log(res)
  }, 9e8)

})