import { PlannerMaterialRag } from '../material_rag'
import { UUID } from '../../../../../../lib/uuid/uuid'


describe('materialRagTest', () => {

  it('testExtractMaterial', async () => {
    const plan = '15:30 提前服务：1) 再次确认他偏好的APP首页直播入口与回放入口的最简路径说明；2) 发送“果园冥想不上火指南”（无画面=正常、跟呼吸/体感走、抓关键词即可）、“记录模板：果实/果树/大门/呼吸是否顺、四季变化+当下情绪/身体部位体感”与示例；3) 若需陪孩子，给出耳机旁听+课后10分钟内速记方案。'
    await PlannerMaterialRag.extractMaterial('local', plan)
  }, 9e8)
})