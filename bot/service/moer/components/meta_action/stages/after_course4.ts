import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo, PostAction } from './post_action'
import { isScheduleTimeAfter } from '../../schedule/creat_schedule_task'
import { DataService } from '../../../getter/getData'
import { FreeThink } from '../../agent/freethink'


export class AfterCourse4 extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse4 = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 4, time: '22:20:00' })
    return Promise.resolve(afterCourse4)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    }
    return Promise.resolve(actionMap)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionAfterCourse4)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptAfterCourse4)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

}