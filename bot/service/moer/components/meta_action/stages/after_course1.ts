import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo } from './post_action'
import { DataService } from '../../../getter/getData'
import { isScheduleTimeAfter } from '../../schedule/creat_schedule_task'
import { FreeThink } from '../../agent/freethink'


export class AfterCourse1 extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse1 = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })
    return Promise.resolve(afterCourse1)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(undefined)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionAfterCourse1)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptAfterCourse1)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }


}