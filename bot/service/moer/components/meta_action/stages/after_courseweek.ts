import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo, PostAction } from './post_action'
import { DataService } from '../../../getter/getData'
import { FreeThink } from '../../agent/freethink'


export class AfterCourseWeek extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourseWeek = Boolean(currentTime.post_course_week) && currentTime.day != 1
    return Promise.resolve(afterCourseWeek)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发起报名邀约': PostAction.sendInvitation,
    }
    return Promise.resolve(actionMap)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(FreeThink.metaActionAfterCourseWeek)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(FreeThink.thinkPromptAfterCourseWeek)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }

}