import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'

describe('FindTestData', () => {
  it('should find chat_ids with recent data', async () => {
    try {
      // 查找最近30天有聊天记录的 chat_id
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentChats = await PrismaMongoClient.getInstance().chat_history.findMany({
        where: {
          created_at: {
            gte: thirtyDaysAgo
          },
          NOT: {
            chat_state: null
          }
        },
        select: {
          chat_id: true,
          created_at: true,
          chat_state: true
        },
        orderBy: {
          created_at: 'desc'
        },
        take: 10
      })

      console.log('=== 最近30天有 chat_state 的聊天记录 ===')
      for (const chat of recentChats) {
        const dateStr = chat.created_at.toISOString().split('T')[0]
        console.log(`Chat ID: ${chat.chat_id}`)
        console.log(`日期: ${dateStr}`)
        console.log(`chat_state 存在: ${chat.chat_state ? '是' : '否'}`)

        if (chat.chat_state) {
          const chatState = chat.chat_state as any
          const userSlots = chatState.userSlots || {}
          console.log(`userSlots 键数量: ${Object.keys(userSlots).length}`)
          if (Object.keys(userSlots).length > 0) {
            console.log(`userSlots 示例:`, JSON.stringify(userSlots, null, 2).substring(0, 200) + '...')
          }
        }
        console.log('---')
      }

      expect(recentChats.length).toBeGreaterThan(0)
    } catch (error) {
      console.error('查询失败:', error)
      throw error
    }
  }, 60000)

  it('should find specific chat_id data by date range', async () => {
    try {
      // 查找特定时间范围内的数据
      const startDate = new Date('2024-08-01')
      const endDate = new Date('2024-12-31')

      const chatsInRange = await PrismaMongoClient.getInstance().chat_history.findMany({
        where: {
          created_at: {
            gte: startDate,
            lte: endDate
          },
          NOT: {
            chat_state: null
          }
        },
        select: {
          chat_id: true,
          created_at: true,
          chat_state: true
        },
        orderBy: {
          created_at: 'desc'
        },
        take: 5
      })

      console.log('=== 2024年8月-12月有 chat_state 的聊天记录 ===')
      for (const chat of chatsInRange) {
        const dateStr = chat.created_at.toISOString().split('T')[0]
        console.log(`Chat ID: ${chat.chat_id}`)
        console.log(`日期: ${dateStr}`)

        if (chat.chat_state) {
          const chatState = chat.chat_state as any
          const userSlots = chatState.userSlots || {}
          console.log(`userSlots:`, JSON.stringify(userSlots, null, 2))
        }
        console.log('---')
      }

      expect(chatsInRange.length).toBeGreaterThan(0)
    } catch (error) {
      console.error('查询失败:', error)
      throw error
    }
  }, 60000)
})
