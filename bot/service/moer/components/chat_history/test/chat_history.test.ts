import { ChatHistoryService } from '../chat_history'


describe('ChatHistoryService', () => {

  it('hasConversationRecentlyTest', async () => {
    const twoDaysAgo = new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)
    const hasConversationInTwoDays = await ChatHistoryService.hasConversationRecently('7881299504929898_1688855184697783', twoDaysAgo)
    console.log(hasConversationInTwoDays)
  }, 60000)

})