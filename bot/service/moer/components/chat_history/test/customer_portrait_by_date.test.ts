import { ChatHistoryService } from '../chat_history'
import { UserSlots } from '../../flow/helper/slotsExtract'

describe('CustomerPortraitByDate', () => {
  /**
   * 测试函数：根据日期和 chat_id 获取客户画像
   *
   * 功能说明：
   * 1. 给定日期和 chat_id
   * 2. 从 chat_history 过滤出这个日期的第一条消息
   * 3. 获取该消息的 chat_state.userSlots (即 moerUserSlots)
   * 4. 将其 stringify 作为客户画像返回
   *
   * @param chatId 聊天ID
   * @param targetDate 目标日期，格式：YYYY-MM-DD
   * @returns 客户画像的 JSON 字符串
   */
  async function getCustomerPortraitByDate(chatId: string, targetDate: string): Promise<string> {
    const userSlots =  await ChatHistoryService.getCustomerPortraitByDate(chatId, targetDate)
    const tmp = UserSlots.fromRecord(userSlots)

    return tmp.toString()
  }

  it('should get customer portrait by date', async () => {
    // 测试用例 1：使用真实的 chat_id 和日期
    const chatId = '7881303189944040_1688857949631398'
    const targetDate = '2025-08-09' // 修改为实际存在数据的日期

    const result = await getCustomerPortraitByDate(chatId, targetDate)

    console.log('=== 客户画像结果 ===')
    console.log('Chat ID:', chatId)
    console.log('目标日期:', targetDate)
    console.log('客户画像:', result)

    // 验证返回结果是有效的 JSON
    expect(() => JSON.parse(result)).not.toThrow()

    const parsedResult = JSON.parse(result)
    console.log('解析后的客户画像:', parsedResult)
  }, 60000)

  it('should handle non-existent date', async () => {
    // 测试用例 2：测试不存在的日期
    const chatId = '7881299504929898_1688855184697783'
    const targetDate = '2020-01-01' // 不存在数据的日期

    const result = await getCustomerPortraitByDate(chatId, targetDate)

    console.log('=== 不存在日期的测试结果 ===')
    console.log('Chat ID:', chatId)
    console.log('目标日期:', targetDate)
    console.log('结果:', result)

    const parsedResult = JSON.parse(result)
    expect(parsedResult.error).toBeDefined()
  }, 60000)

  it('should handle invalid chat_id', async () => {
    // 测试用例 3：测试无效的 chat_id
    const chatId = 'invalid_chat_id'
    const targetDate = '2024-12-01'

    const result = await getCustomerPortraitByDate(chatId, targetDate)

    console.log('=== 无效 Chat ID 的测试结果 ===')
    console.log('Chat ID:', chatId)
    console.log('目标日期:', targetDate)
    console.log('结果:', result)

    const parsedResult = JSON.parse(result)
    expect(parsedResult.error).toBeDefined()
  }, 60000)

  it('should test with multiple dates', async () => {
    // 测试用例 4：测试多个日期
    const chatId = '7881299504929898_1688855184697783'
    const dates = ['2024-11-01', '2024-11-15', '2024-12-01', '2024-12-15']

    console.log('=== 多日期测试结果 ===')

    for (const date of dates) {
      const result = await getCustomerPortraitByDate(chatId, date)
      console.log(`\n日期 ${date}:`)
      console.log(result)

      // 验证返回结果是有效的 JSON
      expect(() => JSON.parse(result)).not.toThrow()
    }
  }, 120000)
})
