import logger from '../../../../../model/logger/logger'
import RateLimiter from '../../../../../model/redis/rate_limiter'
import { Planner } from '../plan/planner'
import { Job, Worker } from 'bullmq'
import { RedisDB } from '../../../../../model/redis/redis'
import { Config } from '../../../../../config/config'
import { ITask } from '../types'
import { getUserId } from '../../../../../config/chat_id'
import { AsyncLock } from '../../../../../lib/lock/lock'
import { TaskManager } from './task_manager'
import { TaskStatus } from '@prisma/client'
import { getPrompt } from '../../agent/prompt'
import { ContextBuilder } from '../../agent/context'
import { SalesNodeHelper } from '../../flow/helper/salesNodeHelper'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { PlannerMaterialRag } from '../../rag/planner/material_rag'
import { UUID } from '../../../../../lib/uuid/uuid'
import { LLMNode } from '../../flow/nodes/llm'
import { sendMsg } from '../../visualized_sop/visualized_sop_processor'
import { KnowledgeRag } from '../../rag/planner/knowledge_rag/knowledge_rag'

export class TaskWorker {
  public static start() {
    const queueName = Planner.getPlannerSOPQueueName(Config.setting.wechatConfig?.id as string)

    new Worker(queueName, async (job: Job) => {
      const data = job.data as ITask
      console.log('process Task:', JSON.stringify(data, null, 4))

      // 如果任务已经被 被动回复完成了不执行任务
      const task = await TaskManager.getTaskById(data.id)
      if (task && task.status !== TaskStatus.TODO) { return }
      if (!task) { return }
      await TaskManager.updateStatus(data.id, TaskStatus.DONE)
      await TaskWorker.processTask(data.chat_id, task.description)
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (err) => {
      logger.error({
        error_name: err.name,
        error_message: err.message,
        error_stack: err.stack,
      }, 'task_worker 发生未捕获错误', err)
    })
  }

  public static async processTask(chatId: string, mainTask: string) {
    // 频率限制，12 小时不超过 x 条
    const limiter = new RateLimiter({
      windowSize: 12 * 60 * 60,
      maxRequests: 5
    })
    const isAllowed = await limiter.isAllowed('planner_sop', chatId)
    if (!isAllowed) { return }

    const round_id = UUID.v4()

    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 防止跟对话撞车
      logger.log({ chatId: chatId }, '执行任务', mainTask)
      const userId = getUserId(chatId)
      const freeKickPrompt = await getPrompt('free-kick')
      const metaActions = ''
      const availableMaterials = await PlannerMaterialRag.extractMaterial(chatId, mainTask, round_id)
      const retrievedKnowledge = await KnowledgeRag.search(mainTask, chatId, round_id)
      const dialogHistory = await SalesNodeHelper.getChatHistory(chatId, 2, 6, false)
      const output = await LLM.predict(
        freeKickPrompt,
        {
          responseJSON: true,
          meta: {
            promptName: 'free_kick',
            chat_id: chatId,
            round_id: round_id,
          }
        },
        {
          courseConfig: '', // await ContextBuilder.courseConfigFreeKick(chatId),
          metaActions: metaActions,
          availableMaterials: availableMaterials,
          retrievedKnowledge: retrievedKnowledge,
          customerBehavior: await ContextBuilder.getCustomerBehavior(chatId),
          customerPortrait: await ContextBuilder.getCustomerPortrait(chatId),
          dialogHistory: dialogHistory,
          temporalInformation: await ContextBuilder.getTimeInformation(chatId),
          mainTask: mainTask,
        }
      )
      let think: string = ''
      let activate: boolean = false
      let action: string[] = []
      let material: string[] = []
      let content: string = ''

      try {
        const parsedOutput = JSON.parse(output)
        think = parsedOutput.think
        activate = parsedOutput.activate
        action = parsedOutput.action
        material = parsedOutput.material
        content = parsedOutput.content
      } catch (error) {
        logger.error('FreeThink 解析 JSON 失败:', error)
      }
      logger.debug({ chat_id: chatId }, `think: ${think}
activate: ${activate}
action: ${JSON.stringify(action)}
material: ${JSON.stringify(material)}
content: ${content}`)
      if (activate) {
        const splitSentence = LLMNode.splitIntoSentencesWithMaxSentences(content, 2)
        await sendMsg(userId, chatId, splitSentence, '营销信息', true, round_id, 'free-kick')
        await PlannerMaterialRag.sendMaterial(chatId, material)
      }
    })
  }
}