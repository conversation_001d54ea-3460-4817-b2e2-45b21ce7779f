// import { Planner } from '../plan/planner'
//
// import { TaskScheduler, SchedulePlanItem } from '../task/task_scheduler'
// // import { PromptBuilder } from '../../agent/context'
// // import { getBotId } from '../../../../../config/chat_id'
// // import { getGeneralSopKey, listSOPByChatId } from '../../flow/schedule/task_starter'
// // import { Queue } from 'bullmq'
// // import { RedisDB } from '../../../../../model/redis/redis'
// // import logger from '../../../../../model/logger/logger'
// import { UUID } from '../../../../../lib/uuid/uuid'
// import { RedisCacheDB } from '../../../../../model/redis/redis_cache'
// import dayjs from 'dayjs'
// import { ITask } from '../types'
//
// // // Mock 外部依赖
// // jest.mock('../task_scheduler')
// // jest.mock('../../agent/context')
// // jest.mock('../../../../../config/chat_id')
// // jest.mock('../../flow/schedule/task_starter')
// // jest.mock('../../../../../model/redis/redis')
// // jest.mock('bullmq')
// //
// // const MockedTaskScheduler = TaskScheduler as jest.MockedClass<typeof TaskScheduler>
// // const MockedPromptBuilder = PromptBuilder as jest.Mocked<typeof PromptBuilder>
// // const MockedGetBotId = getBotId as jest.MockedFunction<typeof getBotId>
// // const MockedListSOPByChatId = listSOPByChatId as jest.MockedFunction<typeof listSOPByChatId>
// // const MockedQueue = Queue as jest.MockedClass<typeof Queue>
//
// // describe('Planner 全流程测试 - taskSchedule', () => {
// //   const testChatId = 'user123_bot456'
// //   const testBotId = 'bot456'
// //   const testRoundId = 'round_789'
// //   const mockCurrentTime = '2025-01-15 14:30:00'
// //
// //   beforeEach(() => {
// //     jest.clearAllMocks()
// //
// //     // 设置 Config.setting.wechatConfig 避免 getGeneralSopKey 抛出错误
// //     const { Config } = require('../../../../../config/config')
// //     Config.setting.wechatConfig = {
// //       id: testBotId,
// //       name: 'Test Bot',
// //       botUserId: 'test_user',
// //       notifyGroupId: 'test_group',
// //       classGroupId: 'test_class_group',
// //       courseNo: 1
// //     }
// //
// //     // 设置基础 mocks
// //     MockedGetBotId.mockReturnValue(testBotId)
// //     MockedPromptBuilder.getTimeInformation.mockResolvedValue(mockCurrentTime)
// //     MockedListSOPByChatId.mockResolvedValue([])
// //
// //     // Mock Redis 和 Queue
// //     const mockRedisInstance = {
// //       set: jest.fn(),
// //       get: jest.fn(),
// //       del: jest.fn()
// //     }
// //     jest.mocked(RedisDB.getInstance).mockReturnValue(mockRedisInstance as any)
// //
// //     const mockQueueInstance = {
// //       getDelayed: jest.fn().mockResolvedValue([]),
// //       add: jest.fn().mockResolvedValue({}),
// //       addBulk: jest.fn().mockResolvedValue([])
// //     }
// //     MockedQueue.mockImplementation(() => mockQueueInstance as any)
// //
// //     // Mock filterSOPByDate
// //     jest.spyOn(Planner as any, 'filterSOPByDate').mockResolvedValue([])
// //   })
// //
// //   describe('完整的任务调度流程', () => {
// //     it('应该完成从 planner 生成任务到调度为 SOP 的完整流程', async () => {
// //       // 1. 模拟 planner 生成的任务
// //       const generatedTasks: ITask[] = [
// //         {
// //           id: 'task_urgent_response_1234',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '及时响应客户需求',
// //           description: '客户刚刚询问了课程相关问题，需要立即回复并提供帮助',
// //           status: 'TODO',
// //           priority: 0,
// //           created_at: new Date('2025-01-15T14:30:00Z'),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_daily_greeting_5678',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '维护客户关系',
// //           description: '明天早上发送个性化问候消息',
// //           status: 'TODO',
// //           priority: 1,
// //           created_at: new Date('2025-01-15T14:30:00Z'),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_course_reminder_9999',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '提升课程参与度',
// //           description: '今晚课程开始前30分钟发送提醒',
// //           status: 'TODO',
// //           priority: 2,
// //           created_at: new Date('2025-01-15T14:30:00Z'),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_followup_check_7777',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '课后跟进',
// //           description: '课程结束后第二天询问学习感受和收获',
// //           status: 'TODO',
// //           priority: 3,
// //           created_at: new Date('2025-01-15T14:30:00Z'),
// //           completed_at: null
// //         }
// //       ]
// //
// //       // 2. 模拟现有的 SOP 调度（避免冲突）
// //       const existingSchedule = [
// //         { description: '系统维护通知', time: '2025-01-15 23:00:00' },
// //         { description: '每日数据统计', time: '2025-01-16 06:00:00' }
// //       ]
// //       jest.spyOn(Planner as any, 'filterSOPByDate').mockResolvedValue(existingSchedule)
// //
// //       // 3. 模拟 TaskScheduler 的调度结果
// //       const scheduledResults: SchedulePlanItem[] = [
// //         {
// //           task_id: '1234', // 紧急任务
// //           urgency_level: 'urgent',
// //           task_type: 'engagement_prompt',
// //           scheduled_time: 'now'
// //         },
// //         {
// //           task_id: '5678', // 日常问候
// //           urgency_level: 'normal',
// //           task_type: 'daily_greeting',
// //           scheduled_time: '2025-01-16 08:30:00'
// //         },
// //         {
// //           task_id: '9999', // 课程提醒
// //           urgency_level: 'normal',
// //           task_type: 'pre_class_reminder',
// //           scheduled_time: '2025-01-15 19:30:00'
// //         },
// //         {
// //           task_id: '7777', // 课后跟进
// //           urgency_level: 'normal',
// //           task_type: 'post_class_follow_up',
// //           scheduled_time: '2025-01-16 10:00:00'
// //         }
// //       ]
// //
// //       const mockScheduleTask = jest.fn().mockResolvedValue(scheduledResults)
// //       MockedTaskScheduler.mockImplementation(() => ({
// //         scheduleTask: mockScheduleTask
// //       } as any))
// //
// //       // 4. 执行 taskSchedule 流程
// //       const result = await Planner.taskSchedule(testChatId, generatedTasks)
// //
// //       // 5. 验证整个流程的正确性
// //
// //       // 验证时间信息获取
// //       expect(MockedPromptBuilder.getTimeInformation).toHaveBeenCalledWith(testChatId)
// //
// //       // 验证现有调度信息获取
// //       expect(Planner['filterSOPByDate']).toHaveBeenCalledWith(
// //         testChatId,
// //         expect.any(Date),
// //         expect.any(Date)
// //       )
// //
// //       // 验证调度器调用参数
// //       expect(mockScheduleTask).toHaveBeenCalledWith({
// //         current_time: mockCurrentTime,
// //         tasks_to_schedule: [
// //           { task_id: '1234', task_description: '客户刚刚询问了课程相关问题，需要立即回复并提供帮助' },
// //           { task_id: '5678', task_description: '明天早上发送个性化问候消息' },
// //           { task_id: '9999', task_description: '今晚课程开始前30分钟发送提醒' },
// //           { task_id: '7777', task_description: '课程结束后第二天询问学习感受和收获' }
// //         ],
// //         existing_schedule: existingSchedule
// //       })
// //
// //       // 验证返回结果
// //       expect(result).toHaveLength(4)
// //       expect(result).toEqual([
// //         {
// //           task_id: 'task_urgent_response_1234',
// //           urgency_level: 'urgent',
// //           task_type: 'engagement_prompt',
// //           scheduled_time: 'now'
// //         },
// //         {
// //           task_id: 'task_daily_greeting_5678',
// //           urgency_level: 'normal',
// //           task_type: 'daily_greeting',
// //           scheduled_time: '2025-01-16 08:30:00'
// //         },
// //         {
// //           task_id: 'task_course_reminder_9999',
// //           urgency_level: 'normal',
// //           task_type: 'pre_class_reminder',
// //           scheduled_time: '2025-01-15 19:30:00'
// //         },
// //         {
// //           task_id: 'task_followup_check_7777',
// //           urgency_level: 'normal',
// //           task_type: 'post_class_follow_up',
// //           scheduled_time: '2025-01-16 10:00:00'
// //         }
// //       ])
// //
// //       // 验证紧急任务被正确识别
// //       const urgentTasks = result.filter((task) => task.urgency_level === 'urgent')
// //       expect(urgentTasks).toHaveLength(1)
// //       expect(urgentTasks[0].scheduled_time).toBe('now')
// //
// //       // 验证任务类型分配正确
// //       const taskTypes = result.map((task) => task.task_type)
// //       expect(taskTypes).toContain('engagement_prompt')
// //       expect(taskTypes).toContain('daily_greeting')
// //       expect(taskTypes).toContain('pre_class_reminder')
// //       expect(taskTypes).toContain('post_class_follow_up')
// //     })
// //
// //     it('应该正确处理有调度冲突的复杂场景', async () => {
// //       // 模拟密集的现有调度
// //       const denseExistingSchedule = [
// //         { description: '早晨问候', time: '2025-01-16 08:00:00' },
// //         { description: '上午提醒', time: '2025-01-16 08:30:00' },
// //         { description: '中午关怀', time: '2025-01-16 12:00:00' },
// //         { description: '下午跟进', time: '2025-01-16 14:00:00' },
// //         { description: '课前提醒', time: '2025-01-16 19:30:00' },
// //         { description: '课后总结', time: '2025-01-16 21:30:00' }
// //       ]
// //       jest.spyOn(Planner as any, 'filterSOPByDate').mockResolvedValue(denseExistingSchedule)
// //
// //       const inputTasks: ITask[] = [
// //         {
// //           id: 'task_new_greeting_1111',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '新的问候',
// //           description: '发送新的个性化问候',
// //           status: 'TODO',
// //           priority: 1,
// //           created_at: new Date(),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_new_reminder_2222',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '新的提醒',
// //           description: '发送新的课程提醒',
// //           status: 'TODO',
// //           priority: 2,
// //           created_at: new Date(),
// //           completed_at: null
// //         }
// //       ]
// //
// //       // TaskScheduler 应该避开冲突时间
// //       const conflictAvoidedResults: SchedulePlanItem[] = [
// //         {
// //           task_id: '1111',
// //           urgency_level: 'normal',
// //           task_type: 'daily_greeting',
// //           scheduled_time: '2025-01-16 09:00:00' // 避开了 08:00 和 08:30
// //         },
// //         {
// //           task_id: '2222',
// //           urgency_level: 'normal',
// //           task_type: 'pre_class_reminder',
// //           scheduled_time: '2025-01-16 19:00:00' // 避开了 19:30
// //         }
// //       ]
// //
// //       const mockScheduleTask = jest.fn().mockResolvedValue(conflictAvoidedResults)
// //       MockedTaskScheduler.mockImplementation(() => ({
// //         scheduleTask: mockScheduleTask
// //       } as any))
// //
// //       const result = await Planner.taskSchedule(testChatId, inputTasks)
// //
// //       // 验证调度器收到了完整的现有调度信息
// //       expect(mockScheduleTask).toHaveBeenCalledWith(
// //         expect.objectContaining({
// //           existing_schedule: denseExistingSchedule
// //         })
// //       )
// //
// //       // 验证结果避开了冲突时间
// //       expect(result[0].scheduled_time).toBe('2025-01-16 09:00:00')
// //       expect(result[1].scheduled_time).toBe('2025-01-16 19:00:00')
// //     })
// //
// //     it('应该正确处理混合优先级和类型的任务', async () => {
// //       const mixedTasks: ITask[] = [
// //         {
// //           id: 'task_urgent_support_aaaa',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '紧急客服',
// //           description: '客户遇到技术问题需要立即协助',
// //           status: 'TODO',
// //           priority: 0,
// //           created_at: new Date(),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_value_content_bbbb',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '价值传递',
// //           description: '分享今日冥想小贴士',
// //           status: 'TODO',
// //           priority: 5,
// //           created_at: new Date(),
// //           completed_at: null
// //         },
// //         {
// //           id: 'task_engagement_cccc',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '互动促进',
// //           description: '询问客户今日练习情况',
// //           status: 'TODO',
// //           priority: 3,
// //           created_at: new Date(),
// //           completed_at: null
// //         }
// //       ]
// //
// //       const mixedResults: SchedulePlanItem[] = [
// //         {
// //           task_id: 'aaaa',
// //           urgency_level: 'urgent',
// //           task_type: 'engagement_prompt',
// //           scheduled_time: 'now'
// //         },
// //         {
// //           task_id: 'bbbb',
// //           urgency_level: 'normal',
// //           task_type: 'value_delivery',
// //           scheduled_time: '2025-01-16 15:00:00'
// //         },
// //         {
// //           task_id: 'cccc',
// //           urgency_level: 'normal',
// //           task_type: 'engagement_prompt',
// //           scheduled_time: '2025-01-16 18:00:00'
// //         }
// //       ]
// //
// //       const mockScheduleTask = jest.fn().mockResolvedValue(mixedResults)
// //       MockedTaskScheduler.mockImplementation(() => ({
// //         scheduleTask: mockScheduleTask
// //       } as any))
// //
// //       const result = await Planner.taskSchedule(testChatId, mixedTasks)
// //
// //       // 验证不同类型的任务都被正确处理
// //       expect(result).toHaveLength(3)
// //
// //       const urgentTask = result.find((task) => task.urgency_level === 'urgent')
// //       expect(urgentTask).toBeDefined()
// //       expect(urgentTask!.scheduled_time).toBe('now')
// //       expect(urgentTask!.task_id).toBe('task_urgent_support_aaaa')
// //
// //       const valueTask = result.find((task) => task.task_type === 'value_delivery')
// //       expect(valueTask).toBeDefined()
// //       expect(valueTask!.task_id).toBe('task_value_content_bbbb')
// //
// //       const engagementTasks = result.filter((task) => task.task_type === 'engagement_prompt')
// //       expect(engagementTasks).toHaveLength(2)
// //     })
// //   })
// //
// //   describe('边界情况和错误处理', () => {
// //     it('应该处理空任务列表的情况', async () => {
// //       const mockScheduleTask = jest.fn().mockResolvedValue([])
// //       MockedTaskScheduler.mockImplementation(() => ({
// //         scheduleTask: mockScheduleTask
// //       } as any))
// //
// //       const result = await Planner.taskSchedule(testChatId, [])
// //
// //       expect(result).toEqual([])
// //       expect(mockScheduleTask).toHaveBeenCalledWith({
// //         current_time: mockCurrentTime,
// //         tasks_to_schedule: [],
// //         existing_schedule: []
// //       })
// //     })
// //
// //     it('应该处理调度器返回空结果的情况', async () => {
// //       const inputTasks: ITask[] = [
// //         {
// //           id: 'task_test_1234',
// //           chat_id: testChatId,
// //           round_id: testRoundId,
// //           overall_goal: '测试',
// //           description: '测试任务',
// //           status: 'TODO',
// //           priority: 1,
// //           created_at: new Date(),
// //           completed_at: null
// //         }
// //       ]
// //
// //       const mockScheduleTask = jest.fn().mockResolvedValue([])
// //       MockedTaskScheduler.mockImplementation(() => ({
// //         scheduleTask: mockScheduleTask
// //       } as any))
// //
// //       const result = await Planner.taskSchedule(testChatId, inputTasks)
// //
// //       expect(result).toEqual([])
// //       expect(mockScheduleTask).toHaveBeenCalledWith(
// //         expect.objectContaining({
// //           tasks_to_schedule: [
// //             { task_id: '1234', task_description: '测试任务' }
// //           ]
// //         })
// //       )
// //     })
// //   })
// // })
//
//
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const testChatId = '7881299785949759_1688855587643034'
//     const testRoundId = UUID.v4()
//
//     // 1. 模拟 planner 生成的任务
//     const generatedTasks: ITask[] = [
//       {
//         id: 'task_urgent_response_1234',
//         chat_id: testChatId,
//         round_id: testRoundId,
//         overall_goal: '及时响应客户需求',
//         description: '客户刚刚询问了课程相关问题，需要立即回复并提供帮助',
//         status: 'TODO',
//         priority: 0,
//         created_at: new Date('2025-01-15T14:30:00Z'),
//         completed_at: null
//       },
//       {
//         id: 'task_daily_greeting_5678',
//         chat_id: testChatId,
//         round_id: testRoundId,
//         overall_goal: '维护客户关系',
//         description: '明天早上发送个性化问候消息',
//         status: 'TODO',
//         priority: 1,
//         created_at: new Date('2025-01-15T14:30:00Z'),
//         completed_at: null
//       },
//       {
//         id: 'task_course_reminder_9999',
//         chat_id: testChatId,
//         round_id: testRoundId,
//         overall_goal: '提升课程参与度',
//         description: '今晚课程开始前30分钟发送提醒',
//         status: 'TODO',
//         priority: 2,
//         created_at: new Date('2025-01-15T14:30:00Z'),
//         completed_at: null
//       },
//       {
//         id: 'task_followup_check_7777',
//         chat_id: testChatId,
//         round_id: testRoundId,
//         overall_goal: '课后跟进',
//         description: '课程结束后第二天询问学习感受和收获',
//         status: 'TODO',
//         priority: 3,
//         created_at: new Date('2025-01-15T14:30:00Z'),
//         completed_at: null
//       }
//     ]
//
//     // 2. 模拟现有的 SOP 调度（避免冲突）
//     const existingSchedule = [
//       { description: '系统维护通知', time: '2025-01-15 23:00:00' },
//       { description: '每日数据统计', time: '2025-01-16 06:00:00' }
//     ]
//
//
//     // 3. 模拟 TaskScheduler 的调度结果
//     const scheduledResults: SchedulePlanItem[] = [
//       {
//         task_id: '1234', // 紧急任务
//         urgency_level: 'urgent',
//         task_type: 'engagement_prompt',
//         scheduled_time: 'now'
//       },
//       {
//         task_id: '5678', // 日常问候
//         urgency_level: 'normal',
//         task_type: 'daily_greeting',
//         scheduled_time: '2025-01-16 08:30:00'
//       },
//       {
//         task_id: '9999', // 课程提醒
//         urgency_level: 'normal',
//         task_type: 'pre_class_reminder',
//         scheduled_time: '2025-01-15 19:30:00'
//       },
//       {
//         task_id: '7777', // 课后跟进
//         urgency_level: 'normal',
//         task_type: 'post_class_follow_up',
//         scheduled_time: '2025-01-16 10:00:00'
//       }
//     ]
//
//     const result = await Planner.taskSchedule(testChatId, generatedTasks)
//
//     console.log(result)
//   }, 1E8)
//
//
//   it('sops', async () => {
//     const sops = await Planner.filterSOPByDate('7881299785949759_1688855587643034', new Date(), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000))
//
//     // redis 中取回对应的 title
//     const redisValue = await new RedisCacheDB('moer:1688855587643034:visualized_sop').get()
//
//     // for (const sop of sops) {
//     //   console.log(sop.name)
//     // }
//
//     // 构建 SOP Map
//     const sopMap = new Map()
//     for (const sop of redisValue) {
//       sopMap.set(sop.id, sop.title)
//     }
//
//     for (const sop of sops) {
//       console.log(sopMap.get(sop.description), dayjs(sop.time).format('YYYY-MM-DD HH:mm:ss'))
//     }
//   }, 30000)
// })
