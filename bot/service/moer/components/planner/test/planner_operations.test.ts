// import { TaskManager } from '../task/task_manager'
// import { Planner } from '../plan/planner'
// import { PlanOperations } from '../types'
// import { TaskStatus } from '@prisma/client'
//
// describe('Planner Operations Test', () => {
//   const testChatId = 'test_chat_planner_ops'
//   const testRoundId = 'test_round_planner_ops'
//
//   beforeEach(async () => {
//     // 清理测试数据
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     if (activeTasks.length > 0) {
//       await TaskManager.cancelTasks(activeTasks.map((task) => task.id))
//     }
//   })
//
//   afterEach(async () => {
//     // 清理测试数据
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     if (activeTasks.length > 0) {
//       await TaskManager.cancelTasks(activeTasks.map((task) => task.id))
//     }
//   })
//
//   test('1. 测试新增任务操作', async () => {
//     const planOperations: PlanOperations = {
//       toAdd: ['新任务1', '新任务2', '新任务3'],
//       toUpdate: [],
//       toRemove: [],
//       toMerge: []
//     }
//
//     // 执行操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(3)
//     expect(activeTasks[0].description).toBe('新任务1')
//     expect(activeTasks[1].description).toBe('新任务2')
//     expect(activeTasks[2].description).toBe('新任务3')
//   })
//
//   test('2. 测试更新任务操作', async () => {
//     // 先创建一些任务
//     await TaskManager.createTasks(
//       testChatId,
//       ['原任务1', '原任务2', '原任务3'],
//       '测试目标',
//       testRoundId
//     )
//
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [
//         { id: '1', content: '更新后的任务1' },
//         { id: '3', content: '更新后的任务3' }
//       ],
//       toRemove: [],
//       toMerge: []
//     }
//
//     // 执行操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(3)
//     expect(activeTasks[0].description).toBe('更新后的任务1')
//     expect(activeTasks[1].description).toBe('原任务2') // 未更新
//     expect(activeTasks[2].description).toBe('更新后的任务3')
//   })
//
//   test('3. 测试删除任务操作', async () => {
//     // 先创建一些任务
//     await TaskManager.createTasks(
//       testChatId,
//       ['任务1', '任务2', '任务3', '任务4'],
//       '测试目标',
//       testRoundId
//     )
//
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [],
//       toRemove: ['2', '4'], // 删除第2和第4个任务
//       toMerge: []
//     }
//
//     // 执行操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(2)
//     expect(activeTasks[0].description).toBe('任务1')
//     expect(activeTasks[1].description).toBe('任务3')
//   })
//
//   test('4. 测试混合操作', async () => {
//     // 先创建一些任务
//     await TaskManager.createTasks(
//       testChatId,
//       ['原任务1', '原任务2', '原任务3'],
//       '测试目标',
//       testRoundId
//     )
//
//     const planOperations: PlanOperations = {
//       toAdd: ['新任务A', '新任务B'],
//       toUpdate: [
//         { id: '1', content: '更新的任务1' }
//       ],
//       toRemove: ['3'], // 删除第3个任务
//       toMerge: []
//     }
//
//     // 执行操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(4) // 2个原任务 + 2个新任务
//
//     // 检查更新的任务
//     const updatedTask = activeTasks.find((task) => task.description === '更新的任务1')
//     expect(updatedTask).toBeDefined()
//
//     // 检查未删除的任务
//     const task2 = activeTasks.find((task) => task.description === '原任务2')
//     expect(task2).toBeDefined()
//
//     // 检查新增的任务
//     const newTaskA = activeTasks.find((task) => task.description === '新任务A')
//     const newTaskB = activeTasks.find((task) => task.description === '新任务B')
//     expect(newTaskA).toBeDefined()
//     expect(newTaskB).toBeDefined()
//
//     // 检查删除的任务不存在
//     const deletedTask = activeTasks.find((task) => task.description === '原任务3')
//     expect(deletedTask).toBeUndefined()
//   })
//
//   test('5. 测试边界情况 - 无效的ID', async () => {
//     // 先创建一些任务
//     await TaskManager.createTasks(
//       testChatId,
//       ['任务1', '任务2'],
//       '测试目标',
//       testRoundId
//     )
//
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [
//         { id: '5', content: '无效更新' }, // 超出范围的ID
//         { id: '1', content: '有效更新' }
//       ],
//       toRemove: ['10', '2'], // 一个无效ID，一个有效ID
//       toMerge: []
//     }
//
//     // 执行操作应该不会报错，只处理有效的操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(1) // 只剩下第1个任务（已更新）
//     expect(activeTasks[0].description).toBe('有效更新')
//   })
//
//   test('6. 测试 activeTasks 为空时只执行新增操作', async () => {
//     // 确保没有活跃任务
//     const initialTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(initialTasks).toHaveLength(0)
//
//     const planOperations: PlanOperations = {
//       toAdd: ['新任务1', '新任务2'],
//       toUpdate: [
//         { id: '1', content: '这个更新不应该执行' }
//       ],
//       toRemove: ['1'], // 这个删除不应该执行
//       toMerge: []
//     }
//
//     // 执行操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )
//
//     // 验证结果：只有新增的任务，没有执行更新和删除
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(2)
//     expect(activeTasks[0].description).toBe('新任务1')
//     expect(activeTasks[1].description).toBe('新任务2')
//   })
// })
