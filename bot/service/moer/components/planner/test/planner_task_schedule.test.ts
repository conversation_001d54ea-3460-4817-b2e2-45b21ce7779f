// import { Planner } from '../plan/planner'
// import { TaskScheduler, SchedulePlanItem } from '../task/task_scheduler'
// import { ContextBuilder } from '../../agent/context'
// import { getBotId } from '../../../../../config/chat_id'
// import { listSOPByChatId } from '../../flow/schedule/task_starter'
// import { ITask } from '../types'
//
//
//
// // Mock 所有外部依赖
// jest.mock('../task/task_scheduler')
// jest.mock('../../agent/context')
// jest.mock('../../../../../config/chat_id')
// jest.mock('../../flow/schedule/task_starter')
//
// const MockedTaskScheduler = TaskScheduler as jest.MockedClass<typeof TaskScheduler>
// const MockedPromptBuilder = ContextBuilder as jest.Mocked<typeof ContextBuilder>
// const MockedGetBotId = getBotId as jest.MockedFunction<typeof getBotId>
// const MockedListSOPByChatId = listSOPByChatId as jest.MockedFunction<typeof listSOPByChatId>
//
// describe('Planner.taskSchedule', () => {
//   const testChatId = 'test_chat_123'
//   const testBotId = 'test_bot_456'
//   const mockCurrentTime = '2025-01-15 10:30:00'
//
//   beforeEach(() => {
//     // 重置所有 mocks
//     jest.clearAllMocks()
//
//     // 设置默认的 mock 返回值
//     MockedGetBotId.mockReturnValue(testBotId)
//     MockedPromptBuilder.getTimeInformation.mockResolvedValue(mockCurrentTime)
//     MockedListSOPByChatId.mockResolvedValue([])
//
//     // Mock filterSOPByDate 方法
//     jest.spyOn(Planner as any, 'filterSOPByDate').mockResolvedValue([
//       {
//         description: '现有任务1',
//         time: '2025-01-15 14:00:00'
//       }
//     ])
//
//     // Mock TaskScheduler 实例
//     const mockScheduleTask = jest.fn()
//     MockedTaskScheduler.mockImplementation(() => ({
//       scheduleTask: mockScheduleTask
//     } as any))
//   })
//
//   describe('正常流程测试', () => {
//     it('应该正确处理多个任务的调度流程', async () => {
//       // 准备测试数据
//       const inputTasks: ITask[] = [
//         {
//           id: 'task_12345678',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试目标',
//           description: '发送早安问候',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'task_87654321',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试目标',
//           description: '课程提醒',
//           status: 'TODO',
//           priority: 2,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: '5678',
//           urgency_level: 'normal',
//           task_type: 'daily_greeting',
//           scheduled_time: '2025-01-16 08:00:00'
//         },
//         {
//           task_id: '4321',
//           urgency_level: 'urgent',
//           task_type: 'pre_class_reminder',
//           scheduled_time: 'now'
//         }
//       ]
//
//       // 设置 TaskScheduler mock
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       // 执行测试
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证结果
//       expect(result).toHaveLength(2)
//       expect(result[0]).toEqual({
//         task_id: 'task_12345678', // 应该还原为完整ID
//         urgency_level: 'normal',
//         task_type: 'daily_greeting',
//         scheduled_time: '2025-01-16 08:00:00'
//       })
//       expect(result[1]).toEqual({
//         task_id: 'task_87654321', // 应该还原为完整ID
//         urgency_level: 'urgent',
//         task_type: 'pre_class_reminder',
//         scheduled_time: 'now'
//       })
//
//       // 验证依赖调用
//       expect(MockedPromptBuilder.getTimeInformation).toHaveBeenCalledWith(testChatId)
//       expect(mockScheduleTask).toHaveBeenCalledWith({
//         current_time: mockCurrentTime,
//         tasks_to_schedule: [
//           { task_id: '5678', task_description: '发送早安问候' },
//           { task_id: '4321', task_description: '课程提醒' }
//         ],
//         existing_schedule: [
//           {
//             description: '现有任务1',
//             time: '2025-01-15 14:00:00'
//           }
//         ]
//       })
//     })
//
//     it('应该正确截取和还原task_id', async () => {
//       const inputTasks: ITask[] = [
//         {
//           id: 'very_long_task_id_123456789',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试目标',
//           description: '测试任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: '6789', // 应该是原ID的后4位
//           urgency_level: 'normal',
//           task_type: 'engagement_prompt',
//           scheduled_time: '2025-01-16 10:00:00'
//         }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证ID截取逻辑
//       expect(mockScheduleTask).toHaveBeenCalledWith(
//         expect.objectContaining({
//           tasks_to_schedule: [
//             { task_id: '6789', task_description: '测试任务' }
//           ]
//         })
//       )
//
//       // 验证ID还原逻辑
//       expect(result[0].task_id).toBe('very_long_task_id_123456789')
//     })
//   })
//
//   describe('边界情况测试', () => {
//     it('应该处理空任务列表', async () => {
//       const mockScheduleTask = jest.fn().mockResolvedValue([])
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, [])
//
//       expect(result).toEqual([])
//       expect(mockScheduleTask).toHaveBeenCalledWith({
//         current_time: mockCurrentTime,
//         tasks_to_schedule: [],
//         existing_schedule: expect.any(Array)
//       })
//     })
//
//     it('应该处理单个任务', async () => {
//       const inputTasks: ITask[] = [
//         {
//           id: 'single_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '单个任务测试',
//           description: '单个任务描述',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: '1234',
//           urgency_level: 'normal',
//           task_type: 'value_delivery',
//           scheduled_time: '2025-01-16 15:00:00'
//         }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       expect(result).toHaveLength(1)
//       expect(result[0].task_id).toBe('single_task_1234')
//     })
//
//     it('应该处理短task_id（少于4位）', async () => {
//       const inputTasks: ITask[] = [
//         {
//           id: 'abc',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '短ID测试',
//           description: '短ID任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: 'abc', // 短ID应该保持原样
//           urgency_level: 'normal',
//           task_type: 'engagement_prompt',
//           scheduled_time: '2025-01-16 12:00:00'
//         }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       expect(result[0].task_id).toBe('abc')
//       expect(mockScheduleTask).toHaveBeenCalledWith(
//         expect.objectContaining({
//           tasks_to_schedule: [
//             { task_id: 'abc', task_description: '短ID任务' }
//           ]
//         })
//       )
//     })
//   })
//
//   describe('异常处理测试', () => {
//     it('应该处理PromptBuilder.getTimeInformation异常', async () => {
//       MockedPromptBuilder.getTimeInformation.mockRejectedValue(new Error('时间获取失败'))
//
//       const inputTasks: ITask[] = [
//         {
//           id: 'test_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '异常测试',
//           description: '异常测试任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       await expect(Planner.taskSchedule(testChatId, inputTasks)).rejects.toThrow('时间获取失败')
//     })
//
//     it('应该处理TaskScheduler.scheduleTask异常', async () => {
//       const mockScheduleTask = jest.fn().mockRejectedValue(new Error('调度失败'))
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const inputTasks: ITask[] = [
//         {
//           id: 'test_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '异常测试',
//           description: '异常测试任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       await expect(Planner.taskSchedule(testChatId, inputTasks)).rejects.toThrow('调度失败')
//     })
//
//     it('应该处理filterSOPByDate异常', async () => {
//       jest.spyOn(Planner as any, 'filterSOPByDate').mockRejectedValue(new Error('SOP过滤失败'))
//
//       const inputTasks: ITask[] = [
//         {
//           id: 'test_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '异常测试',
//           description: '异常测试任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       await expect(Planner.taskSchedule(testChatId, inputTasks)).rejects.toThrow('SOP过滤失败')
//     })
//   })
//
//   describe('集成测试', () => {
//     it('应该正确调用filterSOPByDate获取现有调度', async () => {
//       const mockFilterSOPByDate = jest.spyOn(Planner as any, 'filterSOPByDate')
//       mockFilterSOPByDate.mockResolvedValue([
//         { description: '现有任务1', time: '2025-01-15 14:00:00' },
//         { description: '现有任务2', time: '2025-01-16 09:00:00' }
//       ])
//
//       const mockScheduleTask = jest.fn().mockResolvedValue([])
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const inputTasks: ITask[] = [
//         {
//           id: 'test_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '集成测试',
//           description: '集成测试任务',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证filterSOPByDate被正确调用
//       expect(mockFilterSOPByDate).toHaveBeenCalledWith(
//         testChatId,
//         expect.any(Date), // 当前时间
//         expect.any(Date)  // 3天后的时间
//       )
//
//       // 验证时间范围（3天）
//       const calls = mockFilterSOPByDate.mock.calls[0]
//       const startDate = calls[1] as Date
//       const endDate = calls[2] as Date
//       const timeDiff = endDate.getTime() - startDate.getTime()
//       const threeDaysInMs = 3 * 24 * 60 * 60 * 1000
//       expect(timeDiff).toBe(threeDaysInMs)
//
//       // 验证现有调度信息被正确传递
//       expect(mockScheduleTask).toHaveBeenCalledWith(
//         expect.objectContaining({
//           existing_schedule: [
//             { description: '现有任务1', time: '2025-01-15 14:00:00' },
//             { description: '现有任务2', time: '2025-01-16 09:00:00' }
//           ]
//         })
//       )
//     })
//
//     it('应该正确处理复杂的任务调度场景', async () => {
//       // 模拟复杂的现有调度
//       jest.spyOn(Planner as any, 'filterSOPByDate').mockResolvedValue([
//         { description: '早晨问候', time: '2025-01-16 08:00:00' },
//         { description: '课程提醒', time: '2025-01-16 19:30:00' },
//         { description: '晚安消息', time: '2025-01-16 22:00:00' }
//       ])
//
//       const inputTasks: ITask[] = [
//         {
//           id: 'urgent_task_9999',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '紧急处理',
//           description: '客户刚刚提问需要立即回复',
//           status: 'TODO',
//           priority: 0,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'normal_task_8888',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '常规跟进',
//           description: '发送课程资料',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'followup_task_7777',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '后续跟进',
//           description: '课后感受收集',
//           status: 'TODO',
//           priority: 2,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: '9999',
//           urgency_level: 'urgent',
//           task_type: 'engagement_prompt',
//           scheduled_time: 'now'
//         },
//         {
//           task_id: '8888',
//           urgency_level: 'normal',
//           task_type: 'value_delivery',
//           scheduled_time: '2025-01-16 14:00:00'
//         },
//         {
//           task_id: '7777',
//           urgency_level: 'normal',
//           task_type: 'post_class_follow_up',
//           scheduled_time: '2025-01-17 10:00:00'
//         }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证结果
//       expect(result).toHaveLength(3)
//       expect(result[0]).toEqual({
//         task_id: 'urgent_task_9999',
//         urgency_level: 'urgent',
//         task_type: 'engagement_prompt',
//         scheduled_time: 'now'
//       })
//       expect(result[1]).toEqual({
//         task_id: 'normal_task_8888',
//         urgency_level: 'normal',
//         task_type: 'value_delivery',
//         scheduled_time: '2025-01-16 14:00:00'
//       })
//       expect(result[2]).toEqual({
//         task_id: 'followup_task_7777',
//         urgency_level: 'normal',
//         task_type: 'post_class_follow_up',
//         scheduled_time: '2025-01-17 10:00:00'
//       })
//
//       // 验证调度器接收到正确的参数
//       expect(mockScheduleTask).toHaveBeenCalledWith({
//         current_time: mockCurrentTime,
//         tasks_to_schedule: [
//           { task_id: '9999', task_description: '客户刚刚提问需要立即回复' },
//           { task_id: '8888', task_description: '发送课程资料' },
//           { task_id: '7777', task_description: '课后感受收集' }
//         ],
//         existing_schedule: [
//           { description: '早晨问候', time: '2025-01-16 08:00:00' },
//           { description: '课程提醒', time: '2025-01-16 19:30:00' },
//           { description: '晚安消息', time: '2025-01-16 22:00:00' }
//         ]
//       })
//     })
//   })
//
//   describe('数据转换测试', () => {
//     it('应该正确处理各种长度的task_id', async () => {
//       const inputTasks: ITask[] = [
//         {
//           id: 'a', // 1位
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试',
//           description: '1位ID',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'ab', // 2位
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试',
//           description: '2位ID',
//           status: 'TODO',
//           priority: 2,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'abc', // 3位
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试',
//           description: '3位ID',
//           status: 'TODO',
//           priority: 3,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'abcd', // 4位
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试',
//           description: '4位ID',
//           status: 'TODO',
//           priority: 4,
//           created_at: new Date(),
//           completed_at: null
//         },
//         {
//           id: 'abcdefghij', // 10位
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '测试',
//           description: '10位ID',
//           status: 'TODO',
//           priority: 5,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         { task_id: 'a', urgency_level: 'normal', task_type: 'daily_greeting', scheduled_time: '2025-01-16 08:00:00' },
//         { task_id: 'ab', urgency_level: 'normal', task_type: 'daily_greeting', scheduled_time: '2025-01-16 08:30:00' },
//         { task_id: 'abc', urgency_level: 'normal', task_type: 'daily_greeting', scheduled_time: '2025-01-16 09:00:00' },
//         { task_id: 'abcd', urgency_level: 'normal', task_type: 'daily_greeting', scheduled_time: '2025-01-16 09:30:00' },
//         { task_id: 'ghij', urgency_level: 'normal', task_type: 'daily_greeting', scheduled_time: '2025-01-16 10:00:00' }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证ID截取逻辑
//       expect(mockScheduleTask).toHaveBeenCalledWith(
//         expect.objectContaining({
//           tasks_to_schedule: [
//             { task_id: 'a', task_description: '1位ID' },
//             { task_id: 'ab', task_description: '2位ID' },
//             { task_id: 'abc', task_description: '3位ID' },
//             { task_id: 'abcd', task_description: '4位ID' },
//             { task_id: 'ghij', task_description: '10位ID' } // 应该是后4位
//           ]
//         })
//       )
//
//       // 验证ID还原逻辑
//       expect(result.map((r) => r.task_id)).toEqual([
//         'a',
//         'ab',
//         'abc',
//         'abcd',
//         'abcdefghij'
//       ])
//     })
//
//     it('应该保持任务描述和其他字段不变', async () => {
//       const inputTasks: ITask[] = [
//         {
//           id: 'test_task_1234',
//           chat_id: testChatId,
//           round_id: 'round_1',
//           overall_goal: '保持字段测试',
//           description: '这是一个包含特殊字符的描述：@#$%^&*()',
//           status: 'TODO',
//           priority: 1,
//           created_at: new Date(),
//           completed_at: null
//         }
//       ]
//
//       const mockScheduleResult: SchedulePlanItem[] = [
//         {
//           task_id: '1234',
//           urgency_level: 'urgent',
//           task_type: 'engagement_prompt',
//           scheduled_time: 'now'
//         }
//       ]
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//
//       // 验证任务描述被正确传递
//       expect(mockScheduleTask).toHaveBeenCalledWith(
//         expect.objectContaining({
//           tasks_to_schedule: [
//             {
//               task_id: '1234',
//               task_description: '这是一个包含特殊字符的描述：@#$%^&*()'
//             }
//           ]
//         })
//       )
//
//       // 验证其他字段被保持
//       expect(result[0]).toEqual({
//         task_id: 'test_task_1234',
//         urgency_level: 'urgent',
//         task_type: 'engagement_prompt',
//         scheduled_time: 'now'
//       })
//     })
//   })
//
//   describe('性能和并发测试', () => {
//     it('应该能处理大量任务', async () => {
//       // 创建100个任务
//       const inputTasks: ITask[] = Array.from({ length: 100 }, (_, i) => ({
//         id: `bulk_task_${i.toString().padStart(4, '0')}`,
//         chat_id: testChatId,
//         round_id: 'round_bulk',
//         overall_goal: '批量测试',
//         description: `批量任务 ${i}`,
//         status: 'TODO',
//         priority: i,
//         created_at: new Date(),
//         completed_at: null
//       }))
//
//       const mockScheduleResult: SchedulePlanItem[] = inputTasks.map((_, i) => ({
//         task_id: i.toString().padStart(4, '0'),
//         urgency_level: 'normal',
//         task_type: 'value_delivery',
//         scheduled_time: `2025-01-16 ${(8 + Math.floor(i / 10)).toString().padStart(2, '0')}:${((i % 10) * 6).toString().padStart(2, '0')}:00`
//       }))
//
//       const mockScheduleTask = jest.fn().mockResolvedValue(mockScheduleResult)
//       MockedTaskScheduler.mockImplementation(() => ({
//         scheduleTask: mockScheduleTask
//       } as any))
//
//       const startTime = Date.now()
//       const result = await Planner.taskSchedule(testChatId, inputTasks)
//       const endTime = Date.now()
//
//       // 验证结果数量
//       expect(result).toHaveLength(100)
//
//       // 验证性能（应该在合理时间内完成）
//       expect(endTime - startTime).toBeLessThan(5000) // 5秒内完成
//
//       // 验证ID映射正确性
//       expect(result[0].task_id).toBe('bulk_task_0000')
//       expect(result[99].task_id).toBe('bulk_task_0099')
//     })
//   })
// })
