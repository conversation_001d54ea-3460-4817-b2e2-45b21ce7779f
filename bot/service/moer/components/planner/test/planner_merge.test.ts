// import { TaskManager } from '../task/task_manager'
// import { Planner } from '../plan/planner'
// import { PlanOperations } from '../types'
// import { TaskStatus } from '@prisma/client'
//
// describe('Planner Merge Operations Test', () => {
//   const testChatId = 'test_chat_planner_merge'
//   const testRoundId = 'test_round_planner_merge'
//
//   beforeEach(async () => {
//     // 清理测试数据
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     if (activeTasks.length > 0) {
//       await TaskManager.cancelTasks(activeTasks.map(task => task.id))
//     }
//   })
//
//   afterEach(async () => {
//     // 清理测试数据
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     if (activeTasks.length > 0) {
//       await TaskManager.cancelTasks(activeTasks.map(task => task.id))
//     }
//   })
//
//   test('toMerge operation should merge multiple tasks into one', async () => {
//     // 1. 创建测试任务
//     await TaskManager.createTasks(
//       testChatId,
//       [
//         '发送课程介绍',
//         '询问学习需求',
//         '推荐合适课程',
//         '跟进学习进度'
//       ],
//       '完成客户服务',
//       testRoundId
//     )
//
//     // 2. 获取活跃任务
//     let activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(4)
//
//     // 3. 执行合并操作 - 将任务1、2、3合并到任务1
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [],
//       toRemove: [],
//       toMerge: [
//         {
//           from: ['1', '2', '3'], // 合并任务1、2、3
//           into: '1',             // 合并到任务1
//           mergedContent: '发送课程介绍并询问学习需求，然后推荐合适的课程'
//         }
//       ]
//     }
//
//     // 4. 执行计划操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '完成客户服务',
//       testRoundId
//     )
//
//     // 5. 验证结果
//     activeTasks = await TaskManager.getActiveTasks(testChatId)
//
//     // 应该只剩下2个任务：合并后的任务1和原来的任务4
//     expect(activeTasks).toHaveLength(2)
//
//     // 验证合并后的任务内容
//     const mergedTask = activeTasks.find(task =>
//       task.description === '发送课程介绍并询问学习需求，然后推荐合适的课程'
//     )
//     expect(mergedTask).toBeDefined()
//
//     // 验证任务4仍然存在
//     const remainingTask = activeTasks.find(task =>
//       task.description === '跟进学习进度'
//     )
//     expect(remainingTask).toBeDefined()
//   })
//
//   test('toMerge operation should handle invalid task IDs gracefully', async () => {
//     // 1. 创建测试任务
//     await TaskManager.createTasks(
//       testChatId,
//       ['任务1', '任务2'],
//       '测试目标',
//       testRoundId
//     )
//
//     // 2. 执行包含无效ID的合并操作
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [],
//       toRemove: [],
//       toMerge: [
//         {
//           from: ['1', '99'], // 99是无效ID
//           into: '1',
//           mergedContent: '合并后的任务'
//         }
//       ]
//     }
//
//     // 3. 执行计划操作（不应该抛出错误）
//     await expect((Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '测试目标',
//       testRoundId
//     )).resolves.not.toThrow()
//
//     // 4. 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(2) // 任务数量应该保持不变
//   })
//
//   test('toMerge operation should work with multiple merge operations', async () => {
//     // 1. 创建测试任务
//     await TaskManager.createTasks(
//       testChatId,
//       [
//         '任务A1', '任务A2', '任务A3',  // 第一组
//         '任务B1', '任务B2',           // 第二组
//         '独立任务'                    // 独立任务
//       ],
//       '多组合并测试',
//       testRoundId
//     )
//
//     // 2. 执行多个合并操作
//     const planOperations: PlanOperations = {
//       toAdd: [],
//       toUpdate: [],
//       toRemove: [],
//       toMerge: [
//         {
//           from: ['1', '2', '3'],
//           into: '1',
//           mergedContent: '合并的A组任务'
//         },
//         `{
//           from: ['4', '5'],
//           into: '4',
//           mergedContent: '合并的B组任务'
//         }
//       ]
//     }
//
//     // 3. 执行计划操作
//     await (Planner as any).executePlanOperations(
//       testChatId,
//       planOperations,
//       '多组合并测试',
//       testRoundId
//     )
//
//     // 4. 验证结果
//     const activeTasks = await TaskManager.getActiveTasks(testChatId)
//     expect(activeTasks).toHaveLength(3) // 应该剩下3个任务
//
//     // 验证合并后的任务
//     const mergedTaskA = activeTasks.find(task => task.description === '合并的A组任务')
//     const mergedTaskB = activeTasks.find(task => task.description === '合并的B组任务')
//     const independentTask = activeTasks.find(task => task.description === '独立任务')
//
//     expect(mergedTaskA).toBeDefined()
//     expect(mergedTaskB).toBeDefined()
//     expect(independentTask).toBeDefined()
//   })
// })
