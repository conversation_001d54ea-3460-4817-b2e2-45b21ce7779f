import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { FreeTalk } from '../../agent/freetalk'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { MessageSender } from '../../message/message_send'
import { sleep } from '../../../../../lib/schedule/schedule'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { RegexHelper } from '../../../../../lib/regex/regex'

/**
 * 挖需
 */
export class IntentionQueryBeforeClass extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const nodeCount = ChatStateStore.getNodeCount(state.chat_id, IntentionQueryBeforeClass.name)
    if (nodeCount >= 2) {
      return await FreeTalk.invoke(state)
    }

    try {
      // 客户直接回复数字，直接回复固定话术
      if (/[1-7]/.test(state.userMessage) && RegexHelper.isNumericString(state.userMessage) && !((await ChatHistoryService.getFormatChatHistoryByChatId(state.chat_id)).includes('老师记下了哈。周一晚上八点第一课，咱们特别关注下哦。唐宁老师会教【如何让你如何提高“觉知力’从根源解决情绪、关系卡点，以及专注力提升】让您更加容易静下心，提高睡眠质量。'))) {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '老师记下了哈。周一晚上八点第一课，咱们特别关注下哦。唐宁老师会教【如何让你如何提高“觉知力’从根源解决情绪、关系卡点，以及专注力提升】让您更加容易静下心，提高睡眠质量。'
        })

        await sleep(15 * 1000)

        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '只要我们第一课能认真学好，到第二课【财富果园，解锁思维】，第三课老师讲到重点【红靴子冥想，提高专注力，和增加能量】就能更加进入状态，有着明显效果。每节课都是循序渐进的，每节课都的提前预留时间哦！'
        })
        return MoerNode.IntentionQueryBeforeClass
      } else {
        await FreeTalk.invoke(state) // 这里暂停两轮，等待客户回复 数字
      }
    } catch (e) { // 问题被打断，或没发出去，重新计数，避免对话被打断后，问题没有被问到
      const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount
      slotCount['queryBeforeClass'] -= 1
      throw e
    }

    return MoerNode.IntentionQueryBeforeClass
  }
}