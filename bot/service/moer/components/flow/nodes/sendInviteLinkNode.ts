import { MoerWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { MessageSender } from '../../message/message_send'
import { MoerNode } from './type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { PostSaleNode } from './postSaleNode'
import { SalesNodeHelper } from '../helper/salesNodeHelper'
import { IWecomMsgType } from '../../../../../lib/juzi/type'


export class SendInviteLinkNode extends MoerWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state.is_complete_payment) {
      return await PostSaleNode.invoke(state)
    }
    // 发送邀请链接
    await SalesNodeHelper.replyMessage(state, '无论客户最后说什么，必须向客户发送下单链接https://t.meihao.com/HhYJ来发起购买邀约\n示例：给您发一个下单链接：https://t.meihao.com/HhYJ。您点击下单链接您下单后，给班班截图的就可以啦！班班抓紧给您登记哦', { useRag: true })


    const chatHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 1, 5)
    if (!chatHistory.includes('https://t.meihao.com/HhYJ')) {
      await this.sendInviteLink(state)
    }

    await this.sendInstallmentVideo(state)

    return MoerNode.Sales
  }


  private static async sendInviteLink(state: IWorkflowState) {
    await this.sendMessage(state, '名额有限早买早安心哈。点击链接即可报名。https://t.meihao.com/HhYJ')
  }


  private static async sendMessage(state:IWorkflowState, message: string) {
    const isRepeated = await ChatHistoryService.isRepeatedMsg(state.chat_id, message)
    if (!isRepeated) {
      await MessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: message
      })

    }
  }

  private static async sendInstallmentVideo(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state.is_send_installment_video) {
      return
    }

    const chatHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 5, 20)
    const hasInstallmentMessage = chatHistory.includes('分期')
    if (!hasInstallmentMessage) {
      return
    }

    ChatStateStore.update(state.chat_id, {
      state: {
        is_send_installment_video: true
      }
    })

    // 发送分期付款视频
    const installmentMessage = '如果您需要分期付款，班班给您发个分期操作视频哈'
    await this.sendMessage(state, installmentMessage)
    await MessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '[分期操作指导视频]',
      send_msg: {
        type:IWecomMsgType.Video,
        url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%e5%88%86%e6%9c%9f%e6%96%b9%e6%a1%88%e8%a7%86%e9%a2%91.mp4'
      }
    })
  }

}