// import { SaleCategorizePrompt } from '../../../../prompt/moer/saleCategorize'
// import { LLM } from '../../../../../../lib/ai/llm/LLM'
// import { XMLHelper } from '../../../../../../lib/xml/xml'
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const f = async () => {
//       const prompt = await SaleCategorizePrompt.format(`Q:课怎么卖？
// A:1999 送坐垫
// Q:太贵了`)
//
//       const llmRes = await LLM.predict(prompt)
//       const xmlRes = XMLHelper.extractContent(llmRes, 'answer')
//
//       if (!xmlRes)
//         return UserPaymentPendingType.CourseValue
//       if (xmlRes === '1')
//         return UserPaymentPendingType.CourseValue
//       else if (xmlRes === '2')
//         return UserPaymentPendingType.CourseValue
//       else if (xmlRes === '3')
//         return UserPaymentPendingType.StuckPoint
//       else
//         return UserPaymentPendingType.CourseValue
//     }
//
//     console.log(await f())
//   }, 60000)
// })