import { LLMNode } from '../llm'
import { MockData } from '../../../../mock/mock'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { WealthOrchardAnalyze } from '../wealthOrchard'
import { getState } from '../../schedule/task/baseTask'
import { UUID } from '../../../../../../lib/uuid/uuid'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import MockDate from 'mockdate'
import { getChatId } from '../../../../../../config/chat_id'
import { WorkFlow } from '../../flow'
import { Config } from '../../../../../../config/config'
import { IntentionQueryNode } from '../intentionQuery'
import { loadConfigByAccountName } from '../../../../../../../test/tools/load_config'
import { MoerAPI } from '../../../../../../model/moer_api/moer'

describe('Test', function () {
  beforeAll(() => {

  })


  it('123123', async () => {
    // console.log(JSON.stringify(await MoerAPI.getUserByPhone('***********'), null, 4))
    const courses = await MoerAPI.getUserCourses('876612')
    const is_complete_payment = courses.some((course) => course.type === 2)
    console.log(is_complete_payment)
  }, 60000)

  it('splitSentence', async () => {
    console.log(LLMNode.splitSentence(`好的呀！21天系统课是咱们墨尔冥想的进阶课程，由詹唐宁老师亲自带练。整个课程为期3周，每周上5天，每节课大概30分钟，时间短但非常高效哦！  

内容设计上分为两个部分：  
- **知识模块**：唐宁老师会教你冥想的“心法”，也就是背后的原理和方法。  
- **实践模块**：通过每天的冥想练习，把知识转化为内在的体验，帮你建立稳固的冥想习惯。  

这21天里，你将学习传承千年的12套功法，包括：
1. **禅宗三法**：坐禅、立禅、卧禅，清空负能量，开启觉知。  
2. **五大呼吸**：地火水风空五种呼吸方式，让正能量更好滋养身体和内心。  
3. **四大觉禅**：音、光、息、念四种冥想技巧，提升专注力，显化更高频率的状态。

总之，这门课不只是一次课程，更是一个可以陪伴你很久、甚至一辈子的练习体系。而且内容永久回放哦～你随时可以复习！  

很多学员反映，通过这21天的深入学习，他们变得更平静、更有力量，同时也能更好面对生活中的挑战❤️ 如果你真的喜欢并愿意长期练，这真的是个很棒的选择！`))
  }, 60000)

  it('llmNode3', async () => {
    const mockState = await MockData.getState('输出 200 字的冥想感悟')

    await LLMNode.invoke({
      state: mockState
    })
  }, 60000)

  it('llmNode1', async () => {
    const mockState = await MockData.getState('hi, 你是？')

    await LLMNode.invoke({
      state: mockState
    })
  }, 60000)

  it('llmNode2', async () => {
    const mockState = await MockData.getState('hi, 你是？')

    await LLMNode.invoke({
      state: mockState
    })
  }, 60000)


  it('llmNode1', async () => {
    const mockState = await MockData.getState('21 天课有什么')

    await LLMNode.invoke({
      state: mockState,
      // referenceChatHistory: true,
      recallMemory: true,
      useRAG: true,
      dynamicPrompt: '回答客户的问题'
    })
  }, 60000)

  it('llmNode2', async () => {
    const mockState = await MockData.getState('hi, 你是？')

    await LLMNode.invoke({
      state: mockState
    })
  }, 60000)

  it('llmNode31', async () => {
    const mockState = await MockData.getState('hi, 你是？')

    await LLMNode.invoke({
      state: mockState
    })
  }, 60000)

  it('测试阶段prompt', async () => {
    Config.setting.localTest = true
    MockDate.set('2024-10-23 21:00:00')
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '红靴子是啥')
  }, 30000)

  it('1', async () => {
    console.log(JSON.stringify(await splitWithJinaAI('asdasd', 3), null, 4))
  }, 60000)

  it('去重测试', async () => {
    await (LLMNode as any).sentencePostProcess('哈哈，猜的话有点难哦😄', '****************_1688854546332791')
  }, 60000)

  it('intention', async () => {
    await IntentionQueryNode.invoke(await MockData.getState('hi'))
  }, 60000)

  it('wealth', async () => {
    const userMessage = '我看见青铜红色大门，红色芒果树'
    const chatId = UUID.short()
    await ChatHistoryService.addUserMessage(chatId, userMessage)

    await WealthOrchardAnalyze.invoke(await getState(chatId, UUID.short(), '我看见青铜大门，红色芒果树'))
  }, 60000 * 2)

  it('claude', async () => {
    await LLM.predict('hi', { model: 'claude-3-5-sonnet-********' })
  }, 60000)

  it('chat', async () => {
    const chatHistory = await ChatHistoryService.getLLMChatHistory('7881301047907394_1688854546332791', 4)
    console.log(ChatHistoryService.formatHistoryHelper(chatHistory))
  }, 60000)

  it('模型发送', async () => {
    Config.setting.localTest = false
    Config.setting.wechatConfig = await loadConfigByAccountName('syq')
    await LLM.sendMsg('你好呀\n[我爱吃冰淇淋_7ebb.png]\n[大礼包_daf9.png]\n卧槽', '****************_1688857003605938', '****************', '')
  }, 60000)

  it('', async () => {
    Config.setting.localTest = false
    Config.setting.wechatConfig = await loadConfigByAccountName('syq')
    await ChatHistoryService.clearChatHistory('****************_1688857003605938')

    await WorkFlow.step('****************_1688857003605938', '****************', '抖音支付怎么搞？')
  }, 1E8)

  it('fk', async () => {
    const line = '你好呀\n[我爱吃冰淇淋_7ebb.png]\n[大礼包_daf9.png]\n卧槽'
    const parts = line.split(/\[((?:.*?)_(?:\w{4})\.(?:\w+))\]/)

    for (const part of parts) {
      const fileRegex =  /.*?_\w{4}\.\w+/
      if (fileRegex.test(part)) {
        console.log('m', part)
      } else {
        console.log(part)
      }
    }
  }, 60000)
})


async function splitWithJinaAI(text, numChars) {
  const data = {
    content: text,
    return_chunks: true,
    max_chunk_length: numChars
  }

  try {
    const response = await fetch('https://segment.jina.ai/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      console.error('HTTP error:', response.status, response.statusText)
      return null
    }

    const responseData = await response.json()

    const chunks = responseData.chunks || []  // Assuming the API returns the chunks in a property called 'chunks'

    console.log(`Number of chunks: ${  chunks.length}`)
    // console.table(chunks);  // Uncomment if you want to see the chunks in a table format

    return chunks
  } catch (error) {
    console.error('Fetch error:', error)
    return null
  }
}