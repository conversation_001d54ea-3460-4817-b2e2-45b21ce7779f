import { faker } from '@faker-js/faker'
import { IWorkflowState } from '../../flow'
import { ChatInterruptHandler } from '../../../message/interrupt_handler'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { DataService } from '../../../../getter/getData'
import { FreeTalk } from '../../../agent/freetalk'
import { Config } from '../../../../../../config/config'
import { ChatStateStore } from '../../../../storage/chat_state_store'


describe('freeTalkTest', () => {

  it('freeTalkTest', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    const chatId = '7881300516060552_1688858254705213'
    const userMessage = '有更多的学员案例吗'
    // await ChatHistoryService.addUserMessage(chatId, userMessage)

    const state: IWorkflowState = {
      chat_id: chatId,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chatId),
    }

    DataService.getCurrentTime = async () => {
      return {
        is_course_week: true,
        day: 5,
        time: '22:00:00',
      }
    }

    DataService.isPaidSystemCourse = async () => {
      return false
    }


    ChatStateStore.getNodeCount = (chat_id: string, name: string) => {
      return 16
    }

    ChatHistoryService.getChatHistoryByChatId = async (chat_id: string) => {
      return []
    }

    await FreeTalk.invoke(state)
  }, 9E8)

})