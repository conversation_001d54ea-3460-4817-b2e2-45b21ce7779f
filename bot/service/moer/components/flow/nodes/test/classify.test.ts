import { Router } from '../router'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(JSON.stringify(await Router.classify('不知道为啥我这个群设置免打扰了还是在响，关键是私发的我没关，结果一直发我真的完全没进入状态就一些烦躁，就更没进去[大哭]', '', ''), null, 4))
  }, 60000)

  it('财富果园', async () => {
    console.log(JSON.stringify(await Router.classify('大门是竹子做的，青绿色，比较新，围栏是藤条做的，比较高大。主树是比较高大的长满绿叶的大树。周围有苹果树，梨树，杏树，水蜜桃树。', '', ''), null, 4))
  }, 60000)

  it('投诉_1', async () => {
    expect(await Router.classify('我要投诉', '', '')).toEqual(7)
  }, 60000)

  it('投诉_2', async () => {
    expect(await Router.classify('冥想后想投诉楼下水果摊，他们服务很不好，非常生气', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_3', async () => {
    expect(await Router.classify('楼下水果摊卖的水果是坏的，想投诉他们', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_4', async () => {
    expect(await Router.classify('购课无法退款，那我要投诉', '', '')).toEqual(7)
  }, 60000)

  it('投诉_5', async () => {
    expect(await Router.classify('我报名参加了你们的冥想课程，但是内容太基础了，完全不值这个价钱。都是网上随便就能找到的免费内容。', '', '')).toEqual(7)
  }, 60000)

  it('投诉_6', async () => {
    expect(await Router.classify('我要向消费者协会投诉你们的虚假宣传！课程介绍说有\'终身导师支持\'，实际上三个月后就没人回应我的问题了。', '', '')).toEqual(7)
  }, 60000)

  it('投诉_7', async () => {
    expect(await Router.classify('我强烈投诉你们的课程质量！所谓的\'专业冥想导师\'明显没有受过任何正规培训，讲解的内容甚至有安全隐患。', '', '')).toEqual(7)
  }, 60000)

  it('投诉_8', async () => {
    expect(await Router.classify('我要投诉课程的虚假承诺！广告宣称\'七天见效\'，我已经坚持一个月了还是感觉焦虑。这不是在利用心理脆弱的人赚钱吗？', '', '')).toEqual(7)
  }, 60000)

  it('投诉_9', async () => {
    expect(await Router.classify('最近跟着你们的课程冥想，效果似乎不太明显，可能需要调整一下？', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_10', async () => {
    expect(await Router.classify('这个课程好像不太适合我，但我也不确定是课程的问题还是我自己没做对', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_11', async () => {
    expect(await Router.classify('你是大忽悠，把我忽悠过来，我得捞点好处，对吧!', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_12', async () => {
    expect(await Router.classify('感觉被你忽悠了', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_13', async () => {
    expect(await Router.classify('既然答应邮寄，也知道买课的人来自五湖四海,怎么还让自己付费，这个印家可就打折扣了[呲牙]', '', '')).not.toEqual(7)
  }, 60000)

  it('投诉_14', async () => {
    expect(await Router.classify('主要是没有发放，心里不爽', '', '')).not.toEqual(7)
  }, 60000)

  it('退款_1', async () => {
    expect(await Router.classify('我要退款', '', '')).toEqual(7)
  }, 60000)

  it('退款_2', async () => {
    expect(await Router.classify('申请退款', '', '')).toEqual(7)
  }, 60000)

  it('退款_3', async () => {
    expect(await Router.classify('现在办理退款办不了吗？我看无法退单', '', '')).toEqual(7)
  }, 60000)

  it('退款_4', async () => {
    expect(await Router.classify('线上商品链接，没有退款入口', '', '')).not.toEqual(7)
  }, 60000)

  it('退款_5', async () => {
    expect(await Router.classify('麻烦帮处理一下退课事宜谢谢', '', '')).toEqual(7)
  }, 60000)

  it('退款_6', async () => {
    expect(await Router.classify('麦子老师，记得帮我安排退课事宜', '', '')).toEqual(7)
  }, 60000)

  it('退款_7', async () => {
    expect(await Router.classify('如果进入不了状态，能退课吗？中途能退课吗？', '', '')).not.toEqual(7)
  }, 60000)

  it('退款_8', async () => {
    expect(await Router.classify('中途能退课吗？', '', '')).not.toEqual(7)
  }, 60000)

  it('退款_9', async () => {
    expect(await Router.classify('如果进入不了状态，能退课吗？', '', '')).not.toEqual(7)
  }, 60000)

  it('换助教_1', async () => {
    expect(await Router.classify('我要换助教', '', '')).toEqual(7)
  }, 60000)

  it('换助教_2', async () => {
    expect(await Router.classify('助教老师细心负责_身心得到成长', '', '')).not.toEqual(7)
  }, 60000)

  it('换助教_3', async () => {
    expect(await Router.classify('助教老师细心负责_身心得到成长', '', '')).not.toEqual(7)
  }, 60000)

  it('换助教_4', async () => {
    expect(await Router.classify('老师，我想问一下能不能换助教啊？我和现在的助教相处得不是很顺利，总觉得他讲的我听不太懂。想试试看换一个是不是会好一点。', '', '')).toEqual(7)
  }, 60000)

  it('换助教_5', async () => {
    expect(await Router.classify('我想换助教，现在这个和我不太合，能帮我处理一下吗', '', '')).toEqual(7)
  }, 60000)

  it('换助教_6', async () => {
    expect(await Router.classify('你好，我想问一下怎么换助教？现在这个跟我不太合得来。', '', '')).toEqual(7)
  }, 60000)

  it('换助教_7', async () => {
    expect(await Router.classify('请问有没有可能更换一下我的助教？我们之间好像有点沟通障碍', '', '')).toEqual(7)
  }, 60000)
})