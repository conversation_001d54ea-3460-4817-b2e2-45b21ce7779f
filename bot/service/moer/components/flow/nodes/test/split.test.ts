function splitSentence(text: string): string[] {
  // 1. 先按照序号分割，例如 "1. "、"2. " 等
  const regex = /\d+\.\s[^]*?(?=\d+\.\s|$)/g

  // 使用 match 方法提取所有匹配的段落
  const matchedSections = text.match(regex)

  // 清理分割结果：去除首尾空白并过滤空字符串
  const sections = matchedSections ? matchedSections.map((section) => section.trim()) : []

  // console.log(cleanedSections)

  let splitSentences: string[]
  if (sections.length > 1) {
    splitSentences = sections
  } else {
    // 2. 按标点分割成数组，包括中文和英文标点
    splitSentences = text.split(/(?<=[?!。！？\n~～])(?![”"'])/)
  }

  // 去除每个句子的前后空白并过滤空字符串
  const rawSentences = splitSentences.map((s) => s.trim()).filter(Boolean)

  // 3. 如果句子数 <=5，原样返回
  if (rawSentences.length <= 5) {
    return rawSentences
  }

  // 4. 分句数 >5 时，两两合并
  const combined: string[] = []
  for (let i = 0; i < rawSentences.length; i += 2) {
    let merged = rawSentences[i]
    if (rawSentences[i + 1]) {
      merged += ` ${rawSentences[i + 1]}`
    }
    combined.push(merged)
  }

  return combined
}

describe('splitSentence 函数测试', () => {
  test('按编号分割，句子数超过5句', () => {
    const text = `1. 这是第一句。
2. 这是第二句。
3. 这是第三句。
4. 这是第四句。`

    const expected = [
      '1. 这是第一句。',
      '2. 这是第二句。',
      '3. 这是第三句。',
      '4. 这是第四句。',
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('按标点分割，句子数超过5句并合并', () => {
    const text = '这是第一句。 这是第二句！这是第三句？这是第四句。这是第五句！这是第六句？这是第七句。'

    const expected = [
      '这是第一句。 这是第二句！',
      '这是第三句？ 这是第四句。',
      '这是第五句！ 这是第六句？',
      '这是第七句。'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('句子数等于5句，按标点分割，不合并', () => {
    const text = '这是第一句。 这是第二句！这是第三句？这是第四句。这是第五句！'

    const expected = [
      '这是第一句。',
      '这是第二句！',
      '这是第三句？',
      '这是第四句。',
      '这是第五句！'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('句子数少于5句，按标点分割，不合并', () => {
    const text = '这是第一句。 这是第二句！这是第三句？'

    const expected = [
      '这是第一句。',
      '这是第二句！',
      '这是第三句？'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('空字符串输入，返回空数组', () => {
    const text = ''
    const expected: string[] = []

    expect(splitSentence(text)).toEqual(expected)
  })

  test('仅有空白字符的字符串，返回空数组', () => {
    const text = '   \n\t  '
    const expected: string[] = []

    expect(splitSentence(text)).toEqual(expected)
  })

  test('混合编号和标点的情况，优先按编号分割', () => {
    const text = '1. 这是第一句。 这是第二句！2. 这是第三句？'

    const expected = [
      '1. 这是第一句。 这是第二句！',
      '2. 这是第三句？'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('句子末尾有引号，正确分割', () => {
    const text = '他说：“这是第一句。” 她回答：“这是第二句！”'

    const expected = [
      '他说：“这是第一句。”',
      '她回答：“这是第二句！”'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  test('句子中包含小数，正确分割', () => {
    const text = '这是一个1.23的句子。'

    const expected = [
      '这是一个1.23的句子。'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })


  test('分句~', () => {
    const text = '不用谢啦，大爱送给你也送给世界 愿你每天都能感受到平静和美好～随时聊哦，抱抱你❤️'
    console.log(splitSentence(text))
    const expected = [
      '不用谢啦，大爱送给你也送给世界 愿你每天都能感受到平静和美好～',
      '随时聊哦，抱抱你❤️'
    ]

    expect(splitSentence(text)).toEqual(expected)
  })

  it('测试', async () => {
    function removeSuishi(sentence: string) {
      // 1. 将 sentence 按 ~ 分割
      let sentenceParts = sentence.split(/(~|～)/)

      // 2. 过滤掉包含 '随时' 的分句
      sentenceParts = sentenceParts.filter((part) => !part.includes('随时'))

      // 3. 将剩余的分句合并为一个新的 sentence
      sentence = sentenceParts.join('').trim()

      return sentence
    }

    console.log(removeSuishi('不用谢啦，大爱送给你也送给世界 愿你每天都能感受到平静和美好。随时聊哦，抱抱你❤️'))
    console.log(removeSuishi('嗯嗯，这个截图里的麦子老师就是我啦😊，主要负责5天入门营的支持和答疑~至于助教老师呢，也是帮忙处理课程相关事务的，我们是一个团队，会一起协助大家更好地学习冥想~有啥问题，随时找我就行哈！'))
  }, 60000)
})