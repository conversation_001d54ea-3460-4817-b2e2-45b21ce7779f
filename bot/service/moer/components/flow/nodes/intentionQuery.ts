import { <PERSON>r<PERSON>orkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { MoerNode } from './type'
import { ExtractUserSlots } from '../helper/slotsExtract'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { ObjectUtil } from '../../../../../lib/object'
import { ExtractUserSlotsPrompt } from '../../../prompt/moer/extractUserSlots'
import { LLMNode } from './llm'
import { SendEnergyTest } from '../schedule/task/sendEnergyTest'
import { TaskName } from '../schedule/type'
import { IsNeedEmpathyPrompt } from '../../../prompt/moer/isNeedEmpathy'
import { LLMXMLHelper } from '../helper/xmlHelper'
import { sleep } from '../../../../../lib/schedule/schedule'
import { MessageSender } from '../../message/message_send'
import { DataService } from '../../../getter/getData'
import { FreeTalk } from '../../agent/freetalk'
import { IWecomMsgType } from '../../../../../lib/juzi/type'

export enum MoerUserSlotType {
    is_attend_live_stream = 'live_class_confirmation',
    meditation_goal = 'meditation_goal',
}

/**
 * 挖需
 */
export class IntentionQueryNode extends MoerWorkFlowNode {
    @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const isCompleteIntentionQuery = ChatStateStore.getFlags(state.chat_id).is_complete_user_query

    if (isCompleteIntentionQuery) {
      return FreeTalk.invoke(state)
    }

    // 挖需
    const slotAsk = await this.getSlotAsk(state)
    if (!slotAsk) { // 挖需结束
      ChatStateStore.update(state.chat_id, {
        state: {
          is_complete_user_query: true
        }
      })

      const nextNode = await FreeTalk.invoke(state)

      await this.sendEnergyTest(state)

      return nextNode
    }


    if (slotAsk.slotType === MoerUserSlotType.is_attend_live_stream) {
      const currentTime = await DataService.getCurrentTime(state.chat_id)
      await sleep(9000)

      if (!currentTime.is_course_week) {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '[询问客户下一周晚8点开课时间是否方便]',
          send_msg: {
            type: IWecomMsgType.Voice,
            duration: 9,
            voiceUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/%e6%8c%96%e9%9c%802%e9%97%ae%e4%b8%8b%e5%91%a8%e4%b8%8a%e8%af%be%e6%98%af%e5%90%a6%e5%8f%af%e4%bb%a5.silk'
          }
        })
      } else {
        await MessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '今晚8点咱们就开始直播课了，咱们时间OK吗？可以准时到课不。',
        })
      }
    } else {
      await FreeTalk.invoke(state)
    }

    // } else if (slotAsk.slotType === MoerUserSlotType.meditation_goal) {
    //   await FreeTalk.invoke(state)
    //   // if (!((await ChatHistoryService.getFormatChatHistoryByChatId(state.chat_id)).includes('咱们之前了解过冥想吗'))) {
    //   //   // 如果上一条是 AI 消息，不调用 FreeTalk
    //   //   const lastMessage = await ChatHistoryService.getLastMessage(state.chat_id)
    //   //   if (lastMessage.role === 'user') {
    //   //     await ChatHistoryService.moveToEnd(state.chat_id, state.userMessage)
    //   //
    //   //     await FreeTalk.invoke(state)
    //   //   }
    //   //
    //   //   await MessageSender.sendById({
    //   //     user_id: state.user_id,
    //   //     chat_id: state.chat_id,
    //   //     ai_msg: '咱们之前了解过冥想吗，有具体想要解决的问题不？😊（班班贴合咱们情况提供更针对性学习体验）',
    //   //   })
    //   // }
    // } else {
    //   await LLMNode.invoke({
    //     state,
    //     dynamicPrompt: this.appendEnergyTestInfo(slotAsk.slotQuestion),
    //     referenceChatHistory: true,
    //     useRAG: true
    //   })
    // }

    // 槽位计数
    const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount
    slotCount[slotAsk.slotType] = slotCount[slotAsk.slotType] ? slotCount[slotAsk.slotType] + 1 : 1

    return MoerNode.FreeTalk
  }

    /**
     * 根据客户槽位，提取要问的问题
     * @param state
     * @private
     */
    private static async getSlotAsk(state: IWorkflowState) {
      // 获取 userSlots
      // TODO: 提取槽位
      const userSlots = await this.extractUserSlots(state)
      const slotPromptMap = {
        [MoerUserSlotType.is_attend_live_stream]: '需要确认下客户上课期间晚上8点是不是OK的。例如："咱们下周一晚上8点开始老师的直播课哈。咱们时间都可以不"',
        [MoerUserSlotType.meditation_goal]: '询问客户需求了解其对冥想的了解程度，通过这次学习想收获什么。例如："对啦，咱们之前了解过冥想吗，有具体想要解决的问题不？😊（班班贴合咱们情况提供更针对性学习体验）"'
      }

      const slotCount = ChatStateStore.get(state.chat_id).slotAskedCount

      // 选取当前应该问的槽位
      const toAskedSlots = [MoerUserSlotType.is_attend_live_stream]
      for (let i = toAskedSlots.length - 1; i >= 0; i--) { // 倒序遍历，保证安全删除
        const toAskedSlot = toAskedSlots[i]
        if (slotCount[toAskedSlot] >= 1) {
          toAskedSlots.splice(i, 1)
          continue
        }

        if (userSlots && userSlots[toAskedSlot] !== undefined) { // 如果上文中已经有客户信息，不再提问
          toAskedSlots.splice(i, 1)
          continue
        }
      }

      if (toAskedSlots.length === 0) {
        return null
      }

      const toAskSlot = toAskedSlots[0]

      return {
        slotQuestion: slotPromptMap[toAskSlot],
        slotType: toAskSlot,
      }
    }

    private static async extractUserSlots(state: IWorkflowState) {
      const chat_id = state.chat_id
      const chatHistory = ChatHistoryService.formatHistoryHelper(await ChatHistoryService.getRecentConversations(state.chat_id, 3))
      const extractPrompt = await ExtractUserSlotsPrompt.format(chatHistory)

      let currentUserSlots = await ExtractUserSlots.extract(extractPrompt, ExtractUserSlotsPrompt.schema, state)
      const prevUserSlots = ChatStateStore.get(chat_id).userSlots

      if (currentUserSlots) {
        currentUserSlots = await this.mergeUserSlots(prevUserSlots, currentUserSlots)
        ChatStateStore.update(chat_id, { userSlots: currentUserSlots })
      }

      return currentUserSlots
    }

    private static async mergeUserSlots(prevUserSlots: Record<string, any>, currentUserSlots: Record<string, any>) {
    // 这里可以加一些 合并规则，不一定所有情况都去更新槽位
      return ObjectUtil.merge(prevUserSlots, currentUserSlots)
    }

    private static async sendEnergyTest(state: IWorkflowState) {
      await sleep(15 * 1000)

      if (ChatStateStore.getFlags(state.chat_id).is_delayed_send_energy_test) {
        return
      }

      await new SendEnergyTest().process({
        chatId: state.chat_id,
        userId: state.user_id,
        name: TaskName.SendEnergyTest
      })
    }

}