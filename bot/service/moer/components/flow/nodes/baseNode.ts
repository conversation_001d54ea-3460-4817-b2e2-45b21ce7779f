import { IWorkflowState } from '../flow'
import { ChatStateStore } from '../../../storage/chat_state_store'
import logger from '../../../../../model/logger/logger'
import { MoerNode } from './type'
import { EventTracker, IEventType } from '../../../../../model/logger/data_driven'
import { DataService } from '../../../getter/getData'


export abstract class MoerWorkFlowNode {
  public static async invoke(state: IWorkflowState): Promise<MoerNode> {
    return MoerNode.FreeTalk
  }
}


export function trackInvoke (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any []) {
    const state: IWorkflowState = args [0] // 假设 state 对象存在于第一个参数
    const chatId = state.chat_id // 假设 state 中包含 chatId
    const roundId = state.round_id // 假设 state 中包含 roundId
    const moerId = await DataService.getMoerIdByChatId(chatId)

    const nodeInvokeCount: Record<string, number> = ChatStateStore.get(state.chat_id).nodeInvokeCount
    const node_name = target.name

    if (!nodeInvokeCount [node_name]) {
      nodeInvokeCount [node_name] = 0
    }

    const stateInfo = {
      chatId,
      roundId,
      userMessage: state.userMessage
    }

    // 输出进入节点的信息
    logger.debug({ chat_id: chatId, round_id: roundId },
      `进入 ${node_name}`,
      `调用次数: ${nodeInvokeCount[node_name]}`,
      stateInfo
    )

    EventTracker.track(chatId, IEventType.NodeInvoke, {
      more_id: moerId,
      round_id: roundId,
      node_name: node_name,
      node_count: nodeInvokeCount[node_name]
    })

    const start = Date.now()
    let res
    try {
      res = await originalMethod.apply (this, args)
    } catch (e) {
      logger.error({ chat_id: chatId, round_id: roundId }, `调用 ${node_name} 出错`, e, `round_id: ${roundId}`) // 输出)
      throw e
    }

    const end = Date.now()
    logger.debug({ chat_id: chatId, round_id: roundId },
      `结束 ${target.name}`,
      `执行时长: ${((end - start) / 1000).toFixed(1)}s`,
      stateInfo
    ) // 输出结束节点的信息

    nodeInvokeCount [node_name]++ // 消息有可能被打断，计数放到后面

    return res
  }

  return descriptor
}


/**
 * 获取 LangSmith 的 Trace
 * @param state
 */
export function getLLMMeta(state: IWorkflowState) {
  return {
    chat_id: state.chat_id,
    round_id: state.round_id
  }
}