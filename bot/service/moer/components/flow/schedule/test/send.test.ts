import { getScript } from '../../../script/script'
import { GroupSend } from '../task/groupSend'
import { UUID } from '../../../../../../lib/uuid/uuid'
import { TaskName } from '../type'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { DataService } from '../../../../getter/getData'
import { SendWelcomeMessage } from '../task/sendWelcomeMessage'
import { CheckPreCourseCompletionTask } from '../task/checkPreCourseCompletion'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'
import { RedisCacheDB } from '../../../../../../model/redis/redis_cache'
import { Config } from '../../../../../../config/config'
import { ClassGroupSend } from '../task/classGroupSend'
import { MoerEventHandler } from '../../../../../../../bot_starter/handler/moer_event'
import { IMoerEvent } from '../../../../../../../bot_starter/client/client_server'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { XiaoHongShuActivationReminderTask } from '../task/xiaohongshuActivationTask'
import { ChatDB } from '../../../../database/chat'

const groupSendScript = getScript().course_day
describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '1688858254705213',
      botUserId: 'ShengYueQing',
      name: '暴叔',

      notifyGroupId: 'R:10829337560927503',
      classGroupId: 'xx',
      courseNo: 1
    }
    Config.setting.localTest = false
  })

  it('get phone', async () => {
    Config.setting.wechatConfig =   {
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '暴叔',

      notifyGroupId: 'R:10748678386699836',
      classGroupId: 'xx',
      courseNo: 1
    }


    await (new ClassGroupSend()).process({
      'chatId': 'mock-chat-id',
      'userId': 'R:10748678386699836',
      'name': '建群提醒1',
      'scheduleTime': {
        'is_course_week': false,
        'day': 7,
        'time': '09:00:00'
      },
    })
  }, 60000)

  it('测试任务添加', async () => {
    // await addClassGroupTask()
    if (await new RedisCacheDB('notSendGroupMsg').get()) {
      console.log('stop')
    }
  }, 60000)

  it('s', async () => {
    // await ChatStatStoreManager.initState('7881299780296724_1688857003605938')
    await new GroupSend().process({
      name: TaskName.FinalMarketingPitch,
      chatId: '7881300516060552_1688858254705213',
      userId: '7881300516060552',
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '19:00:00'
      }
    })
  }, 60000)

  it('should pass', async () => {
    await new GroupSend().sendMsg('7881300846030208', UUID.short(), [{
      description: '测试',
      msg: {
        type: IWecomMsgType.Voice,
        voiceUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/tongtongVoice/%E5%91%A8%E4%B8%80%2019_00%20%E5%82%AC%E8%BF%9B%E7%BE%A4.silk',
        duration: 9
      }
    }])
  }, 60000)

  it('send', async () => {
    await new GroupSend().process(({
      chatId: UUID.short(),  // UUID.short()
      userId: '7881302298050442',  // Horus：7881302298050442
      name: TaskName.ClassStartNotification,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '17:50:00'
      }
    }))
  }, 60000)

  it('phone', async () => {
    const moerUser = await MoerAPI.getUserByPhone('19119281868')
    console.log(JSON.stringify(moerUser, null, 4))
    const courseNo = DataService.parseCourseNo(moerUser)

    console.log(courseNo)
  }, 60000)

  it('testPhoneNumber', async () => {
    // console.log(await MoerAPI.getUserPhone({ externalUserId: 'wmXvL2CQAAqiaIuS_YZm1z9JKRgp7WfQ' }))
    console.log(await (new SendWelcomeMessage() as any).findPhoneNumber('7881300329954896'))
  }, 60000)

  it('123123', async () => {
    const currentTime = await DataService.getCurrentTime('7881301047907394_1688854546332791')
    console.log(JSON.stringify(currentTime, null, 4))
  }, 60000)

  it('小讲堂', async () => {
    console.log(await DataService.getCourseLinkByCourseNo(58, 0))
  }, 60000)

  it('xiaohongshuOrder', async () => {
    ChatHistoryService.addBotMessage = jest.fn()
    DataService.getCourseLinkByCourseNo = jest.fn().mockReturnValue('https://wap2.esnewcollege.com/xmly?page_id=317&wx_id=7881300516060552_1688854546332791')
    const event:IMoerEvent = {
      event: 'xiaohongshu_order',
      logid: '123',
      userId: 1,
      mobile: '1',
      wxId: '7881300516060552_1688854546332791', //windowLu
      transferNo: '4',
      course_type: 1,
      stage: 1,
    }
    await MoerEventHandler.handle(event)
  }, 60000)

  it('xiaohongshuReminderTask', async () => {
    ChatDB.getById = jest.fn().mockReturnValue({
      moer_id: null
    })
    const data = {
      chatId: '7881300516060552_1688854546332791',
      userId: '7881300516060552',
      retryTime: 3
    }
    await new XiaoHongShuActivationReminderTask().process(data)
  }, 60000)


  it('红鞋子sop_1', async () => {
    // const scripts = getScript().course_day
    // await new GroupSend().sendMsg('7881302146051227', '7881302146051227_1688858254705213', [await scripts.unpaid_remind_1_1.content(), scripts.unpaid_remind_1_2, scripts.unpaid_remind_1_3.content], 'hello', true)
    await new GroupSend().process({ chatId:'7881302146051227_1688858254705213', userId:'7881302146051227', name:TaskName.UnPaidRemind1 })
  }, 100000)
  it('红鞋子sop_2', async () => {
    // const scripts = getScript().course_day
    // await new GroupSend().sendMsg('7881302146051227', '7881302146051227_1688858254705213', [await scripts.unpaid_remind_2_1.content()], 'hello2', true)
    await new GroupSend().process({ chatId:'7881302146051227_1688858254705213', userId:'7881302146051227', name:TaskName.UnPaidRemind2 })
  }, 100000)


  it('测试添加封面图片sop', async () => {
    await new GroupSend().sendMsg('7881302146051227', '7881302146051227_1688858254705213', [
      {
        description: '链接卡片：🔋能量频率层级测评',
        msg: {
          type: 12,
          sourceUrl: 'https://jsj.top/f/W8ktas',
          title: '🔋能量频率层级测评',
          summary: '🌈了解自己能量，识别那些限制您发展的低频率行为模式，借此提高您的能量频率，释放您真正的潜力。',
          imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
        }
      }])
  })

  it('测试添加封面岛图片sop', async () => {
    await new GroupSend().sendMsg('7881302146051227', '7881302146051227_1688858254705213', [
      {
        description: '链接卡片：玖在岛｜国内首座『水晶岛』：身心疗愈·冥想乐源',
        msg: {
          type: 12,
          sourceUrl: 'http://mp.weixin.qq.com/s?__biz=MzkwMjY2MTA1OQ==&mid=2247486360&idx=1&sn=bea7fadfceb1085c39b268cfa07b3248&chksm=c0a35d34f7d4d4223987750de075e86d076a1d5febaf7263abf0d8a66ca9e6029c143b58f219&mpshare=1&scene=1&srcid=0523U8Lu9Dvp8pXiGKVXlJrC&sharer_shareinfo=0cbe8a4d16d1e545f87f67fdc8dad51d&sharer_shareinfo_first=0cbe8a4d16d1e545f87f67fdc8dad51d#rd',
          title: '玖在岛｜国内首座『水晶岛』：身心疗愈·冥想乐源',
          summary: '岛亲之 理想国\n心回家 爱久在',
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/island.jpg'
        }
      },
    ])
  })

  it('测试添加封面练习营图片sop', async () => {
    await new GroupSend().sendMsg('7881302146051227', '7881302146051227_1688858254705213', [{
      description:'test',
      msg:{
        type: IWecomMsgType.Link,
        summary: '每天45分钟，助你成事悦己，高频正觉',
        imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E5%86%A5%E6%83%B3%E7%B3%BB%E7%BB%9F%E7%8F%AD.png',
        title: '21天|冥想系统班',
        sourceUrl: 'https://h5.esnewcollege.com/pages/course/detail?sku=20240107008805&pid=665301&checkLogin=1'
      } }
    ])
  })

})