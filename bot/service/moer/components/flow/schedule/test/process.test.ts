import { GroupSend } from '../task/groupSend'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    await new GroupSend().process({
      'chatId': '7881301021968452_1688855184697783',
      'userId': '7881301021968452',
      'name': '发布奖励和回访',
      'scheduleTime': {
        'is_course_week': true,
        'day': 3,
        'time': '11:53:00'
      },
      'sendTime': new Date('2025-03-26T03:53:00.000Z')
    }
    )
  }, 60000)
})