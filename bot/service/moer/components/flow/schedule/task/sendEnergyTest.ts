import { BaseTask, trackProcess } from './baseTask'
import { ITask } from '../../../schedule/type'
import { getScript } from '../../../script/script'
import { DataService } from '../../../../getter/getData'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import { ChatHistoryService } from '../../../chat_history/chat_history'

export class SendEnergyTest extends BaseTask {

    @trackProcess
  public async process(task: ITask): Promise<void> {
    const { userId, chatId } = task
    const day0Scripts = getScript().pre_course_day

    if (ChatStateStore.getFlags(chatId).is_send_energy_test) {
      return
    }

    // 如果 前6条 聊天记录中有提到过链接，不再进行发送
    const chatHistory = await ChatHistoryService.getRecentConversations(chatId, 6, 'assistant')
    const formatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)
    if (formatHistory.includes('https://jsj.top/f/W8ktas')) {
      ChatStateStore.update(chatId, {
        state: {
          is_send_energy_test: true
        }
      })
      return
    }

    ChatStateStore.update(chatId, {
      state: {
        is_send_energy_test: true
      }
    })

    // 做过能量测评就不再发送了
    const energyTestScore = await DataService.getEnergyTestScore(task.chatId)
    if (!energyTestScore && energyTestScore !== 0) {
      await this.sendMsg(userId, chatId, [day0Scripts.energy_test_3.content, day0Scripts.energy_test_3_image], day0Scripts.energy_test_3.description)
    }
  }
}