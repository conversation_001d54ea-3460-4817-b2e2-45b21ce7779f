import { ChatDB } from '../../../../database/chat'
import { HumanTransfer, HumanTransferType } from '../../../human_transfer/human_transfer'
import { Config } from '../../../../../../config/config'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../../../../model/redis/redis'
import { ScheduleTask } from '../../../schedule/schedule'
import { AccountTaskName, TaskName } from '../type'
import { DataService } from '../../../../getter/getData'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import { getScript } from '../../../script/script'
import logger from '../../../../../../model/logger/logger'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { NewCourseUser } from '../../helper/newCourseUser'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { MessageSender } from '../../../message/message_send'
import { DateHelper } from '../../../../../../lib/date/date'

interface IPhoneBindCheckData {
    chatId: string
    userId: string
}

export class PhoneBindTask {

  async phoneBindCheck(data: IPhoneBindCheckData) {
    const chat = await ChatDB.getById(data.chatId)
    if (!chat) {
      return
    }

    if (chat && !chat.moer_id) { // 没有正确绑定上 手机号，没有在墨尔系统中查询到信息，通知可能绑定有问题
      await HumanTransfer.transfer(data.chatId, data.userId, HumanTransferType.NotBindPhone, 'onlyNotify')
    }
  }

  public static async addBindPhoneTask(data: IPhoneBindCheckData) {
    const phoneNumber = await DataService.findPhoneNumber(data.userId)

    ChatStateStore.update(data.chatId, {
      state: {
        is_bounding_phone_number: false,
      }
    })

    const day0Script = getScript().pre_course_day

    if (phoneNumber) {
      logger.log(data.chatId, '已经绑定电话号码')
      try {
        const moerUser = await MoerAPI.getUserByPhone(phoneNumber)
        const courseNo = DataService.parseCourseNo(moerUser)

        await NewCourseUser.create(data.userId, data.chatId, courseNo, phoneNumber, moerUser.id.toString())
      } catch (e) {
        await HumanTransfer.transfer(data.chatId, data.userId, HumanTransferType.NotBindPhone, 'onlyNotify')
        logger.error(`phoneNumber: ${phoneNumber} 绑定 ${data.chatId} 失败`)
      }
    } else {
      await sleep(5 * 1000)
      await MessageSender.sendById({
        user_id: data.userId,
        chat_id: data.chatId,
        ai_msg: day0Script.query_phone_number.content
      }, {
        shortDes: day0Script.query_phone_number.description
      })

      // 半小时后检查手机号是否绑定上
      await ScheduleTask.addTask(Config.setting.wechatConfig!.id, TaskName.PhoneBindCheck, DateHelper.add(new Date(), 30, 'minute'), {
        chatId: data.chatId,
        userId: data.userId
      })

      await this.addPhoneBindFailTask()
    }
  }


  private static async addPhoneBindFailTask() {
    const queueName = `bind_phone_fail_${Config.setting.wechatConfig?.id}`
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    // 获取下周一下午6点的时间
    const date = new Date()
    const daysUntilNextMonday = (1 - date.getDay() + 7) % 7
    date.setDate(date.getDate() + daysUntilNextMonday)
    date.setHours(18, 0, 0, 0)

    if (await queue.count() === 0) {
      await ScheduleTask.addTask(queueName, AccountTaskName.WeeklyBindPhoneFailNotify, date, {})
    }
  }
}