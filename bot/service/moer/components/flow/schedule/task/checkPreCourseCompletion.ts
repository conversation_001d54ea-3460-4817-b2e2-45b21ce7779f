import { ITask } from '../../../schedule/type'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import { getScript } from '../../../script/script'
import { BaseTask, getState, trackProcess } from './baseTask'
import { TaskName } from '../type'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { DataService } from '../../../../getter/getData'
import { MoerNode } from '../../nodes/type'
import { IntentionQueryNode } from '../../nodes/intentionQuery'
import { sendEnergyTest } from '../../helper/sendEnergyTest'
import { FreeTalk } from '../../../agent/freetalk'
import { IScheduleTime, isScheduleTimeAfter } from '../../../schedule/creat_schedule_task'

/**
 * 检查客户是否完成小讲堂
 */
export class CheckPreCourseCompletionTask extends BaseTask {
  public static async getTask(chat_id: string, user_id: string) {
    // 构建几个 任务，并返回任务
    const tasks: ITask[] = []

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 2,
        time: '07:14:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 3,
        time: '07:30:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 4,
        time: '07:15:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '06:50:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '17:34:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '08:00:00'
      }
    })
    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '19:36:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '12:00:00'
      }
    })
    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '06:37:00'
      }
    })
    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '14:11:00'
      }
    })

    tasks.push({
      name: TaskName.CheckPreCourseCompletion,
      chatId: chat_id,
      userId: user_id,
      scheduleTime: {
        is_course_week: false,
        day: 4,
        time: '16:50:00'
      }
    })

    return tasks
  }


  /**
   * 发送完课礼
   * @param userId
   * @param chatId
   */
  public async sendPreCourseCompleteGift(userId: string, chatId: string) {
    // 完课礼只发一次
    if (ChatStateStore.getFlags(chatId).is_send_pre_course_completion_gift || (await ChatHistoryService.getFormatChatHistoryByChatId(chatId)).includes('[冥想练习指南图片]')) {
      return // 已经发送过小讲堂礼物
    }

    const currentTime = await DataService.getCurrentTime(chatId)

    // 周一之后不发送完课礼了
    if (isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })) {
      return
    }

    // 如果完成了，发放完课礼
    const day0Scripts = getScript().pre_course_day

    ChatStateStore.update(chatId, {
      nextStage: MoerNode.IntentionQuery,
      state: {
        is_send_pre_course_completion_gift: true,
        is_complete_pre_course: true
      }
    })

    await this.sendMsg(userId, chatId, [day0Scripts.complete_pre_course1_v2.content, day0Scripts.complete_pre_course2], day0Scripts.complete_pre_course1_v2.description)

    // 开始主动挖需
    await IntentionQueryNode.invoke(await getState(chatId, userId))

    await sendEnergyTest(chatId, userId) // 发放能量测评
  }

  @trackProcess
  public async process(task: ITask) {
    const { chatId, userId } = task
    // 查询数据库，检查客户是否完成小讲堂
    const isCompleted = await DataService.isCompletedCourse(task.chatId, { day: 0 })

    const day0Scripts = getScript().pre_course_day

    // 完课发送完课礼，处理定点完课逻辑
    if (isCompleted) {
      await this.sendPreCourseCompleteGift(userId, chatId)

      const scheduleTime = task.scheduleTime

      if (scheduleTime && scheduleTime.day === 4 && scheduleTime.time === '16:50:00') {
        await this.sendMsg(userId, chatId, [day0Scripts.pre_course_push_day4_1650.content, day0Scripts.pre_course_push_day4_1650_link_card, day0Scripts.pre_course_push_day4_1650_link_card_2], day0Scripts.pre_course_push_day4_1650.description)
      }
    } else { // 未完成小讲堂，催完课
      const scheduleTime = task.scheduleTime
      const currentTime = await DataService.getCurrentTime(chatId)
      await this.handleTPlusNTask(task, currentTime)
      if (!scheduleTime) {
        return
      }

      if (!scheduleTime.is_course_week) {
        switch (scheduleTime.day) { // 接量周
          case 6:
            if (scheduleTime.time === '08:00:00' && ChatStateStore.getNodeCount(task.chatId, FreeTalk.name) <= 3) {
              await this.sendMsg(userId, chatId, [day0Scripts.pre_course_push_day6_interaction_less_3.content], day0Scripts.pre_course_push_day6_interaction_less_3.description)
            } else if (scheduleTime.time === '08:00:00' && ChatStateStore.getNodeCount(task.chatId, FreeTalk.name) > 3) {
              await this.sendMsg(userId, chatId, await day0Scripts.pre_course_push_day6_interaction_over_3.content(chatId), day0Scripts.pre_course_push_day6_interaction_over_3.description)
            }
            break

          case 7:
            if (scheduleTime.time === '06:37:00') {
              await this.sendMsg(userId, chatId, await getScript().pre_course_day.pre_course_push_8.content(chatId), getScript().pre_course_day.pre_course_push_8.description)
            } else if (scheduleTime.time === '14:11:00') {
              await this.sendMsg(userId, chatId, await getScript().pre_course_day.pre_course_push_9_link.content(chatId), getScript().pre_course_day.pre_course_push_9.description)
            } else if (scheduleTime.time === '20:30:00') {
              const openingCeremonySummary = [getScript().pre_course_day.opening_ceremony_class_meeting_summary_part1.content, await getScript().pre_course_day.opening_ceremony_class_meeting_summary_part2.content(chatId)]
              await this.sendMsg(userId, chatId, openingCeremonySummary, getScript().pre_course_day.opening_ceremony_class_meeting_summary_part1.description)
            }
            break
        }
      } else if (scheduleTime.day === 1) { // 上课周 周一
        await this.sendMsg(userId, chatId, await getScript().pre_course_day.pre_course_push_course_week_1.content(chatId), getScript().pre_course_day.pre_course_push_course_week_1.description)
      }
    }
  }

  private async handleTPlusNTask(task: ITask, currentTime: IScheduleTime) {
    const userId = task.userId
    const chatId = task.chatId

    if (task.name === TaskName.TPlusNTask && !currentTime.is_course_week && currentTime.day <= 5) {
      if (task.tag === 't_plus_1_07_14') {
        await this.sendMsg(userId, chatId, await getScript().pre_course_day.t_plus_1_7_14.content(chatId), getScript().pre_course_day.t_plus_1_7_14.description)
      } else if (task.tag === 't_plus_2_07_30') {
        await this.sendMsg(userId, chatId, [getScript().pre_course_day.t_plus_2_07_30_voice, await getScript().pre_course_day.t_plus_2_07_30_text.content(chatId)], getScript().pre_course_day.t_plus_2_07_30_voice.description)
      } else if (task.tag === 't_plus_3_07_03') {
        await this.sendMsg(userId, chatId, await getScript().pre_course_day.t_plus_3_7_03.content(chatId), getScript().pre_course_day.t_plus_3_7_03.description)
      } else if (task.tag === 't_plus_4_16_32') {
        await this.sendMsg(userId, chatId, [getScript().pre_course_day.t_plus_4_16_32_voice, await getScript().pre_course_day.t_plus_4_16_32_text.content(chatId)], getScript().pre_course_day.t_plus_4_16_32_voice.description)
      } else if (task.tag === 't_plus_2_12_00') {
        //兼容逻辑
        await this.sendMsg(userId, chatId, [getScript().pre_course_day.t_plus_2_07_30_voice, await getScript().pre_course_day.t_plus_2_07_30_text.content(chatId)], getScript().pre_course_day.t_plus_2_07_30_voice.description)
      }

      const twoDaysAgo = new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000)
      const hasConversationInTwoDays = await ChatHistoryService.hasConversationRecently(chatId, twoDaysAgo)
      if (task.tag === 't_plus_3_18_30_no_interaction' && !hasConversationInTwoDays) {
        await this.sendMsg(userId, chatId, [getScript().pre_course_day.t_plus_3_with_no_interaction], getScript().pre_course_day.t_plus_3_with_no_interaction.description)
      }

    }
  }
}