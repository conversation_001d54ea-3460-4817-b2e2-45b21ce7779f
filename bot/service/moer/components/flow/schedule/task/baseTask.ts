import { ISendMedia, ITask } from '../../../schedule/type'
import { MessageSender } from '../../../message/message_send'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { Config } from '../../../../../../config/config'
import { ChatStateStore, ChatStatStoreManager } from '../../../../storage/chat_state_store'
import logger from '../../../../../../model/logger/logger'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { IWorkflowState } from '../../flow'
import { UUID } from '../../../../../../lib/uuid/uuid'
import { ChatInterruptHandler } from '../../../message/interrupt_handler'
import { DataService } from '../../../../getter/getData'
import { IWecomMsgType, IWecomTextMsg } from '../../../../../../lib/juzi/type'
import { RedisCacheDB } from '../../../../../../model/redis/redis_cache'

export abstract class BaseTask {
  // 定义抽象方法 process，具体实现留给子类
  public abstract process(task: ITask): Promise<void>;

  // 实现 sendMsg 方法
  public async sendMsg(userId: string, chatId: string,  messages: (ISendMedia | string)[] | string, description?: string, isRepeatedCheck?: boolean): Promise<void> {
    // 这里注意下，所有 消息重复的话都不进行发送了
    if (typeof messages === 'string') {
      const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, messages)
      if (isRepeated && !isRepeatedCheck) {
        return
      }

      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: messages,
      }, {
        shortDes: `[${   description  }]`
      })
      return
    }

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i]
      if (typeof message === 'string') {
        const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, message)
        if (isRepeated && !isRepeatedCheck) {
          return
        }

        await MessageSender.sendById({
          user_id: userId,
          chat_id: chatId,
          ai_msg: message,
        }, {
          shortDes: `[${  description  }]`
        })
      } else {
        // 文件，图片类型根据描述去重
        let checkRepeatedText = message.description

        // 对文本不进行根据描述去重
        if (message.msg.type === IWecomMsgType.Text) {
          checkRepeatedText = (message.msg as IWecomTextMsg).text
        }

        const isRepeated = await ChatHistoryService.hasRepeatedMsg(chatId, checkRepeatedText)
        if (isRepeated && !isRepeatedCheck) {
          return
        }

        await MessageSender.sendById({
          user_id: userId,
          chat_id: chatId,
          ai_msg: `[${message.description}]`,
          send_msg: message.msg,
        }, {
          shortDes: `[${ message.description  }]`
        })
      }

      if (i < messages.length - 1) {
        await commonSleep()
      }
    }
  }


  // 实现 sendMsg 方法
  public async sendGroupMsg(groupId: string,  messages: (ISendMedia | string)[] | string, description?: string): Promise<void> {
    const redisCache = new RedisCacheDB('notSendGroupMsg')
    const notSendGroupMsg = await redisCache.get()

    if (notSendGroupMsg) {
      logger.trace('停止发送群消息')
      return
    }

    if (typeof messages === 'string') {
      await MessageSender.sendById({
        room_id: groupId,
        chat_id: groupId,
        ai_msg: messages,
      }, {
        shortDes: `[${   description  }]`,
      })
      return
    }

    let waitTime = 0
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i]
      if (typeof message === 'string') {
        waitTime = message.length * 150

        await MessageSender.sendById({
          room_id: groupId,
          chat_id: groupId,
          ai_msg: message,
        }, {
          shortDes: `[${  description  }]`
        })
      } else {
        await MessageSender.sendById({
          room_id: groupId,
          chat_id: groupId,
          ai_msg: `[${message.description}]`,
          send_msg: message.msg,
        }, {
          shortDes: `[${  description  }]`
        })

        // 对于图片消息，固定等待时间 20s
        waitTime = 20 * 1000
        if (message.msg.type === IWecomMsgType.Text) {
          waitTime = message.msg.text.length * 150
        }
      }

      if (i < messages.length - 1) {
        // 等待指定的时间
        await sleep(waitTime)
      }
    }
  }
}

/**
 * 一个通用的等待时间
 */
export async function commonSleep () {
  await sleep(Config.setting.waitingTime.activePush.interval)
}


export function trackProcess (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any []) {
    // @ts-ignore 忽略类型错误，这里 this 是 BaseTask 的实例
    const taskInfo: ITask = args [0] // 假设 state 对象存在于第一个参数
    await ChatStatStoreManager.initState(taskInfo.chatId) // 注意服务挂掉后，执行任务，要把数据库的任务状态读取到内存中

    const nodeInvokeCount: Record<string, number> = ChatStateStore.get(taskInfo.chatId).nodeInvokeCount
    const node_name = target.name

    if (!nodeInvokeCount [node_name]) {
      nodeInvokeCount [node_name] = 0
    }
    logger.debug({ chat_id: taskInfo.chatId }, `进入 ${taskInfo.name}`, `调用次数: ${nodeInvokeCount [node_name]}`, JSON.stringify(taskInfo, null, 4)) // 输出进入节点的信息

    const start = Date.now()

    const res =  await originalMethod.apply (this, args)

    const end = Date.now()
    logger.debug({ chat_id: taskInfo.chatId }, `结束 ${taskInfo.name}`, `执行时长: ${((end - start) / 1000).toFixed(1)}s`) // 输出结束节点的信息

    await DataService.saveChat(taskInfo.chatId, taskInfo.userId)

    nodeInvokeCount [node_name]++ // 消息有可能被打断，计数放到后面
    return res
  }

  return descriptor
}

export async function getState(chatId: string, userId: string, userMessage = ''): Promise<IWorkflowState> {
  return {
    chat_id: chatId,
    user_id: userId,
    userMessage: userMessage,
    interruptHandler: await ChatInterruptHandler.create(chatId),
    round_id: UUID.v4()
  }
}