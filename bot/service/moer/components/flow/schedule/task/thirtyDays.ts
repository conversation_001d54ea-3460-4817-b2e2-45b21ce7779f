import { BaseTask, trackProcess } from './baseTask'
import { ISendMedia, ITask } from '../../../schedule/type'
import { IScheduleTime } from '../../../schedule/creat_schedule_task'
import { getScript, ISendWrapper, ScriptFunction } from '../../../script/script'
import { TaskName } from '../type'
import { DataService } from '../../../../getter/getData'
// 30天sop任务
export class ThirtyDays extends BaseTask {
  public static async getTask(chatId: string, userId: string) {
    const tasks: ITask[] = []
    const baseInfo = {
      chatId: chatId,
      userId: userId,
    }

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 1,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 1,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 2,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 2,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 3,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 3,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 4,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 4,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 5,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 5,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 6,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 7,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 7,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 1,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 2,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 3,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 3,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 4,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 5,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 6,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 6,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirtyDaysReminder,
      scheduleTime: {
        post_course_week: 2,
        day: 7,
        time: '20:00:00'
      }
    })

    return tasks
  }

  @trackProcess
  public async process(task: ITask) {
    if (!task.scheduleTime) {
      return
    }

    const taskTime = task.scheduleTime as IScheduleTime
    const scripts = getScript().thirty_day

    switch (taskTime.post_course_week) {
      case 1:
        switch (taskTime.day) {
          case 1:
            if (await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_course_complete_week1_day1_07_00 as ISendWrapper<string>).content, scripts.mini_program_week1_day1_07_00 as ISendMedia], (scripts.reminder_text_course_complete_week1_day1_07_00 as ISendWrapper<string>).description)
            } else if (!await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_course_not_complete_week1_day1_07_00 as ISendWrapper<string>).content, scripts.mini_program_week1_day1_07_00 as ISendMedia], (scripts.reminder_text_course_not_complete_week1_day1_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day1_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day1_20_00 as ISendWrapper<string>).description)
            }
            break

          case 2:
            if (await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_complete_week1_day2_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_complete_week1_day2_07_00 as ISendWrapper<string>).description)
            } else if (!await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_not_complete_week1_day2_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_not_complete_week1_day2_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day2_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day2_20_00 as ISendWrapper<string>).description)
            }
            break

          case 3:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day3_07_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day3_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day3_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day3_20_00 as ISendWrapper<string>).description)
            }
            break

          case 4:
            if (await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_complete_week1_day4_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_complete_week1_day4_07_00 as ISendWrapper<string>).description)
            } else if (!await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_not_complete_week1_day4_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_not_complete_week1_day4_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day4_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day4_20_00 as ISendWrapper<string>).description)
            }
            break

          case 5:
            if (await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_complete_week1_day5_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_complete_week1_day5_07_00 as ISendWrapper<string>).description)
            } else if (!await DataService.isCompletedAllCourse(task.chatId) && taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_course_not_complete_week1_day5_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_course_not_complete_week1_day5_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day5_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day5_20_00 as ISendWrapper<string>).description)
            }
            break

          case 6:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [await (scripts.reminder_text_week1_day6_07_00 as ISendWrapper<ScriptFunction>).content(task.chatId)], (scripts.reminder_text_week1_day6_07_00 as ISendWrapper<string>).description)
            }
            break

          case 7:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day7_07_00 as ISendWrapper<string>).content, scripts.reminder_image_week1_day7_07_00 as ISendMedia], (scripts.reminder_text_week1_day7_07_00 as ISendWrapper<string>).description)
            }else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week1_day7_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week1_day7_20_00 as ISendWrapper<string>).description)
            }
            break
        }
        break
      case 2:
        switch (taskTime.day) {
          case 1:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day1_07_00 as ISendWrapper<string>).content, scripts.mini_program_week2_day1_07_00 as ISendMedia], (scripts.reminder_text_week2_day1_07_00 as ISendWrapper<string>).description)
            }
            break

          case 2:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day2_07_00 as ISendWrapper<string>).content, scripts.mini_program_week2_day2_07_00 as ISendMedia], (scripts.reminder_text_week2_day2_07_00 as ISendWrapper<string>).description)
            }
            break

          case 3:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day3_07_00 as ISendWrapper<string>).content], (scripts.reminder_text_week2_day3_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day3_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week2_day3_20_00 as ISendWrapper<string>).description)
            }
            break

          case 4:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day4_07_00 as ISendWrapper<string>).content, scripts.mini_program_week2_day4_07_00 as ISendMedia], (scripts.reminder_text_week2_day4_07_00 as ISendWrapper<string>).description)
            }
            break

          case 5:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day5_07_00 as ISendWrapper<string>).content, scripts.video_week2_day5_07_00 as ISendMedia], (scripts.reminder_text_week2_day5_07_00 as ISendWrapper<string>).description)
            }
            break

          case 6:
            if (taskTime.time === '07:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day6_07_00 as ISendWrapper<string>).content, scripts.image_week2_day6_07_00 as ISendMedia], (scripts.reminder_text_week2_day6_07_00 as ISendWrapper<string>).description)
            } else if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day6_20_00 as ISendWrapper<string>).content], (scripts.reminder_text_week2_day6_20_00 as ISendWrapper<string>).description)
            }
            break

          case 7:
            if (taskTime.time === '20:00:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.reminder_text_week2_day7_20_00 as ISendWrapper<string>).content, scripts.mini_program_week2_day7_20_00 as ISendMedia], (scripts.reminder_text_week2_day7_20_00 as ISendWrapper<string>).description)
            }
            break
        }
    }



  }
}