import { MessageSender } from '../../../message/message_send'
import { ChatDB } from '../../../../database/chat'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { sleep } from 'openai/core'
import { enrollLink } from '../../helper/zeroYuanChannelLink'

interface IXiaohongshuTaskData {
    chatId: string
    userId: string
    retryTime: number
}


export class XiaoHongShuActivationReminderTask {

  async process(data: IXiaohongshuTaskData) {
    const chat = await ChatDB.getById(data.chatId)
    if (!chat) {
      return
    }

    if (chat && chat.moer_id) { // 绑定成功
      return
    }

    switch (data.retryTime) {
      case 1:
        await MessageSender.sendById({
          user_id: data.userId,
          chat_id: data.chatId,
          ai_msg: '同学还没领课吗？😄我们课程的主讲人是唐宁老师，国内冥想界第一人哦~大家熟知的导演徐峥、演员陶虹，都是老师的冥想铁粉，快领取课程开启美好的冥想旅程吧！',
        })
        break
      case 2:
      { await MessageSender.sendById({
        user_id: data.userId,
        chat_id: data.chatId,
        ai_msg: '同学是不是把课程忘了😭，班班已经在统计人数准备开课啦。接下来的课程涵盖情绪减压、沉浸式秒睡、财富唤醒……点击下面卡片领课喔~',
      })

      await sleep(2000)
      const courseLink = enrollLink(data.chatId)
      await MessageSender.sendById({
        user_id: data.userId,
        chat_id: data.chatId,
        ai_msg: '[点击卡片链接，领取0元课程]',
        send_msg: {
          type: IWecomMsgType.Link,
          sourceUrl: `${courseLink}`,
          title: '欢迎来到本次【墨尔冥想】入门营！',
          summary: '点击领取0元课程',
          imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
        }
      })
      break }
      case 3:
        await MessageSender.sendById({
          user_id: data.userId,
          chat_id: data.chatId,
          ai_msg: '同学我们下周一就开课啦~您再不领取的话，课程权限就失效了，不少小白学员在第一课就有收获，您还想练习冥想吗？',
        })

        await MessageSender.sendById({
          user_id: data.userId,
          chat_id: data.chatId,
          ai_msg: '[小红书催领课案例图片]',
          send_msg:{
            type: IWecomMsgType.Image,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/xiaohongshu/%e5%b0%8f%e7%ba%a2%e4%b9%a6%e5%82%ac%e9%a2%86%e8%af%be%e6%a1%88%e4%be%8b.jpg'
          }
        })
        break
      default:
        break

    }

  }
}