import { CheckPreCourseCompletionTask } from './checkPreCourseCompletion'
import { ITask } from '../../../schedule/type'
import { GroupSend } from './groupSend'
import { ThirtyDays } from './thirtyDays'

/**
 * 获取客户启动后的 发送任务
 */
export async function getTaskList(userId: string, chatId: string) {
  const initialTasks: ITask [] = []

  initialTasks.push(...await CheckPreCourseCompletionTask.getTask(chatId, userId))
  initialTasks.push(...await GroupSend.getTask(chatId, userId))
  initialTasks.push(...await ThirtyDays.getTask(chatId, userId))

  return initialTasks
}