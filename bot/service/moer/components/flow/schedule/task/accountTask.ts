import { Job, Worker } from 'bullmq'
import { Config } from '../../../../../../config/config'
import { RedisDB } from '../../../../../../model/redis/redis'
import { PrismaMongoClient } from '../../../../../../model/mongodb/prisma'
import { DataService } from '../../../../getter/getData'
import { GroupNotification } from '../../../../notification/group'
import logger from '../../../../../../model/logger/logger'

export class AccountTask {

  public static async startWorkerForAccount() {
    new Worker(`bind_phone_fail_${Config.setting.wechatConfig?.id}`, async (job: Job) => {
      await this.bindPhoneFailNotify()
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (err) => {
      logger.error('bind_phone_fail Worker 发生未捕获错误', err)
    })
  }

  public static async bindPhoneFailNotify() {
    const chats = await this.getLastWeekChats()
    const unBindWxNames: string[] = []
    for (const chat of chats) {
      const chatId = chat.id
      const moerId = await DataService.getMoerIdByChatId(chatId)
      const wechatId = chat.id.split('_')[1]
      if (!moerId && wechatId === Config.setting.wechatConfig?.id && this.isValidWxName(chat.contact.wx_name))
      {
        unBindWxNames.push(chat.contact.wx_name)
      }
    }

    if (unBindWxNames.length > 0) {
      const notifyMessage = `上周以下客户绑定手机号失败\n${unBindWxNames.join('\n')}`

      await GroupNotification.notify(notifyMessage)
    }
  }

  public static async getLastWeekChats() {
    // 获取当前日期
    const currentDate = new Date()

    // 计算上周一的日期
    const lastMonday = new Date()
    lastMonday.setDate(currentDate.getDate() - currentDate.getDay() - 6)
    lastMonday.setHours(0, 0, 0, 0)

    // 计算上周日的日期
    const lastSunday = new Date()
    lastSunday.setDate(currentDate.getDate() - currentDate.getDay())
    lastSunday.setHours(23, 59, 59, 999)

    return PrismaMongoClient.getInstance().chat.findMany({
      where: {
        created_at: {
          gte: lastMonday,
          lte: lastSunday
        }
      }
    })
  }

  public static isValidWxName(wxName: string) {
    // 过滤掉以788开头且纯数字的微信名
    return !/^788\d+$/.test(wxName) && isNaN(Number(wxName))
  }
}