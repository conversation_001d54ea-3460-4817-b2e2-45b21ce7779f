import { MessageSender } from '../../../message/message_send'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { DataService } from '../../../../getter/getData'
import { getScript } from '../../../script/script'
import logger from '../../../../../../model/logger/logger'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import { ITask } from '../../../schedule/type'
import { BaseTask, trackProcess } from './baseTask'
import { Config, MoerAccountType } from '../../../../../../config/config'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { PrismaMongoClient } from '../../../../../../model/mongodb/prisma'
import { ScheduleTask } from '../../../schedule/schedule'
import { TaskName } from '../type'
import { DateHelper } from '../../../../../../lib/date/date'
import { enrollLink } from '../../helper/zeroYuanChannelLink'
import { getGeneralSopKey } from '../task_starter'

export class SendWelcomeMessage extends BaseTask {
  @trackProcess
  public async process(task: ITask): Promise<void> {
    const { userId, chatId } = task // 增加 accountType 参数
    const accountType = Config.getAccountType()

    // 路由到具体 欢迎语逻辑
    switch (accountType) {
      case MoerAccountType.Normal: // 正常渠道
        await this.handleWeChatWelcome(userId, chatId)
        break
      case MoerAccountType.XiaoHongShu: // 小红书欢迎语逻辑
      case MoerAccountType.BaoDing:
        await this.handleOYuanChannelWelcome(userId, chatId)
        break
      default:
        logger.error(`未知的账号类型: ${accountType}`)
        break
    }
  }

  // 微信欢迎语逻辑
  private async handleWeChatWelcome(userId: string, chatId: string): Promise<void> {
    // 检查绑定手机号状态
    ChatStateStore.update(chatId, {
      state: {
        is_bounding_phone_number: true,
      },
    })

    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: '[欢迎]',
      send_msg: {
        type: IWecomMsgType.Emoticon,
        imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/emoji/welcome.gif',
      },
    })

    await sleep(5000)

    //todo 腾讯1元量测试欢迎语，如果没有效果提醒尽快下掉
    const limitWechatId = ['****************', '****************']
    const currentWechatId = Config.setting.wechatConfig?.id

    if (currentWechatId && limitWechatId.includes(currentWechatId)) {
      await this.oneYuanWelcome(userId, chatId)
    } else {
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: await this.getSelfIntroduction(userId),
      })

      await sleep(5000)

      const currentCourseNo = DataService.getNextWeekCourseNo()

      const liveLink = await DataService.getCourseLinkByCourseNo(currentCourseNo, 0)
      await MessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: `✨【课前必做】很多同学不知道冥想具体是什么，怎么学？老师在10分钟小讲堂都给大家讲啦，一定提前看！：
${liveLink}
（注意用下单手机号登录）      

完成自动激活后续课程，班班会发入学礼哈🎁`,
      })
    }

    // 延迟去读取手机号绑定信息
    await ScheduleTask.addTask(Config.setting.wechatConfig!.id, TaskName.AddPhoneBindTask, DateHelper.add(new Date(), 2, 'minute'), {
      chatId: chatId,
      userId: userId
    })
  }

  // 0元渠道欢迎语逻辑
  private async handleOYuanChannelWelcome(userId: string, chatId: string): Promise<void> {
    // 先创建一下 chat, 防止回调中找不到信息
    await DataService.saveChat(chatId, userId)

    // 获取微信名和 老师名
    const wechatName = await DataService.getWechatName(userId)
    const counsellorName = DataService.getCounsellorName()

    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: `亲爱的${wechatName}同学，您好呀。
✨欢迎参加墨尔冥想5天冥想入门营！我是你的贴心班班 【${counsellorName}】
之前也是从0开始和唐宁老师学冥想，之后会辅助您的冥想学习。`,
    })

    await sleep(3000)

    const courseLink = enrollLink(chatId)

    // 构造一个链接卡片
    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: '[点击卡片链接，领取0元课程]',
      send_msg: {
        type: IWecomMsgType.Link,
        sourceUrl: `${courseLink}`,
        title: '欢迎来到本次【墨尔冥想】入门营！',
        summary: '点击领取0元课程',
        imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/b818825b-5837-4437-9f64-106fd84274be/909f5b57-7b9f-451c-900a-f72dd7aee377.jpg'
      }
    })

    await sleep(3000)

    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: '点击上面👆🏻的卡片，开通我们的0元课程，名额有限，尽快领取哈~请放心，您填写手机号和验证码仅用于课程权限开通，我们会严格保护您的个人信息。',
    })

    // 先绑定一个期数到客户
    await PrismaMongoClient.getInstance().chat.update({
      where: { id: chatId },
      data: {
        course_no: DataService.getNextWeekCourseNo()
      }
    })

    await ScheduleTask.addTask(getGeneralSopKey(), TaskName.XiaoHongShuActivationReminder, DateHelper.add(new Date(), 30, 'minute'),   { chatId, userId, retryTime: 1 })
    await ScheduleTask.addTask(getGeneralSopKey(), TaskName.XiaoHongShuActivationReminder, DateHelper.addDaysWithTime(new Date(), 1, 8),   { chatId, userId,  retryTime: 2 })
    await ScheduleTask.addTask(getGeneralSopKey(), TaskName.XiaoHongShuActivationReminder, DateHelper.addDaysWithTime(new Date(), 2, 8),   { chatId, userId, retryTime: 3 })
  }

  private async oneYuanWelcome(userId: string, chatId: string) {
    const counsellorName = DataService.getCounsellorName()
    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: `🧘‍♀️亲爱的，欢迎加入【五天冥想禅修营】～我是你的班班，你可以叫我${counsellorName}。
你刚刚只花了1元，就加入了这套原价399元的科学冥想课程，太划算啦！
我们每天练习一节课，用最简单的方法，调频身心节奏、稳定情绪、睡得更好🌙`,
    })

    await sleep(3000)

    const currentCourseNo = DataService.getNextWeekCourseNo()
    const liveLink = await DataService.getCourseLinkByCourseNo(currentCourseNo, 0)
    const startTime = await DataService.getCourseStartTimeByCourseNo(currentCourseNo)
    const endTime = DateHelper.add(startTime, 2, 'day')
    const startTimeStr = `${startTime.getMonth() + 1}月${startTime.getDate()}日`
    const endTimeStr = `${endTime.getMonth() + 1}月${endTime.getDate()}日`

    await MessageSender.sendById({
      user_id: userId,
      chat_id: chatId,
      ai_msg: `🧘【上课时间】每晚 20:00（${startTimeStr}—${endTimeStr}）
👉【每位同学只有一次学习机会，一定要珍惜哦】
上课前请务必先听完预习课（不然进不来直播间）
👉【点击进入先导课链接：${liveLink}】
听完后回复我【已看完】，我送你入学仪式感满满的🎁入学礼！`,
    })
  }

  /**
   * 获取自我介绍内容
   * @param {string} userId - 客户ID
   * @returns {Promise<string>} 自我介绍内容
   */
  private async getSelfIntroduction(userId: string): Promise<string> {
    // 获取课程脚本和当前课程号
    const day0Script = getScript().pre_course_day
    const currentCourseNo = DataService.getNextWeekCourseNo()

    // 获取课程开始时间并格式化为字符串
    const startTime = await DataService.getCourseStartTimeByCourseNo(currentCourseNo)
    const startTimeStr = `${startTime.getMonth() + 1}月${startTime.getDate()}日`

    // 获取微信名和 老师名
    const wechatName = await DataService.getWechatName(userId)
    const counsellorName = DataService.getCounsellorName()

    // 获取自我介绍内容
    return await day0Script.self_introduction.content(
      wechatName,
      counsellorName,
      startTimeStr
    )
  }

}
export async function addPreCourseCheckTask(chatId: string, userId: string) {
  const plusOneDay = DateHelper.add(new Date(), 1, 'day')
  plusOneDay.setHours(7, 14, 0, 0)

  const plusTwoDays = DateHelper.add(new Date(), 2, 'day')
  plusTwoDays.setHours(7, 30, 0, 0)

  const plusThreeDays = DateHelper.add(new Date(), 3, 'day')
  plusThreeDays.setHours(7, 3, 0, 0)

  const plusThreeDaysWithoutInteraction = DateHelper.add(new Date(), 3, 'day')
  plusThreeDaysWithoutInteraction.setHours(18, 30, 0, 0)

  const plusFourDays = DateHelper.add(new Date(), 4, 'day')
  plusFourDays.setHours(16, 32, 0, 0)


  const addTask = (date: Date, tag: string) => {
    ScheduleTask.addTask(getGeneralSopKey(), TaskName.CheckPreCourseCompletion, date, {
      chatId,
      userId,
      name: TaskName.TPlusNTask,
      tag,
    })
  }

  addTask(plusOneDay, 't_plus_1_07_14')
  addTask(plusTwoDays, 't_plus_2_07_30')
  addTask(plusThreeDays, 't_plus_3_07_03')
  addTask(plusThreeDaysWithoutInteraction, 't_plus_3_18_30_no_interaction')
  addTask(plusFourDays, 't_plus_4_16_32')
}