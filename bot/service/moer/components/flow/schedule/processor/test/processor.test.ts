// import { IDBBaseMessage } from '../../../../chat_history/chat_history'
// import { MoerProcessor } from '../processor'
//
// describe('测试isChatRecently', () => {
//   test('测试_1', () => {
//     const chatHistory:IDBBaseMessage = {
//       id: '1',
//       created_at: new Date(new Date().getTime() - 2599),
//       chat_id: '1',
//       role:'assistant',
//       content:'none'
//     }
//     expect(MoerProcessor.isChatRecently(chatHistory, 2000)).toEqual(false)
//   })
//
//   test('测试_2', () => {
//     const chatHistory:IDBBaseMessage = {
//       id: '1',
//       created_at: new Date(new Date().getTime() - 599),
//       chat_id: '1',
//       role:'assistant',
//       content:'none',
//       short_description:'[赠送]'
//     }
//     expect(MoerProcessor.isChatRecently(chatHistory, 2000)).toEqual(false)
//   })
//
//   test('测试_3', () => {
//     const chatHistory:IDBBaseMessage = {
//       id: '1',
//       created_at: new Date(new Date().getTime() - 599),
//       chat_id: '1',
//       role:'assistant',
//       content:'none'
//     }
//     expect(MoerProcessor.isChatRecently(chatHistory, 2000)).toEqual(true)
//   })
//
//   test('测试_4', () => {
//     const chatHistory:IDBBaseMessage = {
//       id: '1',
//       created_at: new Date(new Date().getTime() - 2599),
//       chat_id: '1',
//       role:'assistant',
//       content:'none',
//       short_description:'[赠送]'
//     }
//     expect(MoerProcessor.isChatRecently(chatHistory, 2000)).toEqual(false)
//   })
// })