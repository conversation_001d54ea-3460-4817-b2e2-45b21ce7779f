import { Config } from '../../../../../../../config/config'
import { AccountEventProcessor } from '../account_processor'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '****************',
      botUserId: '<PERSON><PERSON><PERSON>',
      name: '暴叔',
      notifyGroupId: 'R:*****************',
      classGroupId: 'x',
      courseNo: 1
    }
  })

  it('should pass', async () => {
    await (AccountEventProcessor as any).handleRumenyingOrder({
      userId: 781967,
      mobile: '***********',
      retryTime: 3
    })
  }, 60000)
})