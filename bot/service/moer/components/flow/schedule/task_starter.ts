import { MoerProcessor } from './processor/processor'
import { Job, Queue, Worker } from 'bullmq'
import { RedisDB } from '../../../../../model/redis/redis'
import { Config } from '../../../../../config/config'
import logger from '../../../../../model/logger/logger'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { DataService } from '../../../getter/getData'
import { AccountEventProcessor } from './processor/account_processor'
import { getVisualizedSopQueueName, visualizedSopStartTasks } from '../../visualized_sop/visualized_sop_task_starter'
import dayjs from 'dayjs'
import { SopScheduleTime } from '../../visualized_sop/visualized_sop_type'
import { ITask } from '../../schedule/type'
import { getBotId } from '../../../../../config/chat_id'
import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'
import { startSilentReAskSystem } from '../../schedule/silent_reask_tasks'
import { TaskWorker } from '../../planner/task/task_worker'
import { BigPlanner } from '../../planner/daily_plan/big_planner'

/**
 * 获取当期客户，当前账号下的客户 进行消息队列监听
 * @constructor
 */
export async function startWorkers() {
  // 监听 SOP 队列
  MoerProcessor.startGeneral(getGeneralSopKey())

  // 启动 SilentReAsk 系统
  startSilentReAskSystem()

  // 添加账号处理事件（新订单，被删除事件等）
  new Worker(Config.setting.wechatConfig?.id as string, async (job: Job) => { // using bind_phone queue to reuse worker
    AccountEventProcessor.handle(job)
  }, {
    connection: RedisDB.getInstance()
  }).on('error', (err) => {
    logger.error('account event Worker 发生未捕获错误', err)
  })

  // 启动 Planner Worker
  TaskWorker.start()
}

/*
 *  创建任务
 */
export async function startTasks(userId: string, chatId: string, force:boolean = false) {
  // 如果已经添加过一次任务了，不再创建任务
  if (!force && ChatStateStore.get(chatId)?.state?.is_add_tasks) {
    return
  }

  try {
    await Promise.all([
      BigPlanner.addTasks(chatId),
      visualizedSopStartTasks(userId, chatId, calSopTime)
    ])

    await visualizedSopStartTasks(userId, chatId, calSopTime)
  } catch (e) {
    logger.error(`添加可视化sop失败 chatId: ${chatId} ${e}`)
    return
  }

  ChatStateStore.update(chatId, {
    state: {
      is_add_tasks: true
    }
  })
}

export async function calSopTime(time:SopScheduleTime, chatId:string):Promise<Date> {
  if (time.timeAnchor == 'course') {
    const day0 = dayjs(await DataService.getCourseStartTime(chatId)).hour(0).minute(0).second(0).subtract(1, 'day')
    const timeStringSplited = time.time.split(':')
    return day0.add(time.week * 7 + time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
  } else {
    const mongoClient = PrismaMongoClient.getInstance()
    const userInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
    if (userInfo?.created_at) {
      const day0 = dayjs(userInfo.created_at).hour(0).minute(0).second(0)
      const timeStringSplited = time.time.split(':')
      return day0.add(time.day, 'day').hour(Number(timeStringSplited[0])).minute(Number(timeStringSplited[1])).second(Number(timeStringSplited[2])).toDate()
    } else {
      throw ('没有create_at')
    }
  }
}

export async function clearTasks(chatId: string) {
  const anotherQueue = new Queue<ITask>(getGeneralSopKey(), {
    connection: RedisDB.getInstance()
  })

  const data = (await anotherQueue.getJobs()).filter((item) => item.data.chatId == chatId)
  for (const sop of data) {
    await sop.remove()
  }
}

export function getGeneralSopKey(botId?: string) {
  if (!botId && !Config.setting.wechatConfig?.id) {
    throw ('没有设置wechat config id')
  }

  return `moer-general-sop-${botId ? botId : Config.setting.wechatConfig?.id}`
}


export async function listSOPByChatId(chatId: string): Promise<Job[]> {
  try {
    const queue = new Queue<ITask>(getVisualizedSopQueueName(getBotId(chatId)), {
      connection: RedisDB.getInstance()
    })

    const jobs =  await queue.getDelayed()

    return jobs.filter((item) => item.data.chatId == chatId)
  } catch (error) {
    // 如果获取队列失败（比如Config.setting.wechatConfig?.id未设置），返回空数组
    return []
  }
}