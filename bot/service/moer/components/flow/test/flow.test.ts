import { WorkFlow } from '../flow'
import { UUID } from '../../../../../lib/uuid/uuid'
import { getChatId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../../../model/redis/redis'

describe('Test', function () {
  beforeAll(() => {

  })

  it('', async () => {
    const queueName = `classGroupTask_${'R:10841257605945218'.replaceAll(':', '')}`
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    const waitingJobs = await queue.getJobs()

    console.log(JSON.stringify(waitingJobs, null, 4))
  }, 60000)

  it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)

  it('情绪化问题测试', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，我完蛋了，怎么办啊')
    await WorkFlow.step(chat_id, user_id, '我没啥具体问题，就是没学上了，求一下安慰')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
  }, 1E8)

  it('留学规划测试 -> 进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '可以的')
  }, 1E8)

  it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await WorkFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await WorkFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await WorkFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await WorkFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await WorkFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)

  it('split', async () => {
    const repairedValue = '1，2,3，4'
    console.log(repairedValue.split(/[,，]/))
  }, 60000)


  it('regex', async () => {
    const s =  '75分有点低，申请名校难度大\n你有考虑哪些国家吗？\n预算大概多少？'
    console.log(/.*[？?么吗啥].*/.test('好的，咱们现在本科几年级？\n'))
  }, 30000)

})