import { PromptTemplate } from '@langchain/core/prompts'

const chat_history_json = `
{
    "chat_history": [
        {
            "role": "user",
            "content": "你好",
            "time": "2023-01-05 20:23:09"
        },
        {
            "role": "user",
            "content": "针对我的情况您有什么好的建议吗谢谢",
            "time": "2023-01-05 20:23:26"
        },
        {
            "role": "me",
            "content": "我是暴叔，咨询的人太多，无法及时回复。先留下你的信息，稍后会回复你的\\n1. 学生背景情况：当下学历（中专/大学写上是否全日制），学校平均成绩，年龄，想要申请的专业，英语情况等\\n2. 写下留学目的国和家里花费的留学预算（写清楚是一共还是一年的）",
            "time": "2023-01-05 20:29:35"
        },
        {
            "role": "user",
            "content": "在职硕士毕业，34岁，需要申请博士，国内不行要脱产，工作走不开，看看有没合适的方案呢谢谢",
            "time": "2023-01-05 20:41:14"
        },
        {
            "role": "me",
            "content": "工作了多长时间了",
            "time": "2023-01-05 20:42:35"
        },
        {
            "role": "user",
            "content": "十年",
            "time": "2023-01-05 20:42:53"
        },
        {
            "role": "user",
            "content": "本科毕业就工作",
            "time": "2023-01-05 20:43:01"
        },
        {
            "role": "me",
            "content": "你有多少预算",
            "time": "2023-01-05 21:06:03"
        },
        {
            "role": "user",
            "content": "不离谱就行",
            "time": "2023-01-05 21:06:26"
        },
        {
            "role": "user",
            "content": "这个不太是问题",
            "time": "2023-01-05 21:07:01"
        },
        {
            "role": "me",
            "content": "常规方式就是申请，但你还是要出去上学的",
            "time": "2023-01-05 21:09:59"
        },
        {
            "role": "me",
            "content": "我给你拉个群吧",
            "time": "2023-01-05 21:10:15"
        },
        {
            "role": "user",
            "content": "有没有国内分校的类型",
            "time": "2023-01-05 21:11:19"
        },
        {
            "role": "user",
            "content": "好",
            "time": "2023-01-05 21:11:23"
        }
    ],
    "chatID": "(wxid_yui1g476kyxk11)",
    "summary_result": {
        "userNeeds": "客户目前为在职硕士毕业，年龄34岁，想要申请博士学位。但由于国内无法脱产且工作无法离开，客户表达了对特定学习情况（如国内分校等）的需要。",
        "consultantAdvice": "暴叔建议，常规的申请方式仍需脱产出国学习，面对客户提出的特定需求（如国内分校的类型），建议了解更多信息和可能的途径。",
        "use_model": "cheap_gpt-4-turbo"
    },
    "user_info_result": {
        "current_education_background": "硕士",
        "application_stage": "博士",
        "is_parent": false,
        "age": "34",
        "goal": "其他",
        "location": "国内",
        "budget": "不离谱就行",
        "use_model": "cheap_gpt-4-turbo"
    },
    "completed_qas_result": {
        "qa": [
            {
                "针对我34岁在职硕士毕业并想申请博士的情况，您有什么好的建议吗？": "我是暴叔，咨询的人太多，无法及时回复。先留下你的信息，稍后会回复你的 1. 学生背景情况：当下学历（中专/大学写上是否全日制），学校平均成绩，年龄，想要申请的专业，英语情况等 2. 写下留学目的国和家里花费的留学预算（写清楚是一共还是一年的）"
            },
            {
                "作为一名已工作十年的在职博士申请者，有没有合适的留学方案？": "常规方式就是申请，但你还是要出去上学的"
            }
        ],
        "use_model": "cheap_gpt-4-turbo"
    }
}
`

export class ConvertHistorytoTestPrompt {
  public static async format(chat_history_json: string) {
    return PromptTemplate.fromTemplate(`
<Instructions>
    You are tasked with converting chat_history JSON to test-oriented TypeScript file. This will help build test workflow.
    
    You will be provided with chat records in JSON format. Your task is to extract the messages from the "user" field within the "chat_history" field and retrieve their content. Then, based on the format of the Conversion_Result in the provided example, fill in the content to construct a new test.
    
    # chat_history_json:
    <chat_history_json>
    {{chat_history_json}}
    </chat_history_json>
    
    <Example 1>
    <chat_history_json>
    {
    "chat_history": [
        {
            "role": "user",
            "content": "你好",
            "time": "2023-01-05 20:23:09"
        },
        {
            "role": "me",
            "content": "我是暴叔，咨询的人太多，无法及时回复。先留下你的信息，稍后会回复你的\\n1. 学生背景情况：当下学历（中专/大学写上是否全日制），学校平均成绩，年龄，想要申请的专业，英语情况等\\n2. 写下留学目的国和家里花费的留学预算（写清楚是一共还是一年的）",
            "time": "2023-01-05 20:29:35"
        },
        {
            "role": "user",
            "content": "我是在职硕士毕业，34岁，需要申请博士，国内不行要脱产，工作走不开，看看有没合适的方案呢谢谢",
            "time": "2023-01-05 20:41:14"
        },
        {
            "role": "me",
            "content": "你有多少预算",
            "time": "2023-01-05 21:06:03"
        },
        {
            "role": "user",
            "content": "预算不离谱就行，不太是问题",
            "time": "2023-01-05 21:06:26"
        },
        {
            "role": "me",
            "content": "常规方式就是申请，但你还是要出去上学的",
            "time": "2023-01-05 21:09:59"
        },
        {
            "role": "user",
            "content": "我比较想去国内分校的类型",
            "time": "2023-01-05 21:11:19"
        },
        {
            "role": "me",
            "content": "我给你拉个群吧",
            "time": "2023-01-05 21:11:25"
        },
        {
            "role": "user",
            "content": "好的，暴叔你拉群吧",
            "time": "2023-01-05 21:11:34"
        }
    ],
    "chatID": "(wxid_yui1g476kyxk11)",
    "summary_result": {
        "userNeeds": "客户目前为在职硕士毕业，年龄34岁，想要申请博士学位。但由于国内无法脱产且工作无法离开，客户表达了对特定学习情况（如国内分校等）的需要。",
        "consultantAdvice": "暴叔建议，常规的申请方式仍需脱产出国学习，面对客户提出的特定需求（如国内分校的类型），建议了解更多信息和可能的途径。",
        "use_model": "cheap_gpt-4-turbo"
    },
    "user_info_result": {
        "current_education_background": "硕士",
        "application_stage": "博士",
        "is_parent": false,
        "age": "34",
        "goal": "其他",
        "location": "国内",
        "budget": "不离谱就行",
        "use_model": "cheap_gpt-4-turbo"
    },
    "completed_qas_result": {
        "qa": [
            {
                "针对我34岁在职硕士毕业并想申请博士的情况，您有什么好的建议吗？": "我是暴叔，咨询的人太多，无法及时回复。先留下你的信息，稍后会回复你的 1. 学生背景情况：当下学历（中专/大学写上是否全日制），学校平均成绩，年龄，想要申请的专业，英语情况等 2. 写下留学目的国和家里花费的留学预算（写清楚是一共还是一年的）"
            },
            {
                "作为一名已工作十年的在职博士申请者，有没有合适的留学方案？": "常规方式就是申请，但你还是要出去上学的"
            }
        ],
        "use_model": "cheap_gpt-4-turbo"
    }
    }
    </chat_history_json>
    
    <Conversion_Result>
  it('留学规划测试 -> 进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await MoerFlow.step(chat_id, user_id, '你好')
    await MoerFlow.step(chat_id, user_id, '我是在职硕士毕业，34岁，需要申请博士，国内不行要脱产，工作走不开，看看有没合适的方案呢谢谢')
    await MoerFlow.step(chat_id, user_id, '预算不离谱就行，不太是问题')
    await MoerFlow.step(chat_id, user_id, '我比较想去国内分校的类型')
    await MoerFlow.step(chat_id, user_id, '好的，暴叔你拉群吧')
  }, 1E8)
    </Conversion_Result>
    
    </Example 1>
    
    
    <Example 2>
    <chat_history_json>
    {
    "chat_history": [
        {
            "role": "me",
            "content": "我是暴叔，咨询的人太多，无法及时回复。先留下你的信息，稍后会回复你的\\n1. 学生背景情况：当下学历（中专/大学写上是否全日制），学校平均成绩，年龄，想要申请的专业，英语情况等\\n2. 写下留学目的国和家里花费的留学预算（写清楚是一共还是一年的）",
            "time": "2023-01-27 22:05:00"
        },
        {
            "role": "user",
            "content": "暴叔您好，我看你b站视频很久了，我个人是自考专科和，专科23年底毕业，本科也是自考预计是24年毕业，我想水个国外硕士，但是本科学位证要25年才能拿到等的太久了，我听朋友说蒙古国留学水硕士可以国内也认",
            "time": "2023-01-27 22:10:00"
        },
        {
            "role": "user",
            "content": "我是非全日制本科没学位证想混个一年制硕士研究生预算五六万之间可出境或者不出境",
            "time": "2023-01-27 22:11:40"
        },
        {
            "role": "user",
            "content": "多少预算有办法呢",
            "time": "2023-01-27 23:22:24"
        },
        {
            "role": "me",
            "content": "20以上吧",
            "time": "2023-01-27 23:22:26"
        },
        {
            "role": "user",
            "content": "蒙古国的不行吗，都说蒙古国的便宜",
            "time": "2023-01-28 00:29:57"
        },
        {
            "role": "user",
            "content": "我对qs排名没要求",
            "time": "2023-01-28 00:30:12"
        },
        {
            "role": "me",
            "content": "可以啊 你可以去申请",
            "time": "2023-01-28 22:25:15"
        }
    ],
    "chatID": "-(wxid_b3tkogpenj7f22)",
    "summary_result": {
        "userNeeds": "客户是一名自考专科和本科的学生，预计2024年本科毕业，希望尽快取得国外硕士学位，但由于本科学位证要到2025年才能拿到，客户考虑申请一年制硕士研究生项目。客户没有对QS排名有要求，并提出在留学预算约为五六万人民币的情况下，有无可能申请蒙古国的硕士项目，因为听说蒙古国的留学成本较低。",
        "consultantAdvice": "留学顾问指出，客户的预算不足，至少需要20万人民币左右。在客户询问蒙古国的留学项目是否可行时，顾问认为蒙古国留学的硕士项目是一个可行的选择并建议客户可以尝试申请。",
        "use_model": "cheap_gpt-4-turbo"
    },
    "user_info_result": {
        "current_education_background": "\\n",
        "school": "\\n",
        "gpa": "\\n",
        "application_major": "\\n",
        "language_score": "\\n",
        "budget": "5-6 \\n",
        "user_intended_country": "\\n",
        "use_model": "cheap_gpt-4-turbo"
    },
    "completed_qas_result": {
        "qa": [
            {
                "在做自考专科和本科，预计23年底毕业，但是本科学位证要25年才能拿到的情况下，蒙古国的水硕士学位在中国内地是否被认可？": "可以啊 你可以去申请"
            },
            {
                "作为正在自考本科但尚未取得学位证的学生，想要申请一年制国外硕士研究生，预算在五六万左右，这样的预算是否足够？": "没有办法"
            },
            {
                "之前提到的专科学历是否也是通过非全日制的方式获得的？": "对"
            },
            {
                "在只有五六万的预算情况下，是否有可能出境或者不出境获得一年制的硕士学位？": "没有办法"
            },
            {
                "如果五六万的预算不足以支付留学硕士的费用，那至少需要多少预算才有可能申请成功？": "20以上吧"
            },
            {
                "考虑到蒙古国留学成本相对较低，这样五六万的预算在蒙古国申请硕士学位是否可行？": "可以啊 你可以去申请"
            },
            {
                "如果我没有对QS世界大学排名提出要求，那么蒙古国的硕士学位在中国内地是否被认可？": "可以啊 你可以去申请"
            }
        ],
        "use_model": "cheap_gpt-4-turbo"
    }
    }
    </chat_history_json>
    
    <Conversion_Result>
 it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await MoerFlow.step(chat_id, user_id, '暴叔您好，我看你b站视频很久了，我个人是自考专科和，专科23年底毕业，本科也是自考预计是24年毕业，我想水个国外硕士，但是本科学位证要25年才能拿到等的太久了，我听朋友说蒙古国留学水硕士可以国内也认')
    await MoerFlow.step(chat_id, user_id, '我是非全日制本科没学位证想混个一年制硕士研究生预算五六万之间可出境或者不出境')
    await MoerFlow.step(chat_id, user_id, '多少预算有办法呢')
    await MoerFlow.step(chat_id, user_id, '蒙古国的不行吗，都说蒙古国的便宜')
    await MoerFlow.step(chat_id, user_id, '我对qs排名没要求')
  }, 1E8)
    </Conversion_Result>
    
    </Example 2>
    
    Follow these steps to complete test.
    Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.
</Instructions>
`, { templateFormat: 'mustache' }).format(
      // @ts-ignore mustache 只忽略 {{}}
      {
        chat_history_json: chat_history_json
      })
  }
}

// 格式化表格数据的 prompt
`
请按照以下格式输出转化下面的数据，
原数据格式如下：
{项目名称}{申请阶段}{入学基础}{总预算}{总预算}{项目介绍}{国家}{是否可以留在国内上学}

目标格式如下：
"""
    [项目名称] 是一个[申请阶段]的项目，[在国内/外]，主要招收[入学基础]学生。该项目的学费范围是[费用范围]万。[项目特点]。[是否可以留在国内上学]。
"""

需要转化的数据如下：
"""
华侨生联考 高中 初中  60-100  需要父母一方拿到国外的绿卡，比如泰国、希腊等境外呆满2年拿到绿卡。学生可以华侨生联考，联考难度低于国内高考    中国 是
国内国际高中 高中 初中,高中  80-80  A-level、 AP、 1B高中体系，读完可以申请英、美、澳、加等名校    英国，美国，加拿大，澳洲 否
OSSD课程 高中 初中,高中  80-80  被称为“差生捷径〞，国內和加拿大就读就可以，建议去加拿大当地读    加拿大，美国 否
普通美国高中 高中 初中,高中  200-200  美高的本科优势明显，对于后期升学能带来帮助    美国 否
国际本科 本科 高中  50-100  3+1， 2+2, 1+3，国内合作院校读几年，最后几年在海外院校读，文凭是海外院校的学位证书.    英国，美国，澳大利亚 是
中外合办 4+0 本科 高中 35-45  3年、4年都在国内读书，享的母海外大学的本科学位 4.0   英国，美国，加拿大，澳大利亚 是
新加坡私立 本科 初中,高中  70-80  新加坡私立大学，在新加坡读海外名校，24-30个月毕业    英国，美国，澳大利亚 否
韩国3+1中文辅助 本科 初中,高中,专科  40-45  0韩语可以直接入学，难度较低，1+3，韩国大学第一年学韩语，后面三年专业课，4年左右时间    韩国 否
加拿大 College 研文 硕士 初中,高中  15-25  加拿大职业学校，研究生文凭课程，一般学制为一年，也有少部分两年的专业。支持专科申请，适合就业移民。    加拿大 否
加拿大硕士 硕士 本科  40-60  适合移民    加拿大 否
澳大利亚硕士 硕士 本科  35-40  适合移民，要求低    澳大利亚 否
美国本科 本科 高中  240-240  "认知较高，有较远大的理想志愿，冲顶级名校

对尊重个性化的发展，自由探索型教育比较感兴趣

成绩优秀或者国际学校就读学生、自身整体的留学规划起始的较早

适合对于不确定未来专业选择的同学，美国实行通识教育，换专业，转学分难度不大

经济预算非常宽裕的同学，相对富裕的中产家庭和富人。至少每年有60W以上的留学预算"    美国 否
英国本科 本科 高中  160-240  "成绩相对优异，国内院校背景还不错的学生

经济预算比较宽裕的同学，最少每年40W-60W的留学预算，或者预算去美国不太够，退而求其次，想冲名校的同学

偏科，但是具备特殊才能或者兴趣的同学

一年制硕士，适合想快速拿出硕士文凭的同学，回国工作的同学

适合大专生拿学历，TOP-UP专升本，或者直接专升硕"    英国 否
澳大利亚本科 本科 高中  50-80  "低分上名校。适合比较在乎大学排名，但成绩一般的同学。成绩去英国可能进不了前100，但去澳洲择校能上升一个档次

有经济预算的同学，澳洲每年35W—45W左右

学习能力较强，自律性较高的学生。澳洲大学宽进严出，容易进但是不容易出

奔着移民去的同学。澳洲是移民国家，无论念的是TAFE、本科还是硕土，都有机会留在当地"    澳大利亚 否
韩国本科 本科 高中,专科  40-50  追求留学性价比。预算有限，一年留学总预算10-15W左右。需要高中毕业后，先学韩语，达到topik等级3级后，申请韩国大学本科，需要1（学语言）+4（韩国本科4年），5年左右时间。可半工半读    韩国 否
香港本科 本科 高中  30-90  "希望出去留学，但是又不想离家太远的同学。香港离家近，学校认可度高，没有时差

考研失利的同学，考研后港校这边也还没有截止申请，也是给考研失利的同学一次上岸读研的机会（港校是先到先得，建议在考研前递交，双保险）

高考失利的同学，与其读大专，还不如看看香港是否能申到本科。即便申不到本科，香港也有副学士的项目，读完之后可以继续在香港读本科

想留在香港本地发展，或者想拿香港本科学位做跳板的同学，香港的学历在国际上比较受认可"    香港 否
加拿大本科 本科 高中  100-240  移民，或者之后计划到美国工作    加拿大 否
新加坡本科 本科 高中  120-140  "有经济预算的同学，每年的留学总预算30-35W

学霸冲名校，成绩优异的同学，申请新加坡公立大学，目标新二，新加坡国立和南洋理工

低分要逆袭，成绩一般或者较弱的同学，申请新加坡私立大学，就读私立大学的合作办学项目，冲刺前Q＄200

专科生升学，就读私立大学的合作办学项目，实现专升本或者专升硕

快速成长型，学生成绩较弱，最低初中毕业或者高一结业，更换赛道，就读私立大学的合作办学项目，能够较快的速度拿出硕士文凭（高中毕业，最快38个月，拿到硕士文凭）"    新加坡 否
国际本科2+2 本科 高中 80-100  国内2年，国外读2年，拿海外大学的本科学位，对接院校QS200以内。 4.0    否
新加坡sim私立大学 硕士 本科 30-35       否
"""
按照我的要求每十条地转化数据。
请深呼吸一下，当完成任务的话我会给你2000刀小费。
`