import { CheckPreCourseCompletionTask } from '../schedule/task/checkPreCourseCompletion'
import { ClassGroupSend } from '../schedule/task/classGroupSend'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const tasks1 = await ClassGroupSend.getTask()
    const tasks2 = await CheckPreCourseCompletionTask.getTask('', '')

    console.log(tasks1.length + tasks2.length)
  }, 60000)
})