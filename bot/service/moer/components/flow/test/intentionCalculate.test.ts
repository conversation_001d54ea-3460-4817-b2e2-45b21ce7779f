// import { IntentionCalculateNode } from '../nodes/intentionCalculate'
// import { UUID } from '../../../../../lib/uuid/uuid'
// import { HashMessagesHandler } from '../../interrupt_handler'
// import { ChatStateStore } from '../../../storage/chat_state_store'
// import { ChatHistoryService } from '../../chat_history'
//
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const uuid = UUID.short()
//     // const
//     // ChatStateStore.update(uuid)
//
//     // if (userSlots && state !== )
//     await ChatHistoryService.addUserMessage(uuid, '我高二')
//
//     await IntentionCalculateNode.invoke({
//       chat_id: uuid,
//       user_id: uuid,
//       userMessage: '',
//       interruptHandler: new HashMessagesHandler([{
//         content: '我高二',
//         role: 'user',
//         created_at: new Date(),
//         chat_id: uuid
//       }]),
//       round_id: UUID.short()
//     })
//   }, 60000)
// })