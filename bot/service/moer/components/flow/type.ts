


export enum BaoshuNode {
    IntentionCalculate = 'intention_calculate',
    EarlyAgeInvitePending = 'early_age_invite_pending',
    EarlyAgeInvited = 'early_age_invited',

    LLMOutput = 'llm_output',
    StudyAbroadPlanningRoute = 'study_abroad_planning_routing',
    SelfPlan = 'self_plan', // 自我规划
    SelfPlanConsult = 'self_plan_consult', // 自我规划咨询
    InternalPlan = 'internal_plan', // 暴叔内部规划

    StudyAbroadPlanningConsult = 'study_abroad_planning_consult',
    InviteConsultant = 'invite_consultant', // 拉顾问群
    END = 'end',
    FillFormNode = 'fill_form_node', // 填写表格
    DoctorConsult = 'doctor_consult', // 博士咨询

    BudgetReConfirm = 'budget_re_confirm', // 预算再确认
    ParentSupportReconfirm = 'parent_support_reconfirm', // 支持再确认

}