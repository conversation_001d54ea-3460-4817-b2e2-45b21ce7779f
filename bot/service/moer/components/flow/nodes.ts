import { MoerNode } from './nodes/type'
import { MoerWorkFlowNode } from './nodes/baseNode'
import { FreeTalk } from '../agent/freetalk'
import { PhoneQuery } from './nodes/phoneQuery'
import { IntentionQueryNode } from './nodes/intentionQuery'
import { IntentionQueryBeforeClass } from './nodes/intentionQueryBeforeClass'
import { EnergyTestAnalyze } from './nodes/energyTestAnalyze'
import {
  CourseFeedbackDay1,
  CourseFeedbackDay2,
  CourseFeedBackDay3InClass,
  CourseFeedBackDay3NotInClass
} from './nodes/courseFeedback'
import { WealthOrchardAnalyze } from './nodes/wealthOrchard'
import { RespondCourseDateNode } from './nodes/respondCourseDateNode'
import { SendFileNode } from './nodes/sendFile'
import { PostSaleNode } from './nodes/postSaleNode'
import { SendInviteLinkNode } from './nodes/sendInviteLinkNode'


export const MoerNodeMap =  new Map<MoerNode, typeof MoerWorkFlowNode>(
  [
    [MoerNode.PhoneQuery, PhoneQuery],
    [MoerNode.IntentionQuery, IntentionQueryNode],
    [MoerNode.IntentionQueryBeforeClass, IntentionQueryBeforeClass],
    [MoerNode.EnergyTestAnalyze, EnergyTestAnalyze],
    [MoerNode.FreeTalk, FreeTalk],
    [MoerNode.CourseFeedBackDay1, CourseFeedbackDay1],
    [MoerNode.CourseFeedBackDay2, CourseFeedbackDay2],
    [MoerNode.CourseFeedBackDay3NotInClass, CourseFeedBackDay3NotInClass],
    [MoerNode.CourseFeedBackDay3InClass, CourseFeedBackDay3InClass],
    [MoerNode.WealthOrchardAnalyze, WealthOrchardAnalyze],
    [MoerNode.Sales, FreeTalk],
    [MoerNode.PostSale, PostSaleNode],
    [MoerNode.SendInviteLink, SendInviteLinkNode],
    [MoerNode.RespondCourseDate, RespondCourseDateNode],
    [MoerNode.SendFile, SendFileNode],
  ]
)