import { JuziAPI } from '../../../../../lib/juzi/api'
import { Config } from '../../../../../config/config'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { getChatId } from '../../../../../config/chat_id'
import { randomSleep } from '../../../../../lib/schedule/schedule'
import logger from '../../../../../model/logger/logger'

export async function inviteToGroup(userId: string, waitTime: number = 3 * 60 * 1000) {
  try {
    // 建议拉人进群时，每分钟不超过50次： 所以随机到 3 分钟内
    await randomSleep(100, waitTime)// 等待随机秒数

    const response = await JuziAPI.addToGroup({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      contactWxid: userId,
      roomWxid: Config.setting.wechatConfig?.classGroupId as string
    })

    // Check for errors in the response
    if (response.errcode !== undefined && response.errcode !== 0) {
      // Only handle errors that are not -8 or -9
      if (response.errcode !== -8 && response.errcode !== -9) {
        logger.error('拉群失败', { errcode: response.errcode, errmsg: response.errmsg }, userId)

        // 只在 周五通知
        if (new Date().getDay() === 5) {
          // 转人工处理
          await HumanTransfer.transfer(
            getChatId(userId),
            userId,
            HumanTransferType.FailedToJoinGroup,
            'onlyNotify',
            `错误码: ${response.errcode}, 错误信息: ${response.errmsg || '未知错误'}`
          )
        }
      }
    }
  } catch (e) {
    logger.error('拉群失败', e, userId)
    if (e instanceof Error) {
      // 转人工处理
      await  HumanTransfer.transfer(getChatId(userId), userId, HumanTransferType.FailedToJoinGroup, 'onlyNotify', e.message)
    }
  }
}