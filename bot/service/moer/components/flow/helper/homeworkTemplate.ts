import { calculateJaccardSimilarity } from '../../../../../lib/text/text_similarity'
import { ChatStateStore, ChatStatStoreManager } from '../../../storage/chat_state_store'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { MoerNode } from '../nodes/type'
import { CourseFeedbackDay1 } from '../nodes/courseFeedback'
import { ChatInterruptHandler } from '../../message/interrupt_handler'
import { UUID } from '../../../../../lib/uuid/uuid'
import { DateHelper } from '../../../../../lib/date/date'
import { DataService } from '../../../getter/getData'
import { sleep } from '../../../../../lib/schedule/schedule'
import { EventTracker, IEventType } from '../../../../../model/logger/data_driven'
import { WealthOrchardAnalyze } from '../nodes/wealthOrchard'

export class HomeworkTemplate {
  public static matchDay1Template(userMessage: string) {
    // 正则匹配
    const regex = /#冥想day1/i
    const sharePartRegex = /感悟分享[:：]/i  // 匹配 🔺1.感悟分享

    // 检查文本是否包含这两个部分
    const hasMeditationDay = regex.test(userMessage)
    const hasReflection = sharePartRegex.test(userMessage)

    // 如果两个部分都匹配，则认为这是一个课程作业
    if (hasMeditationDay || hasReflection) {
      return true
    }

    // 模糊匹配
    if (calculateJaccardSimilarity(userMessage, `🧘🏻‍♀#冥想day1

🔺1.感悟分享：`) > 0.5) {
      return true
    }

    // TODO 语义匹配
    return false
  }

  static async matchDay2Template(chatId: string, userMessage: string) {
    // 正则匹配
    const regex = /#冥想day2/i
    const sharePartRegex = /感悟分享[:：]/i  // 匹配 🔺1.感悟分享

    // 检查文本是否包含这两个部分
    const hasMeditationDay = regex.test(userMessage)
    const hasReflection = sharePartRegex.test(userMessage)

    // 如果两个部分都匹配，则认为这是一个课程作业
    if (hasMeditationDay || hasReflection) {
      return true
    }

    // 模糊匹配
    if (calculateJaccardSimilarity(userMessage, `🧘🏻‍♀#冥想day2

🔺1.感悟分享：`) > 0.5) {
      return true
    }

    if (calculateJaccardSimilarity(userMessage, `同学可以分享自己财富果园的画面，发在【班级群】里，因为每个人都不一样，后续我会逐一解读哦。

比如👉：
大门新旧、材质、颜色：
有无围栏：
主树是什么：
周围的树是什么：
秋天动作、有无变现：
呼吸顺畅吗：
四季循环变化怎么样：`) > 0.6) {
      return true
    }

    const currentTime = await DataService.getCurrentTime(chatId)

    const isDay2 = currentTime.is_course_week &&
        (currentTime.day === 2 && DateHelper.isTimeAfter(currentTime.time, '21:00:00') ||
            (currentTime.day === 3 && DateHelper.isTimeBefore(currentTime.time, '20:00:00')))

    if (isDay2) {
      if (userMessage.includes('大门材质') || userMessage.includes('果') || userMessage.includes('树') || userMessage.includes('门') || userMessage.includes('四季变化')) {
        return true
      }
    }

    // TODO 语义匹配
    return false
  }


  public static async handleDay1Homework(chatId: string, userId: string, text: string) {
    if (ChatStateStore.getFlags(chatId).is_complete_day1_homework) {
      return
    }

    // 群打卡作业进行延迟回复
    await sleep(10 * 60 * 1000)

    await ChatHistoryService.addUserMessage(chatId, text)
    ChatStateStore.update(chatId, {
      nextStage: MoerNode.CourseFeedBackDay1
    })

    await CourseFeedbackDay1.invoke({
      chat_id: chatId,
      user_id: userId,
      userMessage: text,
      interruptHandler: await ChatInterruptHandler.create(chatId),
      round_id: UUID.v4()
    })
  }

  public static async handleDay2Homework(chatId: string, userId: string, text: string) {
    if (ChatStateStore.getFlags(chatId).is_complete_day2_homework) {
      return
    }

    await ChatHistoryService.addUserMessage(chatId, text)

    ChatStateStore.update(chatId, {  // 这里解读时间比较长，提前打上 flag
      state: {
        is_complete_day2_homework: true
      }
    })

    const node = await WealthOrchardAnalyze.invoke({
      chat_id: chatId,
      user_id: userId,
      userMessage: text,
      interruptHandler: await ChatInterruptHandler.create(chatId),
      round_id: UUID.v4()
    })

    // 节点路由回正确的节点
    ChatStateStore.update(chatId, { nextStage: node })
  }

  static async isHomeworkTemplate(chatId: string, text: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    const isDay1 =  currentTime.is_course_week &&
        (currentTime.day === 1 ||
          (currentTime.day === 2 && DateHelper.isTimeBefore(currentTime.time, '21:00:00')))
    const isDay2 = currentTime.is_course_week &&
        (currentTime.day === 2 && DateHelper.isTimeAfter(currentTime.time, '20:00:00') ||
            (currentTime.day === 3 && DateHelper.isTimeBefore(currentTime.time, '20:00:00')))


    if (isDay1 && HomeworkTemplate.matchDay1Template(text)) {
      return 'day1'
    } else if (isDay2 && await HomeworkTemplate.matchDay2Template(chatId, text)) {
      return 'day2'
    }

    return null
  }

  static async handleHomework(text: string, userId: string, chatId: string) {
    const homeWorkType = await HomeworkTemplate.isHomeworkTemplate(chatId, text)
    if (!homeWorkType) {
      return
    }

    await ChatStatStoreManager.initState(chatId)

    if (homeWorkType === 'day1') {
      await HomeworkTemplate.handleDay1Homework(chatId, userId, text)
      ChatStateStore.update(chatId, {
        state: {
          is_complete_day1_homework: true,
        }
      })
    } else if (homeWorkType === 'day2') {
      await HomeworkTemplate.handleDay2Homework(chatId, userId, text)
    } else {
      return
    }

    await DataService.saveChat(chatId, userId)

    const moerId = await DataService.getMoerIdByChatId(chatId)
    EventTracker.track(chatId, IEventType.HomeworkComplete, {
      more_id: moerId,
      class_day: homeWorkType === 'day1' ? 1 : 2,
    })
  }
}