import { ChatHistoryWithRoleAndDate, chatHistoryWithRoleAndDateListToString, ExtractUserSlots, ExtractUserSlotsV2 } from '../slotsExtract'
import { DataService } from '../../../../getter/getData'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import path from 'path'
import fs from 'fs'
import { FileHelper } from '../../../../../../lib/file'
import { HomeworkTemplate } from '../homeworkTemplate'

describe('槽位提取', function () {
  jest.setTimeout(100000)
  beforeAll(() => {

  })

  it('homework', async () => {
    const res = await HomeworkTemplate.isHomeworkTemplate('7881303234925267_1688855184697783', `冥想入门打卡模板
————————————
🧘🏻‍♀#冥想𝘿𝘼𝙔 2

❤大门材质与颜色：大门材质就是白色的颗粒感水泥墙，配着铁质大门
❤围栏材质：围栏也是水泥+铁栅栏
❤主树是有生命力的大树，粗壮，果实丰富，刚开始看到就是金色的果实，
❤主树周围是一颗颗果树（都差不多，没有大小概念）感觉果园挺大片的
[爱心]秋天动作：有很多人在丰收，一片劳作
[爱心]呼吸是否顺畅：顺畅，
❤四季变化是怎样的：四季稍微有变化`)
    console.log(res)
  }, 60000)

  it('1. 构建数据集', async () => {
    const unionCase = ['小淼焱', '金莲', 'lily', '淑红']


    const extractSlotDataSet:any[] = [] // 创建一个数组来存储所有的聊天历史

    for (const weChatName of unionCase) {
      const chats = await DataService.getChatByWechatName(weChatName)
      if (!chats) {
        return
      }
      const chat =  chats[0] as any
      const chatId = chat._id
      const userChatHistory = await ChatHistoryService.formatHistoryOnRole(chatId, 'user', 10)
      const chatHistory = await ChatHistoryService.getFormatChatHistoryByChatId(chatId)
      if (chatHistory) {
        const object = {
          weChatName: weChatName,
          chatId: chatId,
          chatHistory: chatHistory,
          userChatHistory: userChatHistory,
          expect:''
        }
        extractSlotDataSet.push(object)
      }

    }
    // 将聊天历史写入 JSON 文件
    const filePath = path.join(__dirname, 'extractSlotAfterThirdCourseData.json') // 定义文件路径
    await FileHelper.writeFile(filePath, JSON.stringify(extractSlotDataSet, null, 2))

  }, 60000)

  it('3. 运行数据集', async () => {
    DataService.getCurrentTime = jest.fn().mockResolvedValue({ day: 2, time: '22:00:00' })
    const resultSet:any[] = []
    const data: any[] = JSON.parse(await FileHelper.readFile(path.join(__dirname, 'dataSet/extractSlotDataSet.json')))
    for (const item of data) {
      item.actual = await ExtractUserSlots.extractUserSlots(item.chatHistory, item.chatId)
      resultSet.push(item)
    }
    const chatHistoriesJson = JSON.stringify(resultSet, null, 2)
    const filePath = path.join(__dirname, 'dataSet/extractSlotTestResult.json')
    fs.writeFileSync(filePath, chatHistoriesJson) // 写入文件
  }, 270000)


  it('4. 测试卡点提取', async () => {
    DataService.getCurrentTime = jest.fn().mockResolvedValue({ day: 4, time: '22:00:00' })
    const resultSet:any[] = []
    const data: any[] = JSON.parse(await FileHelper.readFile(path.join(__dirname, 'dataSet/extractSlotAfterThirdCourseData.json')))
    for (const item of data) {
      item.actual = await ExtractUserSlots.extractUserSlots(item.chatHistory, item.chatId)
      resultSet.push(item)
    }
    const chatHistoriesJson = JSON.stringify(resultSet, null, 2)
    const filePath = path.join(__dirname, 'dataSet/extractSlotAfterThirdCourseResult.json')
    fs.writeFileSync(filePath, chatHistoriesJson) // 写入文件
  }, 60000)

})
