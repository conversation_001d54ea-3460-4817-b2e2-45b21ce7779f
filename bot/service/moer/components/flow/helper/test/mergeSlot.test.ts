import { ExtractUserSlots } from '../slotsExtract'

describe('Test', function () {
  beforeAll(() => {

  })

  it('merge user slots', async () => {
    const slots = await (ExtractUserSlots as any).mergeUserSlots({
      phone: '123456789',
      meditation_experience: ['在冥想 App 学过'],
      goals_and_needs: ['缓解工作压力', ],
    }, {
      meditation_experience: ['在其他 App 学过'],
      goals_and_needs: ['提高专注力', '改善睡眠质量'],
    })
    // 键到中文的映射
    const keyToChinese = {
      meditation_experience: '- 冥想经验',
      goals_and_needs: '- 目标需求',
      pain_points: '- 主要痛点',
    }
    // 格式化输出
    const formattedSlots = Object.entries(slots).map(([key, values]) => {
      const chineseKey = keyToChinese[key] || key
      const valuesString = Array.isArray(values) ? values.join('，') : values
      return `${chineseKey}：${valuesString}`
    }).join('\n')

    console.log(`## 客户画像\n${formattedSlots}`)
  }, 60000)
})