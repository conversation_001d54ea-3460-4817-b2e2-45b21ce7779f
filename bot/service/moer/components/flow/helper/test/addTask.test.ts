import { DataService } from '../../../../getter/getData'
import { CacheDecorator } from '../../../../../../lib/cache/cache'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../../../../model/redis/redis'
import { PrismaMongoClient } from '../../../../../../model/mongodb/prisma'
import { clearTasks, startTasks } from '../../schedule/task_starter'
import { getUserId } from '../../../../../../config/chat_id'

describe('Test', function () {
  beforeAll(() => {

  })

  it('补充群发任务', async () => {
    const chats = await DataService.getChatsByCourseNo(54)
    const phones: string[] = []

    for (const chat of chats) {
      // @ts-ignore123123
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: chat.id
        },
        data: {
          course_no: 53,
        }
      })
    }

    console.log(phones)
  }, 60000)

  it('补回群发任务', async () => {
    const chats = await DataService.getChatsByCourseNo(53)
    MoerAPI.getCurrentCourseInfo =  CacheDecorator.decorateAsync(MoerAPI.getCurrentCourseInfo)

    for (const chat of chats) {
      if (chat.id.includes('R') || chat.id.includes('local')) {
        continue
      } else {
        // 刷新 任务列表

        if ([
          '19552902802', '13515060785',
          '15835512520', '18961726819',
          '13831108483', '18057178882',
          '15845886805', '18180501818',
          '15214880115', '18990339569',
          '15936870111', '13917009590'
          // @ts-ignore fku
        ].includes(chat?.chat_state?.userSlots.phoneNumber)) {
          const queue = new Queue(chat.id, {
            connection: RedisDB.getInstance()
          })

          await queue.obliterate({ force: true })

          const userId = getUserId(chat.id)

          await startTasks(userId, chat.id)
        }

      }
    }

  }, 60000)

  it('should process chats in batches of 50', async () => {
    const chats = await DataService.getChatsByCourseNo(49)
    const batchSize = 50

    console.log(`Retrieved ${chats.length} chats for course number 49.`)

    await MoerAPI.getCurrentCourseInfo(49)
    MoerAPI.getCurrentCourseInfo =  CacheDecorator.decorateAsync(MoerAPI.getCurrentCourseInfo)

    console.log(await MoerAPI.getCurrentCourseInfo(49))

    // 数字+_+数字
    // test_123123
    // local_123123
    // R:12312321_1231232

    for (let i = 0; i < chats.length; i += batchSize) {
      const batch = chats.slice(i, i + batchSize)
      console.log(`Processing batch ${i / batchSize + 1}: chats ${i} to ${Math.min(i + batchSize, chats.length) - 1}.`)

      const tasks = batch.map(async (chat) => {
        const chatId = chat.id
        try {
          await clearTasks(chatId)
          const userId = getUserId(chatId)

          // ChatStateStore.get(chatId)?.state?.is_add_task = false
          await startTasks(userId, chatId)
        } catch (error) {
          console.error(`Error processing chat ID: ${chatId}`, error)
        }
      })

      await Promise.all(tasks)
      console.log(`Completed processing batch ${i / batchSize + 1}.`)
    }

    console.log('All chats processed.')
  }, 60000)

  it('sadad', async () => {
    const chats = await DataService.getChatsByCourseNo(53)

    console.log(JSON.stringify(chats, null, 4))
  }, 60000)
})