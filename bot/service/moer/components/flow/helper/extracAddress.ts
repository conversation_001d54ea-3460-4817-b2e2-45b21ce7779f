import { z } from 'zod'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { JSONHelper } from '../../../../../lib/json/json'
import logger from '../../../../../model/logger/logger'


export class ExtractAddressSlots {
  public static async extract(prompt: string, schema: z.ZodType<any, any>, logInfo?: {
        chat_id?: string
        user_id?: string
        round_id?: string
    }) {

    const llm = new LLM({
      meta: {
        ...logInfo,
        promptName: 'ExtractAddressSlots',
        description: '提取客户地址'
      }
    })

    const llmRes = await llm.predict(prompt)
    const extractedInfo = XMLHelper.extractContent(llmRes, 'extracted_info')

    if (extractedInfo === null) {
      return null
    }

    try {
      const slotsObj = JSONHelper.parse(extractedInfo)

      const validationResult = schema.safeParse(slotsObj)
      if (!validationResult.success) {
        logger.error('extractSlots Validation failed', validationResult.error)
        return slotsObj
      }

      return slotsObj
    } catch (e) {
      console.error('error parsing extracted slots', e)
      return null
    }
  }

}