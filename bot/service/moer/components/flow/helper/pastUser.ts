import { JuziAPI } from '../../../../../lib/juzi/api'
import { Config } from '../../../../../config/config'
import logger from '../../../../../model/logger/logger'


export async function isPastUser(senderId: string) {
  if (senderId === '7881301047907394' || senderId === '7881299536073194') {
    return false
  }

  // 禁掉 AI 之间的聊天，企微之间的聊天
  if (senderId.startsWith('16888')) {
    return true
  }

  if (Config.setting.wechatConfig?.name && !(['syq'].includes(Config.setting.wechatConfig?.name))) {
    if (senderId === Config.setting.wechatConfig?.id) {
      return false
    }

    const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, senderId)
    if (!userInfo) {
      logger.warn('客户不存在', senderId)
      return false
    }

    if (['麦子', '哈哈哈', 'SYQ'].includes(userInfo.name)) {
      return false
    }

    if ((userInfo && Number(userInfo.createTimestamp) < new Date('2024-08-16T16:30:00+08:00').getTime())) {
      logger.warn('客户在2024-08-16 13:48:00之前创建', senderId)
      return true
    }
  }

  return false
}