import { IWecomMessage } from '../../../../lib/juzi/type'

export enum TextType {
  variable = 'variable',
  fixed = 'fixed',
}

export interface TextFixed {
  type: TextType.fixed
  text: string
}

export interface TextVariable {
  type: TextType.variable
  tag: string
}

export interface Situation {
  conditions: Condition[]
  action: Action[]
}

export type TimeAnchorType = 'register' | 'course'

export interface Sop {
    title: string
    time_anchor: TimeAnchorType
    week: number
    day:number
    time: string
    situations:Situation[]
    enable:boolean
    tag: string
    topic: string
}

export type Action = ContentTextPlain | ContentImage | ContentCustom | ContentVideo | ContentFile | ContentVoice | ContentLink | ContentVideoChannel | ContentDynamicPrompt;

export interface ContentTextPlain {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.text
  textList: (TextFixed | TextVariable)[]
  description: string
}

export interface ContentImage {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.image
  url: string
  description: string
}

export interface ContentVideo {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.video
  url: string
  description: string
}

export interface ContentVoice {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.voice
  url: string
  duration: number
  description: string
}

export interface ContentLink {
  // for prisma, don't remove
  [key: string]: any
  type: ActionType.link
  description: string
  source: LinkSource
  title: string
  summary: string
  imageUrl: string
}

export type LinkSource = LinkSourceFixed | LinkSourceVariable

export interface LinkSourceFixed {
  type:LinkSourceType.fixed
  url: string
}
export interface LinkSourceVariable {
  type:LinkSourceType.variable
  tag: string
}

export enum LinkSourceType {
  fixed = 'fixed',
  variable = 'variable'
}

export interface ContentFile {
  [key: string]: any
  type: ActionType.file
  name: string
  url: string
  description: string
}

export interface ContentCustom {
  [key: string]: any
  type: ActionType.custom
  tag: string
}

export interface ContentDynamicPrompt {
  [key: string]: any
  type: ActionType.dynamicPrompt
  description: string
  customPrompt: string
  dynamicPrompt: string
  chatHistoryRounds: number

  includeRAG: boolean
  includeMemory: boolean
  includeUserSlots: boolean
  includeTimeInfo: boolean
  includeUserBehavior: boolean
}

export interface ContentVideoChannel {
  [key: string]: any
  description:string
  type: ActionType.videoChannel
  avatarUrl: string      // 头像地址
  coverUrl: string       // 封面地址
  contentDescription: string    // 描述
  nickname: string       // 昵称
  thumbUrl: string       // 缩略图地址
  url: string            // 视频号地址
  extras: string         // 未知，请使用收到的字段信息
}

export enum ActionType {
  text = 'text',
  link = 'link',
  dynamicPrompt = 'dynamic_prompt',
  image = 'image',
  video = 'video',
  voice = 'voice',
  file = 'file',
  custom = 'custom',
  changeStage = 'changeStage',
  videoChannel = 'video_channel'
}

export type Condition = {
  //逻辑是正还是负
  isOrNotIs: boolean
  type: 'fixed' | 'dynamic'
  condition: string
}

export function getVisualizedRedisSopKey (botId:string) {
  return `moer:${botId}:visualized_sop`
}

export interface SopScheduleTime {
  day: number // 1-7 表示周一到周日
  week: number // 0表示上课周
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
  timeAnchor:TimeAnchorType
}

export interface ISendMedia {
   description: string // 描述
   msg: IWecomMessage // 消息
}

export interface IScheduleTask {
    sendTime?: Date // 发送时间
    scheduleTime: SopScheduleTime

    tag?: string // 标签
    [key: string]: any
}

export interface ITask extends IScheduleTask, IBaseInfo{}

export interface IBaseInfo {
    name: string // 任务名
    chatId: string
    userId: string
}