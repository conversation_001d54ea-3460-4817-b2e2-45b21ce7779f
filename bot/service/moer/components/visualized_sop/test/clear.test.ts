import { loadConfigByWxId } from '../../../../../../test/tools/load_config'
import { getBotId, getUserId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'
import { ChatStatStoreManager } from '../../../storage/chat_state_store'
import { MoerVisualizedSopProcessor } from '../moer_visualized_sop_processor'
import { VisualizedSopProcessor } from '../visualized_sop_processor'
import { VisualizedSopTasks } from '../visualized_sop_task_starter'

describe('测试sop', () => {

  const chatId = '7881302146051227_1688858254705213'
  const userId = getUserId(chatId)
  const botId = getBotId(chatId)
  test('测试清空sop', async() => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688857404698934')
    await VisualizedSopTasks.clearSop('7881302146051227_1688857404698934')
  }, 6000000)

  test('测试sop', async() => {
    Config.setting.wechatConfig = await loadConfigByWxId(botId)
    await ChatStatStoreManager.initState(chatId)
    await new MoerVisualizedSopProcessor().handleSopBySopId(chatId, userId, '685113ec85ac51120f6990a8')
  }, 60000000)
})