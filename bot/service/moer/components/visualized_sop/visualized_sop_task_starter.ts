import { Queue } from 'bullmq'
import { getVisualizedRedisSopKey, ITask, SopScheduleTime, TimeAnchorType } from './visualized_sop_type'
import { Config } from '../../../../config/config'
import logger from '../../../../model/logger/logger'
import { RedisDB } from '../../../../model/redis/redis'
import { RedisCacheDB } from '../../../../model/redis/redis_cache'
import { ScheduleTask } from '../schedule/schedule'
import { sop } from '@prisma/client'
import { getBotId } from '../../../../config/chat_id'

export async function visualizedSopStartTasks(userId: string, chatId: string, calTaskTime:(time: SopScheduleTime, chat_id: string)=> Promise<Date|undefined>) {
  const tasks = await VisualizedSopTasks.getTaskList(userId, chatId, Config.setting.wechatConfig!.id)
  // 使用Promise.all进行并发操作
  await Promise.all(
    tasks.map(async (task) => {
      task.sendTime = await calTaskTime(
        task.scheduleTime,
        task.chatId,
      )
    })
  )

  if (!Config.setting.wechatConfig?.id) {
    logger.error('没有找到wechatConfig.id')
    return
  }
  await ScheduleTask.addTasks(getVisualizedSopQueueName(getBotId(chatId)), tasks)
}

export class VisualizedSopTasks {
  static async getTaskList(userId: string, chatId: string, botId: string): Promise<ITask[]> {
    const sops = await this.getTasks(botId)
    return sops.map((sop) => {
      return {
        name: sop.id,
        chatId,
        userId,
        scheduleTime: {
          day: sop.day,
          week: sop.week,
          time: sop.time,
          timeAnchor:sop.time_anchor as TimeAnchorType
        },
      }
    })
  }

  static async getTasks(botId:string) {
    const redisClient = RedisDB.getInstance()
    const redisResult = await redisClient.get(getVisualizedRedisSopKey(botId))
    if (!redisResult) {
      logger.error('redis查询失败')
      return []
    }
    return JSON.parse(redisResult) as sop[]
  }

  static async clearSop(chatId:string) {
    const redisClient = RedisCacheDB.getInstance()
    const queue = new Queue<ITask>(
      getVisualizedSopQueueName(Config.setting.wechatConfig!.id),
      {
        connection: redisClient,
      }
    )
    const data = (await queue.getJobs()).filter((item) => item.data.chatId == chatId)
    for (const sop of data) {
      await sop.remove()
    }
  }
}

export function getVisualizedSopQueueName(botId:string): string {
  return `moer-sop-${botId}`
}