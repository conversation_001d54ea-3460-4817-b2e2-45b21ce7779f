import { getPreCourseDayScript, IPreCourseDayScript } from './pre_course_day_script'
import { getCourseDayScript, ICourseDayScript } from './course_day_script'
import { ISendMedia } from '../schedule/type'
import { getThirtyDayScript, ThirtyDayScript } from './thirty_day_script'

export interface ISendWrapper<T> {
  description: string
  content: T
}

export type ScriptFunction = (...args: any[]) => Promise<string>
export type ScriptMediaFunction = (...args: any[]) => Promise<ISendMedia>

interface IScript {
  pre_course_day: IPreCourseDayScript
  course_day: ICourseDayScript
  thirty_day: ThirtyDayScript
}


export function getScript(): IScript {
  return {
    pre_course_day: getPreCourseDayScript(),
    course_day: getCourseDayScript(),
    thirty_day: getThirtyDayScript()
  }
}