import { FlowTask } from './silent_requestion'
import { TaskName } from '../flow/schedule/type'
import { getUserId } from '../../../../config/chat_id'
import { DataService } from '../../getter/getData'
import { ChatStateStore, ChatStatStoreManager, WealthOrchardStore } from '../../storage/chat_state_store'
import { MoerNode } from '../flow/nodes/type'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { NewCourseUser } from '../flow/helper/newCourseUser'
import { AsyncLock } from '../../../../lib/lock/lock'
import { MessageSender } from '../message/message_send'
import logger from '../../../../model/logger/logger'
import { SendEnergyTest } from '../flow/schedule/task/sendEnergyTest'
import { LogOutNotification } from '../../../../../bot_starter/handler/moer_event'
import { IScheduleTime } from './creat_schedule_task'
import { GroupSend } from '../flow/schedule/task/groupSend'
import { getScript } from '../script/script'
import { TaskManager } from '../planner/task/task_manager'
import { Planner } from '../planner'
import { BigPlanner } from '../planner/daily_plan/big_planner'

export enum FlowTaskType {
  PhoneBindCheck = '检查手机号绑定情况',
  SilenceDetection = '沉默检测',
  LogOutNotification = '掉线通知',
  FormIntentionQuery = '表单挖需',
  FormIntentionQueryReminder = '表单挖需提醒',
  EnergyTestFollowUp = '能量测评后续发送APP下载',
  SendEnergyTest = '补发能量测评',
  WealthOrchardCleanup = '财富果园消息清理',
  BigPlan = '每日规划'
}


/**
 * 注册所有 SilentReAsk 任务
 */
export function registerSilentReAskTasks(): void {
  FlowTask.registerTask(FlowTaskType.BigPlan, async (chat_id: string) => {
    await ChatStatStoreManager.initState(chat_id)

    // 如果是非上课周，且先导课 和 能量测评 已完成，不再触发规划
    const currentTime = await DataService.getCurrentTime(chat_id)
    if (!currentTime.is_course_week && !currentTime.post_course_week) {
      const isCompletePreCourse = await DataService.isCompletedCourse(chat_id, { day: 0 })
      const isCompleteEnergyTest = ChatStateStore.getFlags(chat_id).is_complete_energy_test
      if (isCompletePreCourse && isCompleteEnergyTest) {
        return
      }
    }

    await BigPlanner.plan(chat_id)
  })

  // 注册手机号检查任务
  FlowTask.registerTask(FlowTaskType.PhoneBindCheck, async (chat_id: string) => {
    const userId = getUserId(chat_id)

    const phoneNumber = await DataService.findPhoneNumber(userId)
    ChatStateStore.update(chat_id, {
      nextStage: MoerNode.IntentionQuery
    })
    if (!phoneNumber) {
      await HumanTransfer.transfer(chat_id, userId, HumanTransferType.NotBindPhone, 'onlyNotify')
      // 没回手机号，继续走挖需
      await NewCourseUser.formIntentionQuery(chat_id, userId)
    }
  })

  // 注册沉默检测任务
  FlowTask.registerTask(FlowTaskType.SilenceDetection, async (chat_id: string) => {
    // 检查是否有 planner
    const currentTasks = await TaskManager.getFlexibleActiveTasks(chat_id)
    if (currentTasks.length) {
      const scheduledTasks = await Planner.taskSchedule(chat_id, currentTasks)
      // 合并需要立即处理的消息，直接处理
      await Planner.executeImmediateTask(chat_id, scheduledTasks)

      // 需要延后处理的消息
      await Planner.addDelayedTask(chat_id, scheduledTasks.filter((item) => item.scheduled_time !== 'now').map((item) => item.task_id))
    }
  })

  // 注册掉线通知任务
  FlowTask.registerTask(FlowTaskType.LogOutNotification, async (chat_id: string, params: { userId: string, currentTime: IScheduleTime, isInLiveStreamPattern: string, isNotified: string }) => {
    const { userId, currentTime, isInLiveStreamPattern, isNotified } = params
    // 加锁来判断
    const lock = new AsyncLock()
    await lock.acquire(`groupMessage_${chat_id}`, async () => {
      if (ChatStateStore.getFlags(chat_id)[isNotified]) { // 只发一次通知
        return
      }

      // 如果还在线，退出
      if (ChatStateStore.getFlags(chat_id)[isInLiveStreamPattern]) {
        return
      }

      // AI 通知
      await LogOutNotification.notify(chat_id, userId, currentTime)

      ChatStateStore.update(chat_id, {
        state: {
          [isNotified]: true
        }
      })
    })
  })

  // 注册挖需任务
  FlowTask.registerTask(FlowTaskType.FormIntentionQuery, async (chat_id: string, params: { userId: string, phoneNumber: string }) => {
    const { userId, phoneNumber } = params
    await NewCourseUser.formIntentionQuery(chat_id, userId)
    // 更新获课渠道信息
    await NewCourseUser.updateUserSource(chat_id, userId, phoneNumber)
  })

  // 注册挖需提醒任务
  FlowTask.registerTask(FlowTaskType.FormIntentionQueryReminder, async (chat_id: string, params: { userId: string }) => {
    const { userId } = params
    await MessageSender.sendById({
      user_id: userId,
      chat_id: chat_id,
      ai_msg: '在忙吗？方便的时候您回复下老师上面的问卷哈，帮助唐宁老师更了解你们哈',
    })
  })

  // 注册能量测评后续任务
  FlowTask.registerTask(FlowTaskType.EnergyTestFollowUp, async (chat_id: string, params: { userId: string }) => {
    const { userId } = params
    const lock = new AsyncLock() // 等待主线回复完再执行

    await lock.acquire(chat_id, async () => {
      const day0Scripts = getScript().pre_course_day

      await new GroupSend().sendMsg(userId, chat_id, [day0Scripts.complete_energy_test_1.content, day0Scripts.complete_energy_test_2.content, day0Scripts.complete_energy_test_3, day0Scripts.app_download_remind.content], day0Scripts.complete_energy_test_1.description)
    }, { timeout: 3 * 60 * 1000 })
  })

  // 注册财富果园清理任务
  FlowTask.registerTask(FlowTaskType.WealthOrchardCleanup, async (chat_id: string) => {
    WealthOrchardStore.clearUserMessages(chat_id)
  })

  // 注册延迟发送能量测评任务
  FlowTask.registerTask(FlowTaskType.SendEnergyTest, async (chat_id: string, params: { user_id: string }) => {
    const { user_id } = params

    if (ChatStateStore.getFlags(chat_id).is_delayed_send_energy_test) {
      return
    }

    ChatStateStore.update(chat_id, {
      state: {
        is_delayed_send_energy_test: true,
      }
    })

    const lock = new AsyncLock()
    await lock.acquire(chat_id, async () => {
      await new SendEnergyTest().process({
        name: TaskName.SendEnergyTest,
        chatId: chat_id,
        userId: user_id,
      })
    }, { timeout: 3 * 60 * 1000 })

    await DataService.saveChat(chat_id, user_id)
  })

  logger.log('SilentReAsk tasks registered successfully')
}

/**
 * 启动 SilentReAsk 系统
 */
export function startSilentReAskSystem(): void {
  // 启动工作进程
  FlowTask.startWorker()

  // 注册所有任务
  registerSilentReAskTasks()

  logger.log('SilentReAsk worker started successfully')
}
