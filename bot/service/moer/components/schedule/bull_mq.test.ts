import { Job, Queue, Worker } from 'bullmq'
import { sleep } from '../../../../lib/schedule/schedule'
import { RedisDB } from '../../../../model/redis/redis'
import { calTaskTime, taskTimeToCourseTime } from './creat_schedule_task'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const queue = new Queue('1', {
      connection: RedisDB.getInstance()
    })
    queue.add('fku', { a: 1 })
    queue.add('fku', { a: 2 })

    const queue1 = new Queue('1', {
      connection: RedisDB.getInstance()
    })
    queue1.add('fku', { a: 1 })
    queue1.add('fku', { a: 2 })

    const worker = new Worker('1', async (job: Job) => {
      console.log(JSON.stringify(job, null, 4))

      // Do something with job
      return 'some value'
    }, {
      connection: RedisDB.getInstance()
    })

    await sleep(3000)
  }, 60000)

  it('get time', async () => {
    const time = await calTaskTime({
      is_course_week: true,
      day: 6,
      time: '08:00:00'
    }, 'xx')

    console.log(JSON.stringify(await taskTimeToCourseTime(time, '7881302298050442_1688858254705213'), null, 4))
  }, 60000)
})