import { Queue } from 'bullmq'
import { RedisDB } from '../../../../model/redis/redis'
import { ITask } from './type'
import logger from '../../../../model/logger/logger'


export class ScheduleTask {
  private static queues: Map<string, Queue> = new Map()

  private static getDelayedTime(targetTime: Date, taskName: string) {
    const now = new Date()

    const delay = Number(targetTime) - Number(now)

    const totalSeconds = Math.floor(delay / 1000)
    const days = Math.floor(totalSeconds / 86400)
    let remainingSeconds = totalSeconds % 86400
    const hours = Math.floor(remainingSeconds / 3600)
    remainingSeconds %= 3600
    const minutes = Math.floor(remainingSeconds / 60)

    const timeParts: string[] = []
    if (days > 0) {
      timeParts.push(`${days}天`)
    }
    if (hours > 0) {
      timeParts.push(`${hours}小时`)
    }
    if (minutes > 0) {
      timeParts.push(`${minutes}分钟`)
    }

    if (timeParts.length === 0) {
      timeParts.push('不到1分钟')
    }

    if (delay < 0) {
      // logger.warn('Target time is in the past')
    } else {
      console.log(`${taskName} in ${timeParts.join(' ')}`)
    }

    return delay
  }

  /**
   * 添加一个指定时间的任务
   * @param task
   */
  public static taskToJob(task: ITask) {
    const sendTime = task.sendTime

    const targetTime = new Date(sendTime?.toISOString() as string)
    const delay =  this.getDelayedTime(targetTime, task.name)

    return {
      name: task.name,
      data: task,
      opts: { delay }
    }
  }

  static getQueue(queueName: string) {
    let queue = this.queues.get(queueName)
    if (!queue) {
      queue = new Queue(queueName, {
        connection: RedisDB.getInstance()
      })
      this.queues.set(queueName, queue)
    }

    return this.queues.get(queueName) as Queue
  }

  static async addTasks(queueName: string, tasks: ITask[]) {
    // 获取 Queue 实例
    const queue = this.getQueue(queueName)

    let jobs = tasks.map((task) => this.taskToJob(task))
    jobs = jobs.filter((job) => job.opts.delay >= 0)

    try {
      await queue.addBulk(jobs)
    } catch (e) {
      logger.error('添加任务失败', e)
    }
  }


  public static async addTask(queueName: string, taskName: string, date: Date, data: any) {
    const queue = this.getQueue(queueName)

    await queue.add(taskName, data, {
      delay: this.getDelayedTime(date, taskName)
    })
  }
}
