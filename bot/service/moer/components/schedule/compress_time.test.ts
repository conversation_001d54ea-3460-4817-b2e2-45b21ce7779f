import { ITask } from './type'
import { ScheduleTask } from './schedule'
import { calTaskTime, IScheduleTime } from './creat_schedule_task'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const tasks: ITask[] = [
      {
        name: 'fk',
        chatId: '661ce59f5ed60835eb1343ff',
        userId: '1213',
        scheduleTime: {
          post_course_week: 1,
          day: 1,
          time: '10:00:00',
        }
      }
    ]

    for (const task of tasks) {
      task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId, true)

      console.log(task.sendTime.toLocaleString())
    }

    console.log(tasks.map((task) => ScheduleTask.taskToJob(task)))
  }, 60000)
})