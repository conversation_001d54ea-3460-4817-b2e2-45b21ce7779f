import { IWecomMessage } from '../../../../lib/juzi/type'
import { IScheduleTime } from './creat_schedule_task'

export interface ISendMedia {
   description: string // 描述
   msg: IWecomMessage // 消息
}

export interface IScheduleTask {
    sendTime?: Date // 发送时间
    reScheduleSendTime?: Date // 重新规划的发送时间

    scheduleTime?: IScheduleTime

    tag?: string // 标签
    [key: string]: any
}

export interface ITask extends IScheduleTask, IBaseInfo{

}

export interface IBaseInfo {
    name: string // 任务名
    chatId: string
    userId: string
}