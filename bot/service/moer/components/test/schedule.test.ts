import { FlowTask } from '../schedule/silent_requestion'
import { UUID } from '../../../../lib/uuid/uuid'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { sleep } from '../../../../lib/schedule/schedule'
import * as MockDate from 'mockdate'

describe('Test', function () {
  beforeAll(() => {

  })

  it('danmu', async () => {
    const qas = [{ question: '已拍', answers: ['已拍了'] }]

    if (qas.some((qa) => qa.answers.some((answer) => ['想学', '想买', '想了解', '已拍', '已买'].includes(answer)))) {
      console.log('here')
    }
  }, 60000)

  it('should pass', async () => {
    const chat_id = UUID.short()

    // Register a test task
    FlowTask.registerTask('test_task', async (chat_id: string) => {
      console.log('fk')
    })

    await FlowTask.schedule(
      'test_task',
      chat_id,
      1000,
      undefined,
      {
        auto_retry: true,
        independent: true
      }
    )

    // 插一条消息
    await PrismaMongoClient.getInstance().chat_history.create({
      data: {
        chat_id: chat_id,
        role: 'user',
        content: '1',
        created_at: new Date(),
      }
    })

    await sleep(5000)
  }, 60000)

  it('sinon', async () => {
    // 设置全局时间为指定日期
    MockDate.set('2023-10-12')

    // 现在 new Date() 将返回你指定的日期
    console.log(new Date()) // 输出 2023-10-12T00:00:00.000Z
  }, 60000)
})