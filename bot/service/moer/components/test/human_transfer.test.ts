import { Config } from '../../../../config/config'
import { ChatHistoryService } from '../chat_history/chat_history'
import { DataService } from '../../getter/getData'

interface Conversation {
  A: string[]
  B: string[]
}

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '暴叔',
      notifyGroupId: 'R:10735753744477170',
      classGroupId: 'x',
      courseNo: 1
    }
  })

  it('分析类', async () => {
    const text = `同学可以分享自己财富果园的画面，发在【班级群】里，因为每个人都不一样，后续我会逐一解读哦。

比如👉：
大门新旧、材质、颜色：
有无围栏：
主树是什么：
周围的树是什么：
秋天动作、有无变现：
呼吸顺畅吗：
四季循环变化怎么样：`

    console.log(text.replace(/[\s\S]*?(比如👉：)/, ''))
  }, 60000)

  it('标签更新', async () => {
    Config.setting.wechatConfig =   {
      id: '1688857949631398',
      botUserId: 'MaiZi',
      name: '暴叔',
      notifyGroupId: 'R:10735753744477170',
      classGroupId: 'x',
      courseNo: 1
    }

    await DataService.updateTags('7881300846030208', '48期')
  }, 60000)

  it('测试', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getRecentConversations('7881301047907394_1688854546332791', 3), null, 4))
  }, 60000)
})