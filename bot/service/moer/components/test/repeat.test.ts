import { ChatHistoryService } from '../chat_history/chat_history'

describe('Test', function () {
  beforeAll(() => {

  })

  it('重复度测试', async () => {
    console.log(await ChatHistoryService.isRepeatedMsg('7881301063926546_1688856322643146', '先看小讲堂，里面有一次初步体验的海浪冥想，对咱们情绪减压和睡眠帮助很大，也能感受老师带练的风格呢～明晚正式开课，期待你的参与🌟'))
  }, 60000)

  it('should pass', async () => {
    console.log(await ChatHistoryService.isRepeatedMsg('7881302298050442_1688854546332791', '唐宁老师姓唐哦'))
  }, 60000)

  it('a', async () => {
    const a = ['1', '2', 3, 4, 5, 6, 7, 8, 9, 0, 10]
    console.log(a.slice(-10))
  }, 60000)
})