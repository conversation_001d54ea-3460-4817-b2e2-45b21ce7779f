import { ChatHistoryService } from '../chat_history/chat_history'
import { DataService } from '../../getter/getData'
import { ClassGroupSend } from '../flow/schedule/task/classGroupSend'
import { ClassTaskName } from '../flow/schedule/type'
import { UUID } from '../../../../lib/uuid/uuid'
import { Config } from '../../../../config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('1231', async () => {
    Config.setting.localTest = false

    new ClassGroupSend().process({
      chatId: UUID.short(),
      userId: UUID.short(),
      name: ClassTaskName.ClassMeetingDay5,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:00:00'
      }
    })
  }, 60000)

  it('should pass', async () => {
    const recentConversations = await ChatHistoryService.getRecentConversations('7881301181141895_1688857003605938', 3)
    const chat_history = ChatHistoryService.formatHistoryHelper(recentConversations)

    console.log(chat_history)
  }, 60000)

  it('完课', async () => {
    const chat = await DataService.getChatByWechatName('郭小琴')
    const user = chat[0] as any

    console.log(await DataService.isCompletedCourse(user._id, { day: 2 }))
  }, 60000)
})