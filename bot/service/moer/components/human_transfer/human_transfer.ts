import { Chat, ChatDB } from '../../database/chat'
import { GroupNotification } from '../../notification/group'
import { ObjectUtil } from '../../../../lib/object'
import { JuziAPI } from '../../../../lib/juzi/api'
import { Config } from '../../../../config/config'
import { ChatStateStore } from '../../storage/chat_state_store'
import { EventTracker, IEventType } from '../../../../model/logger/data_driven'
import logger from '../../../../model/logger/logger'

interface UpdateAliasParams {
  userId: string
  alias: string
  leading?: boolean
  replace?: RegExp // 用于替换原有备注
}

export enum HumanTransferType {
  UnknownMessageType = 1,
  NotBindPhone = 2,
  JoinedGroup = 3,
  FailedToJoinGroup = 4,
  ProcessImage = 5,
  MessageSendFailed = 6,
  ConfirmedAddress = 7,
  HesitatePayment = 8,
  LogOutNotify = 9,
  RefundCourse = 10,
  SendMiniProgram = 11,
  SoftwareIssue = 12,
  RobotDetected = 13,
  PaidCourse = 14,
  RefusePurchase = 15,
  WeeklyCard = 16,
  RepeatedWealthOrchardAnalyze = 17,
  ProblemSolving = 18,
  Course188Consulting = 21,
  ExplicitlyPurchases = 23
}

export class HumanTransfer {
  /**
 * 转交人工，toBot为true时，表示转交机器人
 * @param chatId
 * @param userId
 * @param type
 * @param toHuman
 * @param additionMsg 会拼接到转人工消息后面的额外信息，比如: A 客户说了 xxx(additionMsg)
  */
  public static async transfer(chatId: string, userId: string, type: HumanTransferType, toHuman: boolean |'onlyNotify' = true, additionMsg?: string) {
    if (type !== HumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, type) })
    }

    if (typeof toHuman === 'boolean' && await ChatDB.getById(chatId)) {
      await ChatDB.setHumanInvolvement(chatId, toHuman)
    } else {
      if (! await ChatDB.getById(chatId)) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await ChatDB.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: ChatStateStore.get(chatId)
        })
      }
    }

    // 包装通知
    const chat = await ChatDB.getById(chatId) as Chat
    let contactName = userId
    if (chat && chat.contact) {
      contactName = chat.contact.wx_name
    }

    // 通知类型
    const handleType = toHuman === true ? '，请人工处理\n[心碎]AI已关闭[心碎]' : '，请观察👀'
    const notificationMessages = {
      [HumanTransferType.UnknownMessageType]: '客户发了一个文件',
      [HumanTransferType.ProcessImage]: '客户发了一张【图片】',
      [HumanTransferType.NotBindPhone]: '客户手机号绑定失败',
      [HumanTransferType.SoftwareIssue]: '客户软件或者课程链接出问题',
      [HumanTransferType.MessageSendFailed]: '消息发送失败',
      [HumanTransferType.RobotDetected]: '客户识别到了AI',
      [HumanTransferType.ConfirmedAddress]: '客户支付后已发送地址',
      [HumanTransferType.HesitatePayment] : '客户支付失败',
      [HumanTransferType.LogOutNotify]: '客户直播掉线',
      [HumanTransferType.RefundCourse]: '客户之前购买过课程',
      [HumanTransferType.SendMiniProgram]: '已发送小程序给客户',
      [HumanTransferType.PaidCourse]: '[烟花]客户已支付[烟花]',
      [HumanTransferType.RefusePurchase]: '客户拒绝购买',
      [HumanTransferType.WeeklyCard]: '客户索要周卡',
      [HumanTransferType.RepeatedWealthOrchardAnalyze]: '客户已经分析过财富果园',
      [HumanTransferType.ProblemSolving]: '客户遇到问题',
      [HumanTransferType.Course188Consulting]: '客户回复了188课程',
      [HumanTransferType.FailedToJoinGroup]: '客户拉群失败',
      [HumanTransferType.ExplicitlyPurchases]: '客户准备购买课程'
    }

    let message = (notificationMessages[type] || type) + handleType
    message = additionMsg ? `${contactName} ${message}\n${additionMsg}` : `${contactName} ${message}`

    if (toHuman === 'onlyNotify' && Config.setting.localTest) { return }
    logger.log(chatId, '通知人工接入：', message)

    await GroupNotification.notify(message) // 顾问群
  }
}