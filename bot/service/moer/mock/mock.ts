import { faker } from '@faker-js/faker'
import { RandomHelper } from '../../../lib/random/random'
import { IWorkflowState } from '../components/flow/flow'
import { ChatInterruptHandler } from '../components/message/interrupt_handler'
import { ChatHistoryService } from '../components/chat_history/chat_history'
import axios from 'axios'
import { ChatDB } from '../database/chat'

interface Lesson {
    id: string
    name: string
    index: number
    address: string
    is_recording: boolean
}

interface MoerLesson {
    id: string
    updated_at: Date
    lesson_no: number
    startTime: Date
    lessons: Lesson[]
}

/**
 * 由于 API 需要时间对接，所以暂时使用 mock 数据， 注意 API 接入后，所有 Mock 数据和函数要使用真实 API 实现
 */
export class MockData {

  public static get courseInfo() { // 当前期数
    const lessons: Lesson[] = []
    lessons.push({
      id: faker.string.uuid(),
      name: `Lesson 0 - ${faker.lorem.words(3)}`,
      index: 0,
      address: faker.internet.url(),
      is_recording: true,
    })

    // channel Id

    return {
      id: faker.string.uuid(),
      updated_at: faker.date.recent(),
      lesson_no: 30,
      start_at: faker.date.soon({ days: 14 }),
      lessons: lessons,
    }
  }

  public static async getPhoneNumberByUserId(userId: string) {
    return faker.phone.number()
  }

  /**
   * 获取订单
   */
  public static async getOrderByPhoneNumber(phoneNumber: string) {
    return RandomHelper.select([{
      order_no: faker.string.uuid(),
      order_status: 'paid'
    }, null])
  }

  public static async isCompletedPreCourse(chatId: string) {
    return true
  }

  public static async isCompletedCourse(chatId: string) {
    return RandomHelper.select([true, false])
  }

  public static async getState(userMessage: string): Promise<IWorkflowState> {
    const chat_id =  faker.string.uuid()
    await ChatHistoryService.addUserMessage(chat_id, userMessage)

    return {
      chat_id,
      user_id: faker.string.uuid(),
      round_id: faker.string.uuid(),
      userMessage: userMessage,
      interruptHandler: await ChatInterruptHandler.create(chat_id)
    }
  }

  static async findUserByPhoneNumber(phoneNumber: string) {
    return RandomHelper.select<string | null>(['1', null])
  }

  static getEnergyTestScore(chatId: string) {
    //返回 -60 到 504 之间的随机数
    return faker.number.int({ min: -60, max: 504 })
  }

  static async completePreCourse(moerId: string) {
    await axios.post('http://localhost:3001/moer/event', {
      userId: moerId,
      event: 'course_study_guide'
    })
  }

  static async completeEnergyTest(chat_id: string) {
    const chat = await ChatDB.getById(chat_id)
    if (!chat || !chat.moer_id) {
      return
    }

    await axios.post('http://localhost:3001/moer/event', {
      userId: chat.moer_id,
      event: 'complete_energy_test',
    })
  }

  static getLiveChat(chat_id: string) {
    return faker.lorem.sentence()
  }

  static getRelevanceContext(userMessage: string) {
    return ''
  }

  static getCurrentDay() {
    return RandomHelper.select([ 4, 5, 6, 7])
  }

  static isInClass(userId: string) {
    return RandomHelper.select([true, false])
  }

  static getCurrentCourseNo() {
    return [35, 36, 37]
  }

  static async getCourseInfoByCourseNo(courseNo: number) {
    if (courseNo === 36) {
      return {
        'code': 0,
        'msg': '操作成功',
        'data': {
          'courseNo': '36',
          'sku': '20240830009212',
          'name': '【10.7开营】5天冥想入门营',
          'startTime': '2024-10-07 20:00:00',
          'resource': [
            {
              'vodId': 7310,
              'liveId': 0,
              'day': 0,
              'shortUrl': 'https://t.meihao.com/ZlGO',
              'resourceName': '小讲堂 冥想急救：1招快速放松精神和身体',
              'resourceType': 2
            },
            {
              'vodId': 7336,
              'liveId': 5213062,
              'day': 1,
              'shortUrl': 'https://t.meihao.com/T7vl',
              'resourceName': '第1节 情绪减压：放松心身 从根源释放压力',
              'resourceType': 2
            },
            {
              'vodId': 7364,
              'liveId': 5222031,
              'day': 2,
              'shortUrl': 'https://t.meihao.com/VZ2J',
              'resourceName': '第2节 财富唤醒：突破金钱卡点 唤醒财富潜力',
              'resourceType': 2
            },
            {
              'vodId': 0,
              'liveId': 5222043,
              'day': 3,
              'shortUrl': 'https://t.meihao.com/7bJK',
              'resourceName': '第3节 效能提升：《红靴子》冥想 提高精力 高效做事',
              'resourceType': 1
            }
          ]
        }
      }
    } else {
      return {
        'code': 0,
        'msg': '操作成功',
        'data': {
          'courseNo': '37',
          'sku': '20240830009212',
          'name': '【10.14开营】5天冥想入门营',
          'startTime': '2024-10-14 20:00:00',
          'resource': [
            {
              'vodId': 7310,
              'liveId': 0,
              'day': 0,
              'shortUrl': 'https://t.meihao.com/ZlGO',
              'resourceName': '小讲堂 冥想急救：1招快速放松精神和身体',
              'resourceType': 2
            },
            {
              'vodId': 7336,
              'liveId': 5213062,
              'day': 1,
              'shortUrl': 'https://t.meihao.com/T7vl',
              'resourceName': '第1节 情绪减压：放松心身 从根源释放压力',
              'resourceType': 2
            },
            {
              'vodId': 7364,
              'liveId': 5222031,
              'day': 2,
              'shortUrl': 'https://t.meihao.com/VZ2J',
              'resourceName': '第2节 财富唤醒：突破金钱卡点 唤醒财富潜力',
              'resourceType': 2
            },
            {
              'vodId': 0,
              'liveId': 5222043,
              'day': 3,
              'shortUrl': 'https://t.meihao.com/7bJK',
              'resourceName': '第3节 效能提升：《红靴子》冥想 提高精力 高效做事',
              'resourceType': 1
            }
          ]
        }
      }
    }
  }
}