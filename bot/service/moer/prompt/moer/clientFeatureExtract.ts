import { PromptTemplate } from '@langchain/core/prompts'
import { MemoryStore } from '../../components/memory/memory_store'

export class ClientFeatureExtractPrompt {
  public static async format(chat_id: string) {
    const previousMemory = await MemoryStore.getMemoriesByChatId(MemoryStore.indexName, chat_id)
    const memoryString = previousMemory.join(' ')
    return PromptTemplate.fromTemplate(`请仔细分析以下新客户的聊天历史总结，并从给定的特征库中提取最相关的特征。只选择语义上最接近的特征，如果找不到相似的特征则忽略。请使用<features></features>标签列出所有匹配的特征，并放入<query></query>中。

特征库：[桀骜叛逆,自我否定,对生活缺乏热情,生活作息紊乱,缺乏自制力,孤独感强烈,情感认知模糊,人际关系疏离,对父母关系复杂,冥想练习困难,迷茫,对未来恐惧,自我认知为受害者,事业不顺,缺乏人生方向,对个人成长有渴望,开放尝试新事物,寻求精神指引,经历人生低谷,缺乏内在力量感,敏感,焦虑,思维固化,自卑,自责,爱抱怨,被念头支配,情绪化,家庭关系紧张,抑郁倾向,人生缺乏规划,随波逐流,忽视健康,工作态度消极,消费观念不当,控制欲强,情绪波动大,缺乏耐心,职场压力大,情绪压抑,睡眠质量差,亲密关系紧张,自我提升欲望强,开放学习新事物,缺乏内心平静,易受外界影响,对自身发展有追求,过分在意外界评价,承担他人责任,自我价值感低,缺乏商业意识,自我设限,缺乏突破勇气,服务意识强但执行力弱,情执重,忽视自我,忧虑,恐惧,身体健康问题,过度关注他人,潜在焦虑,压力感强,强迫性行为倾向,追求外在认可,工作狂倾向,现实逃避倾向,对生命意义困惑,事业受挫,失眠,身体亚健康,情感迷茫,生活态度消极,工作压力大,身心透支,缺乏自我调节能力,对未来缺乏信心,体型肥胖,皮肤问题,精神寄托缺失,自信心低,精力不足,寻求精神成长,缺乏合适的指导,学习能力受限,生活缺乏意义感,渴望自我提升,经济条件优越,事业成功,亲密关系和谐,追求自我认知,生活方式需要调整,开放接受新知识,重视夫妻共同成长,寻求生命意义,愿意改变生活习惯,对个人成长有强烈兴趣,自卑感强,亲子关系困难,子女教育问题,婚姻不和谐,情绪管理能力差,人际交往困难,缺乏积极生活态度,对灵性成长有强烈渴望,决策果断,直觉敏锐,对导师有强烈信任,行动力强,有组织能力,愿意突破自我,面临生活挑战,追求生命意义,对群体活动有热情,高学历高收入,管理经验丰富,持续学习意愿强,寻求智慧困惑,决心坚定,面临团队管理挑战,处理情绪问题困难,对个人成长投入大,年龄较大,追求职业突破,职业成就高,重度抑郁,情感连接困难,性格强势,身心不协调,自我消耗严重,寻求身心疗愈,缺乏爱的能力,渴望改变,内心不满足,生活缺乏真实快乐,过度理性,童年缺爱,自我封闭,人生方向迷茫,对改变有渴望,高级管理经验,自我反思能力强,追求个人成长,愿意投入时间和精力,对体验式学习感兴趣,具有"空杯心态",重视自我提升,寻求多元化成长途径,脾气暴躁,工作效率低,创造力受限,能够快速应用所学,长期抑郁,自杀倾向,对未来绝望,职业不满,社交恐惧,情绪失控,注意力分散,情绪易激动,自我否定倾向,有坚持学习的决心]

新客户聊天历史总结：{memoryString}

请按以下格式输出结果：
<query>
<features>特征1, 特征2, 特征3...</features>
</query>

请确保只选择与新客户描述最相关的特征，并提供简洁的解释。如果某些描述无法在特征库中找到匹配项，请忽略这些内容。所有特征应该用<feature></feature>标签包围，并放入<query></query>中。
提取出的features:`).format({
      memoryString: memoryString
    })
  }
}