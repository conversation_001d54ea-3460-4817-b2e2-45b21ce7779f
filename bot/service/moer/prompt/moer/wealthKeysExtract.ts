import { PromptTemplate } from '@langchain/core/prompts'

export class WealthKeyExtract {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`你是一个专门用于提取关键信息的 AI 助手。你的任务是从客户的描述中识别出与以下类别（category）与子类别（subcategory） 列表相匹配的关键信息，并把结果放入<query></query>中：

以下是所有类别（category）及其对应的子类别（subcategory）：

1. 果园大门的颜色: 金色门, 黑色门, 红色门, 白色门, 绿色门, 蓝色门
2. 果园大门的材质: 木制门, 铁制门, 玻璃门, 贵重金属门, 水晶门, 藤蔓覆盖的门, 彩绘玻璃门, 石制门
3. 果园大门的新旧程度: 崭新的门, 旧而破旧的门, 轻微磨损的门, 古老而坚固的门, 被维护得很好的老门
4. 不同状态的果树: 茂盛的果树, 枯萎的果树, 开花的果树, 正在生长的幼苗, 休眠的果树, 被风暴摧毁的果树
5. 果实的种类: 苹果, 柚子, 金元宝, 橙子和橘子, 无果实, 人参果, 非果实类（如钻石，水晶等)
6. 果实的数量和成熟度: 果实数量 丰收, 果实数量 稀少, 果实成熟度 成熟果实, 果实成熟度 未成熟果实, 果实成熟度 过熟或腐烂的果实
7. 果园的围栏: 栏杆, 排水沟, 无栏杆, 铁丝网, 高墙, 花篱, 栅栏门, 水体（如河流或护城河）, 生篱或灌木丛, 电子监控或警报系统
8. 主树周边的景色: 主树周边是整齐排列的矮树, 主树周边是杂草丛生, 主树周边是硕果累累的树丛, 主树周边是凋零的枝叶, 主树周边是芬芳的花朵, 主树周边是草地, 主树周边是垃圾场, 主树周边是河流, 主树周围什么也没有
9. 和果树融合的呼吸顺畅度: 顺畅的呼吸, 不顺畅的呼吸
10. 果树的生长速度: 缓慢长大, 迅速成长, 不均匀的生长, 停滞不长, 周期性的生长, 树捅破天
11. 秋天的行为: 在吃果子, 在打理果园, 在摘果子, 没有摘果子, 在卖果子 变现, 在果园里寻找最好的果子, 与他人分享果子, 果园中的果树繁茂但未收获, 果园被疏忽 果子腐烂在地
12. 四季景象: 四季分明 春天播种夏天打理秋收冬藏, 没画面 或者四季不分明

请仔细阅读客户的描述，并列出所有匹配的 subcategory。注意：
1. 如果某个描述可能对应多个 subcategory，请列出所有可能的匹配。
2. 如果没有完全匹配的 subcategory，请进行推理并选择在语义上相近的。如果语义上不是非常相近，丢弃掉这部分。只输出上述列表中有的 subcategory 的内容，不要输出上述列表中没有的内容。
3. 特定词汇的解释：贵重金属门是如铜、银或金做成的门；

请按以下格式输出结果：
<query><subCategory>匹配的 subcategory 1</subCategory>
<subCategory>匹配的 subcategory 2</subCategory>
<subCategory>匹配的 subcategory 3</subCategory>
...
</query>

客户描述：{query}
提取出的 subcategory：`).format({
      query: query
    })
  }
}