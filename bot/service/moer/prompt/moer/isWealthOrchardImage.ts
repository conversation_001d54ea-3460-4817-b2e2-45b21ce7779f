import { PromptTemplate } from '@langchain/core/prompts'

export class IsWealthOrchardImagePrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are analyzing whether a user's description refers to the "Wealth Orchard" (财富果园) visualization from a meditation exercise. Teachers guide students through this visualization exercise, and afterwards, students record what they saw in their imagination. These descriptions need to be identified correctly so teachers can provide appropriate interpretations.
    
A description is about the Wealth Orchard if it:
1. Explicitly mentions wanting an analysis of their Wealth Orchard visualization, OR
2. Describes elements typically found in the Wealth Orchard visualization, such as:
   - Trees, particularly fruit trees
   - Gates or doors
   - Fruits
   - Fences or walls
   - Seasonal changes
   - The overall garden/orchard environment

Even descriptions stating "no image" or "no clear visualization" should return true if they are clearly attempting to report on a Wealth Orchard meditation experience.

Examples that should return true:
- "No gate, small crown wood fence, main tree unclear, didn't see any fruit, evergreen all seasons"
- "Gray steel gate, no fence, many apple trees, largest fruit tree in the center, orchard had many apples, I was the only one climbing trees to pick fruit, didn't see seasonal changes, fell asleep"
- "I didn't see any visualization of the wealth orchard"
- "Please analyze my wealth orchard: large wooden gate, stone fence..."

Examples that should return false:
- "Thank you teacher"
- "When is the next class?"

Here is the user description to analyze:
<user_description>
{query}
</user_description>

Carefully examine if this description:
1. Explicitly requests analysis of a Wealth Orchard visualization, OR
2. Contains descriptions of typical Wealth Orchard elements (trees, gates, fruits, fences, seasons)

Based on your analysis, provide your determination in the following format:

<result>
true
</result>

OR

<result>
false
</result>`).format({
      query
    })
  }
}