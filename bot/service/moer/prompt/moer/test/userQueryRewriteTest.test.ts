import { <PERSON>Helper } from '../../../../../lib/file'
import { QueryRewritePrompt } from '../userQueryRewrite'
import path from 'path'
import { MoerRag } from '../../../components/rag/moer_embedding_search'


describe('客户搜索重写', () => {
  beforeAll(() => {

  })
  it('1.数据集测试', async () => {
    //读取数据集
    const fileContent =  await FileHelper.readFile(path.join(__dirname, 'dataSet/userQueryRewriteDataSet.json'))
    const dataSet = JSON.parse(fileContent)
    const testResult :any = {}
    testResult.irrelevantChatHistoryCase = []
    testResult.relevantChatHistoryCase = []
    //测试数据集
    for (const data of dataSet.irrelevantChatHistoryCase) {
      const prompt = await QueryRewritePrompt.format(data.query, data.chatHistory)
      data.actual = await MoerRag.queryReWrite(data.query, prompt)
      testResult.irrelevantChatHistoryCase.push(data)
    }
    for (const data of dataSet.relevantChatHistoryCase) {
      const prompt = await QueryRewritePrompt.format(data.query, data.chatHistory)
      data.actual = await MoerRag.queryReWrite(data.query, prompt)
      testResult.relevantChatHistoryCase.push(data)
    }
    //写入测试结果
    const filePath = path.join(__dirname, 'dataSet/userQueryRewriteTestResult.json')
    await FileHelper.writeFile(filePath, JSON.stringify(testResult, null, 2))
  }, 270000)
})