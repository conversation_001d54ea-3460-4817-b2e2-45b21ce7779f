import { LLM } from '../../../../../lib/ai/llm/LLM'
import { ExtractUserSlotsPrompt } from '../extractUserSlots'
import { ExtractAddressSlotsPrompt } from '../extractAddressSlots'
import { IsNeedEmpathyPrompt } from '../isNeedEmpathy'
import { LLMXMLHelper } from '../../../components/flow/helper/xmlHelper'
import { WealthOrchardAnalyze } from '../../../components/flow/nodes/wealthOrchard'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(await LLM.predict(await ExtractUserSlotsPrompt.format(`麦子老师：确认下每晚8点能来上课么？
客户：不一定吧，有录播么`)))
  }, 1E9)

  it('prompt', async () => {
    console.log(await LLM.predict(await ExtractAddressSlotsPrompt.format('广州市天河区，联系电话是020-12345678。')))
  }, 60000)

  it('empathy', async () => {
    console.log(await LLM.predict(await IsNeedEmpathyPrompt.format('老公打我，怎么办', '打坐垫怎么卖')))
  }, 60000)

  it('asdas', async () => {
    console.log(await LLMXMLHelper.predictAndExtractBooleanAnswer(`请判断以下客户输入是否为发送资料/完课礼/课程回放请求，如果是，请返回 "true"，否则返回 "false"。

客户输入：
<user_input>可以发一下回访吗</user_input>
    
请根据上述指令进行判断，先输出理由到 <reason></reason> 标签中，并将结果输出到 <answer></answer> 标签中 (仅包含结果)。

示例如下：
<reason>客户询问能不能获得完课礼</reason>
<answer>true</answer>`, { tagName: 'answer', trueFlag: 'true', falseFlag: 'false', model: 'gpt-4.1-mini', meta: { name: 'send_file_check', round_id:'' } }))
  }, 60000)

  it('1232', async () => {
    console.log(await WealthOrchardAnalyze.isWealthOrchardImage('没有画面'))
  }, 60000)
})