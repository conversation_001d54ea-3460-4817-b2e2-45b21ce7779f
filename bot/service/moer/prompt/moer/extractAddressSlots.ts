import { PromptTemplate } from '@langchain/core/prompts'
import { z } from 'zod'

export class ExtractAddressSlotsPrompt {
  public static schema: z.ZodType<any, any> = z.object({
    address: z.string().nullable(),
    phone: z.string().nullable().optional(),
    name: z.string().nullable().optional(),
  })

  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You are tasked with extracting specific information from a user's chat history regarding their address, name, and phone number. This information will help in contacting the user and delivering services.

Here’s how you should approach this:

1. Read the user's chat history carefully to identify key pieces of information. The chat history variable contains the user's input, and you should use it to look for the specified fields.
2. Extract information related to the following fields:
   - "name": {"type": "string", "description": "客户的姓名。Extract the user's name. It should be a string value."}
   - "phone": {"type": "string", "description": "客户的电话号码。Extract the user's phone number. It should be a string value."}
   - "address": {"type": "string", "description": "客户的地址。Extract the user's address. It should be a string value."}
3. Structure the extracted information into JSON format. Begin with a curly brace, and place each field as a key with its extracted value as the value. Separate multiple fields with commas. End with a curly brace. Ensure the JSON is correctly formatted and follows the specified keys and data types.
4. Place the JSON within <extracted_info> tags.

Below are examples to help guide you:

<Example>
<chat_history>
您好，我的名字是张伟，电话号码是13812345678，地址是北京市朝阳区。
</chat_history>

<reasoning>
识别关键信息：客户的姓名是张伟，电话号码是13812345678，地址是北京市朝阳区。
将信息映射为字段："name", "phone", "address"
</reasoning>

<extracted_info>
{"name": "张伟", "phone": "13812345678", "address": "北京市朝阳区"}
</extracted_info>
</Example>

<Example>
<chat_history>
你好，我是李娜，请问你们的产品可以送到上海吗？
</chat_history>

<reasoning>
识别关键信息：客户的姓名是李娜。没有提供电话号码和具体地址。
将信息映射为字段："name"
</reasoning>

<extracted_info>
{"name": "李娜", "phone": null, "address": null}
</extracted_info>
</Example>

<Example>
<chat_history>
我需要订购一份产品，送到广州市天河区，联系电话是020-12345678。
</chat_history>

<reasoning>
识别关键信息：客户的地址是广州市天河区，电话号码是020-12345678。没有提供姓名。
将信息映射为字段："phone", "address"
</reasoning>

<extracted_info>
{"name": null, "phone": "020-12345678", "address": "广州市天河区"}
</extracted_info>
</Example>

If the required information is not present within the chat history, append the value as null. Ensure you follow proper JSON formatting guidelines and accurately map user input to the specified fields.

Follow these steps to convert the input you receive into the appropriate JSON format.

Give your simple reasoning in <reasoning>.

Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.

<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>`,
    { templateFormat: 'mustache' }).format({
      chat_history
    })
  }
}