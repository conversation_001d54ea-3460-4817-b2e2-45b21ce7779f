import { PromptTemplate } from '@langchain/core/prompts'

export class IsNeedEmpathyPrompt {
  public static async format(customer_need: string, customer_statement: string) {
    return PromptTemplate.fromTemplate(`You are an AI assistant tasked with determining whether empathy is appropriate in a sales scenario for meditation products. Your goal is to analyze the customer's need and current statement to decide if empathizing with their situation is suitable or if you should prioritize answering their question directly.

Here is the customer's need:
<customer_need>
{customer_need}
</customer_need>

Here is the customer's current statement:
<customer_statement>
{customer_statement}
</customer_statement>

Analyze the customer's statement carefully. Consider the following guidelines:

1. If the statement is a question or observation unrelated to the customer's personal situation or meditation goals, empathy may not be appropriate.
2. If the statement is a general inquiry about meditation or the product that doesn't reveal personal information, empathy may not be necessary.
3. If the statement expresses personal challenges, emotions, or goals related to meditation, empathy may be appropriate.
4. If the statement is off-topic or unrelated to meditation or the sales context, prioritize answering their question or redirecting the conversation.

Based on these guidelines, determine whether empathy is appropriate in this situation.

Provide your reasoning and decision in the following format:
<reasoning>
[Explain your analysis of the customer's statement and how it relates to the need for empathy]
</reasoning>

<decision>[true/false]</decision>

Remember, output "true" if empathy is appropriate and should be used, and "false" if empathy is not necessary and you should prioritize answering the customer's question or addressing their statement directly.`).format({
      customer_need,
      customer_statement
    })
  }
}