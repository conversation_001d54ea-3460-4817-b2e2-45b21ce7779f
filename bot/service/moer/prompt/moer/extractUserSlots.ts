import { PromptTemplate } from '@langchain/core/prompts'
import { z } from 'zod'

export class ExtractUserSlotsPrompt {
  public static schema: z.ZodType<any, any> = z.object({
    live_class_confirmation: z.boolean().optional(),
    meditation_goal: z.string().optional()
  })

  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You are tasked with extracting specific information from a user's chat history regarding their ability to attend live classes at 8 PM and their understanding or purpose of studying meditation. This information will help in tailoring the course offerings better suited to the user's needs.

Here’s how you should approach this:

1. Read the user's chat history carefully to identify key pieces of information. The chat history variable contains the user's input, and you should use it to look for the specified fields.   
2. Extract information related to the following fields:
   - "live_class_confirmation": {"type": "boolean", "description": "确认上课期间晚上8点是不是都可以来听直播课. Extract whether the user confirms they can attend live classes at 8 PM during the course period. It should be a boolean value: true or false."}
   - "meditation_goal": {"type": "string", "description": "客户对冥想的了解，或者学习冥想的目的. Extract the user's understanding of meditation or their purpose for studying meditation. It should be a string value."}
3. Structure the extracted information into JSON format. Begin with a curly brace, and place each field as a key with its extracted value as the value. Separate multiple fields with commas. End with a curly brace. Ensure the JSON is correctly formatted and follows the specified keys and data types.
4. Place the JSON within <extracted_info> tags.

Below are examples to help guide you:

<Example>
<chat_history>
您好，我想确认一下，我晚间八点都可以参加直播课。然后我对冥想有所了解，主要是想通过冥想来减压。
</chat_history>

<reasoning>
识别关键需求：客户确认晚上八点可以上课，以及客户表示学习冥想主要是想减压。
将信息映射为字段："live_class_confirmation", "meditation_goal"
</reasoning>

<extracted_info>
{"live_class_confirmation": true, "meditation_goal": "减压"}
</extracted_info>
</Example>

<Example>
<chat_history>
你好，我想学习冥想来提高专注力。
</chat_history>

<reasoning>
识别关键需求：客户表示学习冥想的目的是提高专注力。没有关于是否能晚上八点上课的信息。
将信息映射为字段："meditation_goal"
</reasoning>

<extracted_info>
{"live_class_confirmation": null, "meditation_goal": "提高专注力"}
</extracted_info>
</Example>

<Example>
<chat_history>
客户：我不确定每天晚上八点能不能参加直播课。
我觉得冥想可以帮助我减轻压力。
</chat_history>

<reasoning>
识别关键需求：客户不确定是否能参加晚上八点的直播课, 客户表示学习冥想是为了减轻压力。
将信息映射为字段："live_class_confirmation", "meditation_goal"
</reasoning>

<extracted_info>
{"live_class_confirmation": false, "meditation_goal": "减轻压力"}
</extracted_info>
</Example>

If the required information is not present within the chat history, append the value as null. Ensure you follow proper JSON formatting guidelines and accurately map user input to the specified fields.
Follow these steps to convert the input you receive into the appropriate JSON format.
Give your simple reasoning in <reasoning>.
Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.

<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>`,
    { templateFormat: 'mustache' }).format({
      chat_history
    })
  }
}