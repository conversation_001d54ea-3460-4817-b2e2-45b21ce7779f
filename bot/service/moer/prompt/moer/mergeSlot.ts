import { PromptTemplate } from '@langchain/core/prompts'

export class MergeSlotArrayPrompt {
  public static async format(slotName: string, slotDescription: string, arraysToMerge: string[][]) {
    const formatArrays = arraysToMerge.map((array, index) => `${index + 1}. ${  JSON.stringify(array)}`).join('\n')

    return PromptTemplate.fromTemplate(`You will be merging arrays of values for a given slot based on semantic similarity. Your goal is to combine values that express the same or very similar meanings while preserving truly distinct information.
    
Here is the slot you'll be working with:
<slot_name>{{slotName}}</slot_name>
<slot_description>{{slotDescription}}</slot_description>

Here are the arrays to merge:
<arrays_to_merge>
{{formatArrays}}
</arrays_to_merge>

Follow these rules for merging:
1. For values that express exactly the same meaning in different words, keep only one representative value
2. For values that express very similar but not identical meanings, combine them into a single comprehensive value that captures the key information from both
3. For values that express distinct meanings, preserve them separately
4. When combining similar values, maintain clarity and avoid redundancy
5. Use your judgment about semantic similarity, but err on the side of keeping values separate if you're unsure

First, analyze the semantic relationships between values in <analysis> tags. Group values by their semantic similarity and explain your reasoning.

Then, provide your merged result in <merged_result> tags in this format: 
[
    "value1",
    "value2",
    ...
]

Here's an example:
<example>
Input arrays:
1. ["有使用冥想应用的经验但未能保持持续练习"]
2. ["曾尝试过冥想但没有坚持下来", "学习过禅修"]

<analysis>
- "有使用冥想应用的经验但未能保持持续练习" and "曾尝试过冥想但没有坚持下来" express the same core meaning of having tried meditation but not maintaining practice
- "学习过禅修" is related but distinct as it specifically mentions zen meditation training
</analysis>

<merged_result>
[
    "有使用冥想应用的经验但未能保持持续练习",
    "学习过禅修"
]
</merged_result>
</example>

Proceed with your analysis and merging now. Put analysis in <analysis> tag and merged result in <merged_result> tag.`, { templateFormat: 'mustache' }).format({
      slotName,
      slotDescription,
      formatArrays
    })
  }
}