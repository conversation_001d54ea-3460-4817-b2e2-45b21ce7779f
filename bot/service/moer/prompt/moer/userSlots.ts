import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { z } from 'zod'

export class ExtractUserSlotPrompt {
  public static schema: z.ZodType<any, any> = z.object({
    meditation_experience: z.array(z.string()).nullable().optional(),
    goals_and_needs: z.array(z.string()).nullable().optional(),
    pain_points: z.array(z.string()).nullable().optional(),
    purchase_hesitation: z.array(z.string()).nullable().optional(),
    meditation_practice_experience: z.array(z.string()).nullable().optional()
  })

  public static async format(chat_history: string) {
    return SystemMessagePromptTemplate.fromTemplate(`You will be analyzing chat conversations to extract key information about a meditation course student's background, needs, and concerns. Your task is to extract specific fields from the conversation and format them as a JSON object.

You should extract information related to the following fields:

{
    "meditation_experience": {
        "type": "string",
        "description": "User's previous meditation experience, if any. Include both whether they have experience and what methods they've used if mentioned."
    },
    "goals_and_needs": {
        "type": "string",
        "description": "User's stated goals for meditation, for examples: focusing on stress relief, focus improvement, and/or sleep quality."
    },
    "pain_points": {
        "type": "string",
        "description": "Issues and emotions user aims to address through meditation. This includes specific life challenges, health concerns, emotional distress, and any other significant barriers that the user has mentioned."，
    },
    "meditation_practice_experience": {
        "type": "string",
        "description": "User's emotional or physical sensations and experiences during meditation practice, including feelings of calm, discomfort, stress, or any specific thoughts or body reactions."
  }
}

Before extracting information, analyze the conversation in your scratchpad to identify relevant details. For example:

<example>
<chat_history>
客户：我之前用过冥想App，但是坚持不下来。最近工作压力很大，而且睡眠不好。我想学习正确的冥想技巧，但担心没有时间。
客户：之前冥想感觉挺好的，身体在发热
</chat_history>

<scratchpad>
识别关键信息：
- 冥想经验：曾使用冥想应用但未能坚持
- 目标：应对工作压力，改善睡眠
- 痛点：工作压力，睡眠不好
- 冥想练习感受： 冥想感觉挺好，冥想时身体在发热
</scratchpad>

<extracted_info>
{
    "meditation_experience": "有使用冥想应用的经验但未能保持持续练习",
    "goals_and_needs": "寻求缓解工作压力和改善睡眠质量",
    "pain_points": "经历工作压力且睡眠不好",
    "meditation_practice_experience": "冥想感觉挺好，冥想时身体在发热"
}
</extracted_info>
</example>

Key extraction rules:
1. Only extract information explicitly mentioned in the conversation
2. Use precise quotes or paraphrasing that closely matches the user's words
3. If a field has no relevant information in the conversation, set its value to null
4. Focus on the user's statements rather than the instructor's suggestions
5. If multiple relevant points exist for a field, combine them with commas

Begin your response by analyzing the conversation in your scratchpad, then present your structured extraction in JSON format within <extracted_info> tags.

Please proceed with the extraction.

Here is the chat conversation to analyze:

<chat_history>
{{chat_history}}
</chat_history>`, { templateFormat: 'mustache' }) }

  public static extractStuckPointPrompt() {
    return SystemMessagePromptTemplate.fromTemplate(`# 卡点提取
你是一位冥想课程的销售，正在售卖21天系统课，现在你需要根据聊天记录，提取客户在当前的下单卡点，并以简洁明确的语言总结出客户的下单卡点

# 与客户的聊天记录
- 聊天记录中以“[]”包含的单独一条内容为营销信息或案例，遵守格式使用
{chatHistory}

# 要求
- 请注意，你要准确地提取和推断客户相关(user)的信息，而非其他方(assistant)的。
- 请准确的提取有关下单冥想课程的卡点，不要提取其他无关信息
- 如果没有下单卡点，<purchaseHesitation></purchaseHesitation>标签中留空即可

# 输出规则
将得到的客户卡点输出到<purchaseHesitation></purchaseHesitation>标签中（每个卡点用“-”隔开，如<purchaseHesitation>xxxx-xxxx</purchaseHesitation>）
Check your xml output and make sure it conforms, use <purchaseHesitation> and </purchaseHesitation> tags to enclose the answer`)
  }

  public static mergeStuckPointPrompt() {
    return SystemMessagePromptTemplate.fromTemplate(`请分析以下两段信息，并将它们合并为一段新信息。在合并过程中，识别相似的信息点（每段信息中信息点以"-"分隔），并直接合并，避免信息重复。确保最终输出既简洁又完整。

信息1：{oldStuckPoint}

信息2：{newStuckPoint}

合并要求：
1. 将相似元素合并为一个表述
2. 输出信息必须使用("-")分隔各个信息点，与输入格式保持一致
3. 不添加原文中不存在的信息
4. 如果信息1与信息2均为空则输出<result></result>即可(“使用<result></result>包裹”)

# 输出规则
请你先将思考过程输出到<think></think>中，
然后将得到的新信息输出到<result></result>标签中（每个卡点用“-”隔开，如<result>xxxx-xxxx</result>）
Check your xml output and make sure it conforms, use <result> and </result> tags to enclose the answer`)
  }
}