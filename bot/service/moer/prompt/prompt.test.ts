import { LLM } from '../../../lib/ai/llm/LLM'
import { IsSuitableJoinGroupPrompt } from './baoshu/is_suitable_join'
import { XMLHelper } from '../../../lib/xml/xml'
import { JSONHelper } from '../../../lib/json/json'
import { FewShotPromptTemplate, PromptTemplate } from '@langchain/core/prompts'
import { MergeSlotArrayPrompt } from './moer/mergeSlot'

describe('Test', function () {
  beforeAll(() => {

  })

  it('合并 slot', async () => {
    const prompt = await MergeSlotArrayPrompt.format('goals_and_needs', 'User\'s stated goals for meditation', [
      ['寻求缓解工作压力和改善睡眠质量', '提高专注力', '改善心理健康'],
      ['缓解工作压力', '改善睡眠质量', '提高专注力'],
      ['减轻工作压力', '提高专注力', '改善睡眠质量'],
      ['缓解工作压力', '提高专注力', '改善睡眠质量']
    ])

    console.log(prompt)

    const res = await LLM.predict(prompt, {
      model: 'gpt-4.1-mini'
    })

    const mergedResult = XMLHelper.extractContent(res, 'merged_result')
    if (!mergedResult) {
      return
    }

    console.log(JSONHelper.parse(mergedResult))
  }, 60000)

  it('测试', async () => {
    const prompt = await IsSuitableJoinGroupPrompt.format('韩国有什么推荐的学校么')
    const openai = new LLM()
    const llmRes = await openai.predict(prompt)

    const xmlRes = XMLHelper.extractContent(llmRes, 'answer')
    if (!xmlRes) {
      return false
    }

    const jsonRes = JSONHelper.parse(xmlRes)
    console.log(typeof jsonRes)
  }, 30000)

  it('few shots template', async () => {
    const examplePrompt = PromptTemplate.fromTemplate(
      'Question: {question}\n{answer}'
    )

    const examples = [
      {
        question: 'Who lived longer, Muhammad Ali or Alan Turing?',
        answer: `Are follow up questions needed here: Yes.
  Follow up: How old was Muhammad Ali when he died?
  Intermediate answer: Muhammad Ali was 74 years old when he died.
  Follow up: How old was Alan Turing when he died?
  Intermediate answer: Alan Turing was 41 years old when he died.
  So the final answer is: Muhammad Ali`,
      },
      {
        question: 'When was the founder of craigslist born?',
        answer: `Are follow up questions needed here: Yes.
  Follow up: Who was the founder of craigslist?
  Intermediate answer: Craigslist was founded by Craig Newmark.
  Follow up: When was Craig Newmark born?
  Intermediate answer: Craig Newmark was born on December 6, 1952.
  So the final answer is: December 6, 1952`,
      },
      {
        question: 'Who was the maternal grandfather of George Washington?',
        answer: `Are follow up questions needed here: Yes.
  Follow up: Who was the mother of George Washington?
  Intermediate answer: The mother of George Washington was Mary Ball Washington.
  Follow up: Who was the father of Mary Ball Washington?
  Intermediate answer: The father of Mary Ball Washington was Joseph Ball.
  So the final answer is: Joseph Ball`,
      },
      {
        question:
            'Are both the directors of Jaws and Casino Royale from the same country?',
        answer: `Are follow up questions needed here: Yes.
  Follow up: Who is the director of Jaws?
  Intermediate Answer: The director of Jaws is Steven Spielberg.
  Follow up: Where is Steven Spielberg from?
  Intermediate Answer: The United States.
  Follow up: Who is the director of Casino Royale?
  Intermediate Answer: The director of Casino Royale is Martin Campbell.
  Follow up: Where is Martin Campbell from?
  Intermediate Answer: New Zealand.
  So the final answer is: No`,
      },
    ]

    const prompt = new FewShotPromptTemplate({
      examples,
      examplePrompt,
      inputVariables: [],
    })

    const formatted = await prompt.format({})
    console.log(formatted.trim())
  }, 60000)
})