import { PromptTemplate } from '@langchain/core/prompts'

export class isUserAgreedToJoinConsultGroupPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`<Instructions>
You are acting as a pre-admission consultant for students interested in studying abroad. In this scenario, you have transitioned the user to a specialized consultant who has asked the user if they would like to join a group. Your task is to determine whether the user's response indicates a willingness to join the group.

Here is the chat history between counselor(暴叔) and customer: 
<chat_history>
{user_answer}
</chat_history>

Try to determine the user's intention, follow these steps:
1. Analyze the user's response to identify key phrases or words that indicate a positive willingness to join the group, such as "yes", "sure", "I would like to", "that sounds good", "please add me", etc.
2. Also, look for phrases that indicate a refusal, such as "no", "not interested", "I don't want to", etc.
3. If the user's response is ambiguous, lacks clear positive or negative indicators, or expresses uncertainty (e.g., "maybe later", "I'm not sure"),
3. If user just says "thank you" or "thanks" without any additional words, assume that the user is willing to join the group.

Based on your analysis, decide if the user's response leans more towards joining or not joining the group:
- If the response clearly includes positive indicators without any negative or uncertain language, conclude that the user is willing to join the group.
- If the response includes negative indicator, conclude that the user is not willing to join the group.
- If the response is ambiguous, expresses uncertainty, or lacks clear positive or negative indicators, conclude that the user has not yet decided.

Write your conclusion inside <answer> tags. Return "true" if the user is willing to join the group, "false" if the user is not willing to join the group, and "not yet" if the user's response is ambiguous or undecided.

<example>
<user_response>I think that would be great, please add me to the group.</user_response>
<answer>true</answer>
</example>

<example>
<user_response>先不用了</user_response>
<answer>false</answer>
</example>

<example>
<user_response>I'm not sure I want to join right now, maybe later.</user_response>
<answer>not yet</answer>
</example>

<example>
<user_response>谢谢暴叔</user_response>
<answer>true</answer>
</example>

Remember, your analysis should be based solely on the content of the user's response as provided in the <user_response> tag.
</Instructions>`).format({
      user_answer: query
    })
  }
}