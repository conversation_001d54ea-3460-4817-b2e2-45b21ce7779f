import { PromptTemplate } from '@langchain/core/prompts'

export class IsParentSupportedPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You are tasked with analyzing a chat log to determine whether a client's parents support their intention to study abroad.

Here's the chat log:

<chat_log>
{chat_history}
</chat_log>

Follow these steps to complete your analysis:

1. **Review the Chat Log:** Carefully read through the chat log provided. Look for any explicit mentions or implications about the client's parents' attitudes towards studying abroad. Focus on identifying key phrases or sentiments that directly address or hint at their support or lack thereof.

2. **Decide on the Support Status:**
   - If you find clear evidence that the parents are supportive of studying abroad, such as positive statements or encouragement related to the topic, you should conclude they support the intention.
   - If you find statements that express disapproval, concern, or discouragement about studying abroad from the parents, conclude they do not support the intention.
   - If the chat log does not contain any clear information regarding the parents' stance on studying abroad, or if the context is too ambiguous, you should return null.

3. **Format Your Response:** After analyzing the chat log, write your conclusion (YES/NO/null) in the <answer> tag. If you conclude support or lack of support, provide a brief explanation for your decision based on the sentiments found in the chat log. If undetermined, simply state "null". 
Response format:
[Brief explanation of the analysis]
<answer>
[YES/NO/null]
</answer>

<example>
<chat_log>
Parent: We really think you should focus on opportunities here rather than going abroad.
Client: But I've always wanted to study in Europe!
Parent: It's not the right time with everything going on.
</chat_log>

Based on the conversation, the client's parents do not support the intention to study abroad, as they express concerns and prefer local opportunities.
<answer>
NO
</answer>
</example>

<example>
<chat_log>
Parent: How exciting that you might study in Germany!
Client: Yes, I'm looking at programs now.
Parent: We'll support you in any way we can!
</chat_log>

The client's parents support the intention to study abroad, as evidenced by their enthusiasm and offer of support.
<answer>
YES
</answer>
</example>

<example>
<chat_log>
Client: I'm thinking about studying abroad next year.
Parent: Oh, really? Have you looked into the details yet?
</chat_log>

<answer>
null
</answer>
</example>

Your response should directly reflect the analysis of the chat log without adding assumptions beyond what is explicitly mentioned.`).format(
      {
        chat_history: chat_history
      })
  }
}