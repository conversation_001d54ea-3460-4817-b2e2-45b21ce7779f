import { UserInfoExtractPrompt } from './user_info_extract'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { IntendedCustomerPrompt } from './intented_customer'
import { IsAICompleteSlotAskTaskPrompt } from './is_slot_ask'
import { EducationGroupCategoryPrompt } from './education_group_category'
import { BooleanUserInfoExtractPrompt } from './study_abroad_and_is_parent'
import { IntentionCategorizePrompt } from './intention_categorize'
import { isUsingWebSearchPrompt } from './is_using_web_search'
import { IsParentSupportedPrompt } from './is_parent_support'
import { ShouldPushProjectPrompt } from './should_push_project'
import { ProjectSelectPrompt } from './pick_project'
import { OperationGroupRecommendationPrompt } from './group_pick'
const llm = new LLM()

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(await llm.predict(await UserInfoExtractPrompt.format('我是一名初三学生，计划在国内完成高中教育后去国外读大学，目标是澳大利亚或者加拿大。总预算约为150万人民币。')))
  })

  it('test', async () => {
    console.log(await llm.predict(await UserInfoExtractPrompt.format('你好')))
  }, 30000)

  it('prompt test', async () => {
    console.log(await llm.predict(await IntendedCustomerPrompt.format('我是一名初三学生，计划在国内完成我是一名初三学生，计划在国内完成高中教育后去国外读大学，目标是澳大利亚或者加拿大。总预算约为150万人民币。')))
  }, 30000)

  it('q123', async () => {
    console.log(await llm.predict(await IsAICompleteSlotAskTaskPrompt.format('', '')))
  }, 30000)

  it('123', async () => {
    console.log(await llm.predict(await EducationGroupCategoryPrompt.format('非全专科', '')))
  }, 30000)


  it('test1', async () => {
    console.log(await llm.predict(await BooleanUserInfoExtractPrompt.format('暴叔，我想出去，大三，有什么推荐么，可能毕业之后润')))
  }, 60000)

  it('test2', async () => {
    console.log(await llm.predict(await BooleanUserInfoExtractPrompt.format('四川文科 495 二本线上31分，一本线下41分。英语121羽毛球国家一级运动员，走高水平没走通')))
  }, 60000)

  it('test3', async () => {
    console.log(await llm.predict(await BooleanUserInfoExtractPrompt.format('四川文科 495 二本线上31分，一本线下41分。日语100，有什么路')))
  }, 60000)

  it('高考', async () => {
    console.log(await llm.predict(await BooleanUserInfoExtractPrompt.format('暴叔我要高考了')))
  }, 60000)

  it('父母是否支持', async () => {
    console.log(await llm.predict(await IsParentSupportedPrompt.format(`暴叔：咱家里支持么
顾客：不太想让我出去`)))
  }, 60000)
})


describe('Test', function () {
  beforeAll(() => {

  })

  it('正常', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format('我是一名初三学生，计划在国内完成高中教育后去国外读大学，目标是澳大利亚或者加拿大。总预算约为150万人民币。')))
  }, 60000)

  it('保录', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format(`你好，我想了解一下，留学的事情。
我这边的话，我现在是大三，马上暑假结束之后就是大四了，然后我是在一个2本，浙江万里学院，专业是生物工程，绩点在3.3往上的样子，然后比较麻烦的是，我语言是学的日语，高考的时候也是考的日语，但是也是因为我不是太喜欢日语
所以说我也不想去日本，后现在基本上就是处于一个英语真的没什么基础的情况，从高一的时候开始就是日语了，所以可能我这个语言成绩真的是个问题
那我能不能，先保录到英国100左右的大学，比如诺丁汉这种，然后距离我毕业还有1年多，这期间我补一下英语基础，后面在英国够我毕业就行，这样可以吗,`)))
  }, 60000)

  it('offer 对比', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format('暴叔，我本科专业自动化，收到了伯明翰大学电子和计算机工程、机器人工程两个offer，这两个哪个好一点')))
  }, 60000)

  it('测试1', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format('暴叔，怎么润出去')))
  }, 60000)

  it('测试', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format('暴叔，我美国身份，怎么申请研究生')))
  }, 60000)

  it('1243', async () => {
    console.log(await llm.predict(await IntentionCategorizePrompt.format('我是香港人，想咨询下华侨生联考')))
  }, 60000)


  it('using web search1', async () => {
    console.log(await llm.predict(await isUsingWebSearchPrompt.format('暴叔，我本科专业自动化，收到了伯明翰大学电子和计算机工程、机器人工程两个offer，这两个哪个好一点')))
  }, 60000)


  it('using web search2', async () => {
    console.log(await llm.predict(await isUsingWebSearchPrompt.format('美国 CS 有什么推荐')))
  }, 60000)


  it('using web search3', async () => {
    console.log(await llm.predict(await isUsingWebSearchPrompt.format('暴叔你好')))
  }, 60000)

  it('pt1', async () => {
    console.log(await llm.predict(await ShouldPushProjectPrompt.format('暴叔你好')))
  }, 60000)

  it('projectSelect', async () => {
    console.log(await new LLM({ model: 'claude' }).predict(await ProjectSelectPrompt.format(`预算：100 万
当前学历水平：本科
GPA：73
目标：出国读研
城市：烟台
学校：滑铁卢
是否留学：是`, `以下是本科生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 英国
    - 学制1年，预算40-60w，偏好国内985、211学生，其次是高分双非，对学术要求相对高。1年制硕士，省时省钱，回国认可度高。

2. 澳洲
    - 每年预算40-55w，学制一般1-2年，宽进严出，澳洲硕士申请门槛低于英美，低分也能逆袭前100，其次澳洲具备移民属性，硕士毕业可留在当地工作拿身份。

3. 香港
    - 每年预算30-50w，香港硕士1年制，省时省钱，认可度高，离家近，毕业之后留港工作，可拿香港身份。

4. 新加坡
    - 每年预算30-35w，学制1-2年，去新加坡读硕士包容性强，学霸可以冲新二；而普娃和成绩较弱的学生，可以冲私立大学的合作办学项目，拿英美澳加的大学的硕士文凭。城市虽小，但是法制严格，留学安全。

5. 美国
    - 每年预算60-100w，1-2年学制，硕士阶段的TOP1，国际学历含金量和认可度高。美研申请不聚焦在学生的学术成绩，更看重学生全面的综合能力，就业机会多。

6. 中外合作办学硕士
    - 总预算15-35w，1-2年学制，省钱不出国，可以选择非全日制的学习方式，工作和学历提升两不误，拿海外大学的硕士文凭。

7. 韩国
    - 总预算10-30w, 1-2年学制，可以半工半读，性价比较高，三种不同语言的硕士授课项目，适合不同语言类型的学生，不会韩语、英语较弱的学生，花1-2年的时间，也可以拿韩国名校的硕士学位`)))
  }, 60000)


  it('groupPick', async () => {
    console.log(await llm.predict(await OperationGroupRecommendationPrompt.format('单词群怎么进')))
  }, 60000)

})
