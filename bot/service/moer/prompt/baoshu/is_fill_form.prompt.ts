import { PromptTemplate } from '@langchain/core/prompts'

export class isFillFormPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are tasked with determining whether a user has filled out a specific form based on the information they provide. Here is the form template:

<form_template>
姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）：
</form_template>

Your task is to analyze the following user input and determine if they have filled out the form above:

<user_input>
{user_input}
</user_input>

Follow these steps to complete the task:

1. Carefully read through the user input.
2. Compare the information provided in the user input to the fields in the form template.
3. Note that the user doesn't need to fill out all fields for it to be considered filled. Even if they've only provided information for one or two fields, it still counts as filling out the form.
4. Look for any information that matches the fields in the form template, regardless of the order or exact wording.
5. If you find any information that corresponds to at least one field in the form template, consider the form as filled.
6. If you cannot find any information that matches any of the fields in the form template, consider the form as not filled.

Provide your answer in the following format:

<reasoning>
Reasoning: [Simply explain your reasoning here]
</reasoning>
<answer>
[YES/NO] - The form [is/is not] filled.
</answer>You are tasked with determining whether a user has filled out a specific form based on the information they provide. Here is the form template:

<form_template>
姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）：
</form_template>

Your task is to analyze the following user input and determine if they have filled out the form above:

<user_input>
{user_input}
</user_input>

Follow these steps to complete the task:

1. Carefully read through the user input.
2. Compare the information provided in the user input to the fields in the form template.
3. Note that the user doesn't need to fill out all fields for it to be considered filled. Even if they've only provided information for one or two fields, it still counts as filling out the form.
4. Look for any information that matches the fields in the form template, regardless of the order or exact wording.
5. If you find any information that corresponds to at least one field in the form template, consider the form as filled.
6. If you cannot find any information that matches any of the fields in the form template, consider the form as not filled.

Provide your answer in the following format:

<reasoning>
Reasoning: [Simply explain your reasoning here]
</reasoning>

<answer>
[YES/NO] - The form [is/is not] filled.
</answer>

Remember, even if only one field is filled, the answer should be YES. Only answer NO if absolutely no information matching any field in the form template is provided.`).format({
      user_input: query
    })
  }
}