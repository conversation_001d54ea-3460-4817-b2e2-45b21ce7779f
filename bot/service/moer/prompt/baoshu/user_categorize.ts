import { PromptTemplate } from '@langchain/core/prompts'

export class UserCategorizePrompt  {
  public static async format(user_consultant_chat: string) {
    return PromptTemplate.fromTemplate(`<Instructions> 您的任务是根据客户与留学顾问的聊天记录,从学业情况和留学aspirations的角度对客户进行分类。以下是分类类别:
【学霸登顶】:该类别适用于有能力就读中国顶尖大学(985和211大学)且渴望就读更高声望院校的学生。
【普娃冲名校】:该类别包括来自中国双非和二本院校的普通成绩水平孩子,渴望就读顶尖大学的学生。
【差生拿文凭】:该类别适用于考不上大学的学生，如大专，中专，高职，技校生，想提升学历的学生。
【留学移民】:该类别适用于希望通过留学移民的学生。
【低龄规划】:该类别涉及从小(初中或之前)为孩子未来教育做规划,如从小学阶段就开始为上大学制定策略。
以下是客户与留学顾问的聊天记录:

<user_consultant_chat>
{user_consultant_chat}
</user_consultant_chat>

根据聊天记录中提供的信息,将客户分类到上述类别之一。考虑学生当前的学业情况、志向,以及聊天中提到的任何关于留学或未来规划的具体目标等因素。

请在<classification>标签内以1为起始索引写下你的分类决定。

<example> 
<user_consultant_chat> 
学生目前就读于中国一所双非大学,表示强烈希望就读美国顶尖大学以提升职业前景。 
</user_consultant_chat> 

<classification> 
2.【普娃冲名校】 
</classification> 
</example> 

<example>
<user_consultant_chat> 
学生正在读初中,家长希望从小就为孩子申请全球顶尖大学做准备。 
</user_consultant_chat>

<classification> 
5.【低龄规划】
</classification> 
</example>
请仔细分析客户与顾问的聊天记录,根据所提供的类别描述将其匹配到最合适的类别。`).format(
      {
        user_consultant_chat: user_consultant_chat
      })
  }
}

export class DetailQuestionUserCategorizePrompt  {
  public static async format(user_consultant_chat: string) {
    return PromptTemplate.fromTemplate(`<Instructions> 您的任务是根据客户与留学顾问的聊天记录, 对客户所处的留学决策阶段进行分类。以下是分类类别:
1. 确认自己的痛点和目标，但是方向不确定，是否出国也不确定。
2. 确认出国，但是不确定国家/通过什么路径
3. 明确出国方向，有择校和专业选择，申请流程等具体问题。
4. 正处于申请阶段，或者已经申请完成。

以下是客户与留学顾问的聊天记录:
<user_consultant_chat>
{user_consultant_chat}
</user_consultant_chat>

根据聊天记录中提供的信息,将客户决策阶段分类到上述类别之一。考虑学生当前的申请阶段，申请进度等相关信息。

请在<classification>标签内以1为起始索引写下你的分类决定。

<example> 
<user_consultant_chat> 学生：我目前大三，想出国读研，但是还没决定去哪个国家比较好。我的专业是金融，不知道英美澳加哪个国家的金融专业比较强？申请难度如何？ 顾问：美国，英国的金融专业都很强，且申请难度适中。澳大利亚申请难度相对低一些。建议您对比不同国家院校的课程设置、学费等因素，结合自身情况权衡选择。我可以为您提供几所比较有针对性的院校建议。 </user_consultant_chat>
<classification>2</classification>
</example>

<example> 
<user_consultant_chat> 学生：我现在在准备雅思考试，但是感觉提分很困难。不知道能不能申请到理想的学校。 顾问：雅思提分需要一定时间和努力。您的目标院校对雅思成绩有什么要求吗？我们可以结合您的目标院校情况，制定一份备考计划。此外，申请成功不只看雅思成绩，还要准备好推荐信、文书等申请材料。有什么需要我这里都可以为您提供指导和建议。 </user_consultant_chat>
<classification>3</classification>
</example>

<example>
<user_consultant_chat> 学生：老师您好，我目前在准备申请美国的 MBA 项目，最近几所心仪的学校陆续放榜了，但是目前还没收到任何 offer, 感到很焦虑。不知道自己的申请材料是否有问题？ 顾问：从您的背景来看，您的整体条件不错，申请顶尖 MBA 项目难度较大，还需要再等待一段时间。建议您可以主动和目标院校沟通，表达您的强烈兴趣。同时我们可以再梳理下您的申请材料，看是否还有优化的空间。保持积极乐观的心态，相信您的付出一定会有回报的。 </user_consultant_chat>
<classification>4</classification>
</example>

<example> 
<user_consultant_chat> 学生：老师，我马上高中毕业了，家里条件一般，希望能出国读本科，但是听说留学费用很高，自己的成绩也不是很突出，不知道有没有合适的出国途径。 顾问：出国念本科有多种途径，可以申请奖学金、助学贷款等方式解决资金问题。选校时可以考虑适合自己的定位学校，学费、生活费压力会小一些。此外，高考后选择国内读个大专，再申请国外学校本科也是一种选择，学历提升的同时节省部分留学开支。我们可以根据您的具体情况，一起探讨出一条合适的留学路径规划。 </user_consultant_chat>
<classification>1</classification>
</example>
请仔细分析客户与顾问的聊天记录,根据所提供的类别描述将其匹配到最合适的类别。`).format(
      {
        user_consultant_chat: user_consultant_chat
      })
  }
}


export class IsInApplicationStagePrompt  {
  public static async format(user_consultant_chat: string) {
    return PromptTemplate.fromTemplate(`<Instructions> 您的任务是根据客户与留学顾问的聊天记录, 判断客户是否已经完成留学申请，还是在申请中，还是不确定:
1. 已经完成申请
2. 仍在申请中，或者还没开始申请
3. 不确定申请的状态

以下是客户与留学顾问的聊天记录:
<user_consultant_chat>
{user_consultant_chat}
</user_consultant_chat>

根据聊天记录中提供的信息,将客户决策阶段分类到上述类别之一。考虑学生当前的申请阶段，申请进度等相关信息。

请在<classification>标签内以1为起始索引写下你的分类决定。

<example> 
<user_consultant_chat> 学生：我目前大三，想出国读研，但是还没决定去哪个国家比较好。我的专业是金融，不知道英美澳加哪个国家的金融专业比较强？申请难度如何？ 顾问：美国，英国的金融专业都很强，且申请难度适中。澳大利亚申请难度相对低一些。建议您对比不同国家院校的课程设置、学费等因素，结合自身情况权衡选择。我可以为您提供几所比较有针对性的院校建议。 </user_consultant_chat>
<classification>2</classification>
</example>

<example> 
<user_consultant_chat> 学生：我现在在准备雅思考试，但是感觉提分很困难。不知道能不能申请到理想的学校。 顾问：雅思提分需要一定时间和努力。您的目标院校对雅思成绩有什么要求吗？我们可以结合您的目标院校情况，制定一份备考计划。此外，申请成功不只看雅思成绩，还要准备好推荐信、文书等申请材料。有什么需要我这里都可以为您提供指导和建议。 </user_consultant_chat>
<classification>2</classification>
</example>

<example>
<user_consultant_chat> 学生：老师您好，我目前在准备申请美国的 MBA 项目，最近几所心仪的学校陆续放榜了，但是目前还没收到任何 offer, 感到很焦虑。不知道自己的申请材料是否有问题？ 顾问：从您的背景来看，您的整体条件不错，申请顶尖 MBA 项目难度较大，还需要再等待一段时间。建议您可以主动和目标院校沟通，表达您的强烈兴趣。同时我们可以再梳理下您的申请材料，看是否还有优化的空间。保持积极乐观的心态，相信您的付出一定会有回报的。 </user_consultant_chat>
<classification>1</classification>
</example>

<example> 
<user_consultant_chat> 学生：老师，你好，想咨询下留学方面 </user_consultant_chat>
<classification>3</classification>
</example>
请仔细分析客户与顾问的聊天记录,根据所提供的类别描述将其匹配到最合适的类别。`).format(
      {
        user_consultant_chat: user_consultant_chat
      })
  }
}