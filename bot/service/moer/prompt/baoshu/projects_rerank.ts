import { PromptTemplate } from '@langchain/core/prompts'
import { logPrompt } from './decorator'

export class ProjectsRerankPrompt {

  public static async format(chat_history: string, project_list: string) {
    return PromptTemplate.fromTemplate(`You will be given a chat history between a user and a study abroad consultant, and a list of projects. Your task is to analyze the user's needs as expressed in the chat and match them with the most suitable projects from the list provided. Here are the detailed steps you should follow:
    
    <chat_history>
    {chat_history}
    </chat_history>
    
    <project_list>
    {project_list}
    </project_list>
    
    1. Carefully read through the chat history to understand the user's preferences, requirements, and any specific mentions of countries or programs.
    
    2. Review the project list. Each project is associated with a country or a specific study program.
    
    3. Identify keywords or phrases in the chat that match the descriptions in the project list. For example, if the user mentions a preference for studying in English-speaking countries, prioritize projects associated with countries like the USA or the UK.
    
    4. Rank the projects based on how closely they match the user's expressed preferences. Consider factors such as mentioned countries, desired study fields, and any other specific requirements.
    
    5. Select the top four projects that best match the user's needs. List them in order of relevance, with the most relevant project first.
    
    6. Output the numbers of these top four projects in a list format as shown in the example below. Ensure that the project numbers correspond exactly to those in the project list.
    
    <example>
    Output: ['2', '3', '1', '5']
    </example>
    
    Remember, your goal is to accurately match the user's needs with the appropriate projects, ensuring that the most suitable options are prioritized. This will help provide a tailored response that aligns with the user's study abroad goals.`).format(
      {
        chat_history: chat_history,
        project_list: project_list
      })
  }
}