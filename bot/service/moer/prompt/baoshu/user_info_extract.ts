import { PromptTemplate } from '@langchain/core/prompts'

export class UserInfoExtractPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`Your task is to carefully read the user's chat history with the study abroad consultant and extract key information related to the user's study abroad requirements. This will help in retrieving relevant information from the database based on the user's needs.
<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>

Here's how you should approach this task:

Read the user's query carefully to identify key requirements and constraints. Focus on extracting information related to the following fields:
{
    "application_status": {"type": "string", "enum": ["未知", "未开始", "申请中", "已完成"], "description": "客户目前的留学申请阶段"},
    "budget": {"type": "number", "description": "总预算，单位为万，如果有预算的区间，可以用数组表示，如：[10, 20] 表示 10-20 万。如果客户省略单位，直接说 20～30，指的是 20～30 万。如果客户明确说'无上限' 或 '预算够的'，'预算充足'等，这个位置使用 9999 填充。如果客户没有明确说预算或者没有提及，则设为 null"},
    "budget_is_per_year": {"type": "boolean", "description": "true 为每年平均预算， false 为总预算"},
    "gpa": {"type": "string", "description": "成绩、绩点, 如果客户有对成绩的描述，如 "成绩很差" 也可以填写"},
    "language_test_score": {"type": "string", "description": '语言成绩， e.g. "雅思 7.5", "GRE 330", "CET-4 560"'},
    "current_level_of_education": {"type": "string", "enum": ['低龄', '小学', '初中', '职高', '中专', '技校', '高中', '大专', '本科', '硕士', '博士'], "description": "客户当前所处的教育阶段（学历），如果是高考，则为高中。注意：这里指的是客户当前的学历，而不是客户询问或打算申请的学历。例如，如果客户询问 '硕士费用'，但没有明确说明自己当前是硕士，则不应将 current_level_of_education 设为 '硕士'。如果客户已经专升本，应该是 '本科'"},
    "grade": {"type": "string", "enum": ['一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '初一', '初二', '初三', '初四', '高一', '高二', '高三', '大一', '大二', '大三', '大四', '研一', '研二', '研三'], "description": "客户当前所在年级"},
    "goal": {"type": "string", "description": "客户当前短期内的目或想咨询的留学问题或。例如 "留学规划", "高中成绩不好，咨询学校", "了解下中外合办"},
    "user_intended_country": {"type": "array", "items": {"type": "string"}, "description": "客户的意向国家， e.g. ["德国", "美国"]."},
    "application_stage": {"type": "string", "enum": ['高中', '大专', '本科', '硕士', '博士'], "description": "客户需要申请什么学历, 比如：高中、大专、本科、硕士、博士"},
    "preferred_plan": {"type": "string", "description": "对于达成现有目标，客户自己的想法是什么，比如说想去什么国家的，申请什么院校，或者有什么对学校，路线的想法或偏好。如果客户回复'还没有'或者'不知道'，则填入'还没有'或'不知道'。"},
    "city": {"type": "string", "description": "客户所在的城市"},
    "school": {"type": "string", "description": "客户就读的最高学历的所在学校名称，如：清华大学"},
    "major": {"type": "string", "description": "当前客户的专业，如果有辅修或双专业也要填写， 如："计算机科学与技术 辅修哲学", "哲学 数学""},
    "user_intended_school": {"type": "array", "items": {"type": "string"}, "description": "客户的意向学校，如 ["MIT", "斯坦福"]"},
    "user_intended_project": {"type": "array", "items": {"type": "string"}, "description": "客户的意向项目，如 ["国际本科", "中外合办", "4+0", "3+1", "2+2", "TOP-UP", "华侨生联考"]等"},  
}

将提取的信息整理成结构化的格式。请特别注意数据类型和单位 (例如，预算值应转换为万元)。注意 budget, user_intended_country, user_intended_school, user_intended_project 应该只从客户侧的消息中提取，不应该从顾问的回复中提取。
以 JSON 格式输出提取的信息。以大括号开始，将每个字段作为键，提取的值作为值，多个字段之间用逗号分隔。以大括号结束。将 JSON 放在 <extracted_info> 标签内。
确保 JSON 格式正确，并按照字段描述中指定的键、值和数据类型进行格式化。

Key extraction rules:
1. "budget":
    - Look for mentions of the user’s total budget, including phrases like “预算”, “总预算”, “存了”, “准备”, etc.
    - If user has a budget range, pay more attention to the user’s budget range, instead of counselor(暴叔) suggestions.
2. "application_stage"
    - 如果客户说了想申请什么学历，就提取该项。比如“硕士”，“本科”等。
3. "grade"
    - 注意毕业生，如果客户没有主动说研究生或博士，默认为大学毕业，年级可以为 “大四”

以下是一些示例，供您参考:
<Example>
<chat_history>
你好，我是音乐表演专业今年大四国内的普通二本，想出国读研究生，预算 30w 以内，我专业水平还不错，可以出申请用的作品集，叔有什么好的推荐吗
</chat_history>

<reasoning>
识别关键需求：预算为 30 万，当前教育背景为二本， 年级为大四， 专业为音乐表演，读研究生
将信息映射为字段:'budget','current_level_of_education', 'grade', 'major', 'application_stage'
</reasoning>

<extracted_info>
{"budget":30,"current_level_of_education":"本科","grade":"大四","major":"音乐表演", "application_stage": "硕士"}
</extracted_info>
</Example>

<Example>
<chat_history>
你好
</chat_history>

<reasoning>
There is no user information in the chat history.
</reasoning>

<extracted_info>
{}
</extracted_info>
</Example>

<Example>
<chat_history>
顾问: 咱这边是家长还是同学？
客户: 同学
顾问: 咱们目前自己有什么想法？有没有特别想去的国家？
客户: 目前还没有
</chat_history>

<reasoning>
识别关键需求：preferred_plan: 还没有。
将信息映射为字段: "preferred_plan"
</reasoning>

<extracted_info>
{"preferred_plan": "还没有"}
</extracted_info>
</Example>

<Example> 
<chat_history>
客户：我孩子初一成绩不是很好，也就60分吧，后续有什么规划建议吗。暴叔 
暴叔：现在孩子还小，不着急出国。 
暴叔：你有多少预算？ 
客户：出国一般大概要准备多少，我没有什么概念。 
暴叔：70-80w还是要的。 
</chat_history>
<reasoning> 
识别关键需求：初一，60分，当前询问身份是家长，预算和是否出国，暴叔都给了提议，但是家长没有给予肯定，所以不提取
将信息映射为字段:'current_level_of_education','grade','gpa', 'goal'
</reasoning>

<extracted_info> 
{"current_level_of_education":"初中","grade":"初一","gpa": "60", "goal": "初中规划建议"} 
</extracted_info>
</Example>


<Example> 
<chat_history>
暴叔: 明白了，预算大概多少呢？
客户: 10w吧
暴叔: 10万预算有点紧张，考虑欧洲吗？
</chat_history>

<reasoning> 
识别关键需求：预算为 10 万，暴叔建议考虑欧洲，但是不是客户的需求，不做意向国家的提取
将信息映射为字段:'budget'
</reasoning>

<extracted_info> 
{"budget": 10} 
</extracted_info>
</Example>

Follow these steps to convert the input you receive into the appropriate JSON format.
Give your simple reasoning in <reasoning>.
Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.`, { templateFormat: 'mustache' }).format(
      // @ts-ignore mustache 使用
      {
        chat_history: chat_history
      })
  }
}