import { PromptTemplate } from '@langchain/core/prompts'

export class BudgetFromCounselorPrompt {
  public static async format(budget: string, chat_history: string) {
    return PromptTemplate.fromTemplate(`You will be given a budget and a chat history. Your task is to determine if the budget was extracted from the chat history.
    
Here is the budget:

<budget>
{budget}
</budget>

Here is the chat history:

<chat_history>
{chat_history}
</chat_history>

Please follow these steps:

1. Carefully read through the entire chat history.
2. Identify any parts of the chat history that mention or discuss budget details.
3. Compare these details with the provided budget.
4. Determine if the provided budget matches the details extracted from the chat history.

Write your reasoning and the result in the following format:

<answer>
<Reasoning>
[Write your reasoning in Chinese here, explaining the details you compared and any matches or discrepancies found.]
</Reasoning>
<Result>
[Write "YES" if the budget was extracted from the chat history, "NO" if it was not.]
</Result>`).format({
      budget: budget,
      chat_history: chat_history,
    })
  }
}