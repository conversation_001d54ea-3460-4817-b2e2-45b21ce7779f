import { DataService } from './getData'
import { ChatDB } from '../database/chat'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import isoWeek from 'dayjs/plugin/isoWeek'
import { ChatStatStoreManager } from '../storage/chat_state_store'
import { Config } from '../../../config/config'
import { loadConfigByAccountName } from '../../../../test/tools/load_config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('获取系统班开营时间', async () => {
    console.log(await DataService.getSystemCourseStartTime('7881299769197087_1688855842687484'))
  }, 60000)

  it('获取期数', async () => {
    function getCurrentCourseNo() {
      function getPeriod(date: Date): number {
        dayjs.extend(weekOfYear)
        dayjs.locale('zh-cn')
        const now = dayjs(date)

        // 检查是否为周日
        if (now.day() === 0) { // day() 返回 0 表示周日
          return now.week() - 1 // 将周日归到当前周
        }

        return now.week()
      }

      // 42 对应 37 期
      const weekNo = getPeriod(new Date())

      return weekNo - 42 + 37
    }

    console.log(getCurrentCourseNo())

    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('保利威直播', async () => {
    const liveId = 5302805
    const courseStatus = await (DataService as any).pullMoerCourseStatusFromPolyv('941355', liveId, new Date('2024-11-5'), 4739)
    console.log(JSON.stringify(courseStatus, null, 4))
  }, 60000)

  it('tanglang broadcast', async() => {
    const moerId = '1024890'
    const courseInfo = await DataService.getMoerCourses(moerId)
    console.log(courseInfo)
    if (courseInfo['1'].live) {
      const courseStatus = await DataService.getMoerCourseStatus(courseInfo['1'].live)
      console.log(courseStatus)
    }
  })

  it('should pass', async () => {
    const chat_id = '7881302599967827_1688856322643146'
    // await ChatStatStoreManager.initState(chat_id)
    // fixDate('2024-10-14')

    console.log(await DataService.isCompletedCourse(chat_id, {
      day: 1,
      is_recording: true,
    }))
  }, 60000)


  it('xxx', async () => {
    const names :string[] = []
    for (const name of names) {
      const chats = (await DataService.getChatByWechatName(name)) as unknown as any[]
      if (chats && chats.length > 1) {
        const chat = chats[chats.length - 1]
      }
    }
  }, 60000)

  it('', async () => {
    console.log(await DataService.getEnergyTestScore('7881300929922693_1688857003605938'))
  }, 60000)

  it('123123213', async () => {
    const event = {
      'logid': '4qLHAhfa00E99RvnV73B',
      'examScore': 0,
      'userId': 926600,
      'mobile': '15902060557',
      'event': 'jinshuju_user_exam_score'
    }
    console.log(JSON.stringify(await ChatDB.getByMoerId(event.userId.toString()), null, 4))
  }, 60000)

  it('moerID', async () => {
    const event = {
      'logid': '4qLHAhfa00E99RvnV73B',
      'examScore': 0,
      'userId': 926611,
      'mobile': '15902060557',
      'event': 'jinshuju_user_exam_score'
    }
    console.log(JSON.stringify(await ChatDB.getByMoerId(event.userId.toString()), null, 4))
  }, 60000)

  it('38期', async () => {
    function getPeriod(date: Date): number {
      const startDate = dayjs('2024-01-29') // 示例日期

      dayjs.extend(isoWeek)

      const now = dayjs(date)

      // 将开始日期和当前日期对齐到各自的周一
      const startOfStartWeek = startDate.startOf('isoWeek')
      const startOfCurrentWeek = now.startOf('isoWeek')

      // 计算两个日期之间相差的周数，并加1表示第1周
      return startOfCurrentWeek.diff(startOfStartWeek, 'week')
    }

    console.log(getPeriod(new Date()))
    console.log(getPeriod(new Date('2024-12-30 00:00:00')))
  }, 60000)

  it('week', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('TestGetSystemCourseStartTime', async () => {
    const chats = await DataService.getChatByWechatName('windowLU')
    const chat = chats[0] as any
    const chat_id = chat._id
    console.log(await DataService.getSystemCourseStartTime(chat_id))
  }, 60000)

  it('TestIsCompleteAllCourse', async () => {
    const chats = await DataService.getChatByWechatName('Andrew3')
    const chat = chats[0] as any
    const chat_id = chat._id
    console.log(await DataService.isCompletedAllCourse(chat_id))
  }, 60000)

  it('deleteTag', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('tongtong')
    // 更新 客户标签
    await DataService.deleteTags('****************', '先导-完课')
  }, 60000)

})