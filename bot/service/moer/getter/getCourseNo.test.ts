/**
 * 说明：
 * - 每个测试通过 setSystemTime 把“当前时间”定到指定日期 00:00:00。
 * - 返回值来自产品需求：
 *   • 2025‑04‑26~2025‑05‑04 ‑‑ 固定 64
 *   • ≥2025‑05‑05          ‑‑ getPeriod() 结果 - 2
 *   • 其他日期              ‑‑ getPeriod() 原值
 */
import { DataService } from './getData'
import { sleep } from '../../../lib/schedule/schedule'

describe('CourseUtils.getCurrentWeekCourseNo()', () => {
  beforeAll(() => {
    // 使用 modern fake timers，允许动态设置系统时间
    jest.useFakeTimers()
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  const cases: Array<[string, number]> = [
    ['2025-04-22', 63],
    ['2025-04-25', 63], // 停播前最后一天：原规则
    ['2025-04-26', 63], // 停播区间首日：固定 64
    ['2025-04-28', 63], // 停播区间内：固定 64
    ['2025-05-04', 63], // 停播区间末日：固定 64
    ['2025-05-05', 64], // 复播首日：64 期
    ['2025-05-11', 64], // 复播后首期周日：64 期
    ['2025-05-12', 65], // 复播后第二期：65期
  ]

  // 下一期， 4.21 到 5.4 号下一期都为 64，5.5 号开始正常 65期，66期
  const nextWeekCases: Array<[string, number]> = [
    ['2025-04-22', 64],
    ['2025-04-25', 64],
    ['2025-04-26', 64],
    ['2025-04-28', 64],
    ['2025-05-04', 64],
    ['2025-05-05', 65],  // 复播首日：65 期
    ['2025-05-11', 65],
    ['2025-05-12', 66],
  ]

  it('测试', async () => {
    jest.setSystemTime(new Date('2025-05-04T00:00:00Z'))
    const actual = DataService.getNextWeekCourseNo()
    console.log(actual)
  }, 60000)

  it.each(cases)('on %s should return %i', (dateString, expected) => {
    jest.setSystemTime(new Date(`${dateString}T00:00:00Z`))
    const actual = DataService.getCurrentWeekCourseNo()
    expect(actual).toBe(expected)
  })

  it.each(nextWeekCases)('on %s next week should return %i', (dateString, expected) => {
    jest.setSystemTime(new Date(`${dateString}T00:00:00Z`))
    const actual = DataService.getNextWeekCourseNo()
    expect(actual).toBe(expected)
  })
})


const doubleAfterDelay = async (n: number) => {
  await sleep(1000)
  return n * 2
}

const cases = [
  // [输入值, 预期输出]
  [1, 2],
  [5, 10],
  [0, 0],
  [-3, -6],
]

describe('doubleAfterDelay 并发异步测试', () => {
  it.concurrent.each(cases)(
    'when input is %i, should resolve to %i',
    async (input, expected) => {
      const result = await doubleAfterDelay(input)

      console.log(result)
      expect(result).toBe(expected)
    }
  )
})


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const actual = DataService.getNextWeekCourseNo()
    console.log(actual)
  })
})