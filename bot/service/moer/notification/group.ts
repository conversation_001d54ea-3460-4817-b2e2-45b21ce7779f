// 在通知群内通知对应的客服处理消息
import { JuziAPI } from '../../../lib/juzi/api'
import { Config } from '../../../config/config'
import { IWecomMsgType } from '../../../lib/juzi/type'
import logger from '../../../model/logger/logger'

export class GroupNotification {
  public static async notify(message: string, groupId?: string) {
    await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string, groupId ? groupId : Config.setting.wechatConfig?.notifyGroupId as string, {
      type: IWecomMsgType.Text,
      text: message
    })
  }

  // 用于 人工介入 账号，周四后 回复通知
  public static async notifyUserReply(userId: string, userMessage: string) {
    try {
      const contact = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
      let contactName = userId
      if (contact) {
        contactName = contact.name
      }

      if (contactName === '当下烘焙王小慧') {
        return
      }

      await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string,  Config.setting.wechatConfig?.notifyGroupId as string, {
        type: IWecomMsgType.Text,
        text: `${contactName}有新消息，请注意查看\n${userMessage}`
      })
    } catch (e) {
      logger.error('周五后客户回复通知出错', e)
    }
  }
}