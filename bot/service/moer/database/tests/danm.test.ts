import { DanmuDB } from '../danmu'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    await DanmuDB.saveDanmu([{
      liveId: '5661893',
      content: '心怀天下',
      time: 1744288148803,
      userId: '1053865',
      userName: '赛丽达尔',
      courseNo: 61,
      sendTime: new Date('2025-04-10T12:29:08.803Z'),
      day: 4
    },
    {
      liveId: '5661893',
      content: '心怀天下',
      time: 1744288148861,
      userId: '1053621',
      userName: '墨宝0001',
      courseNo: 61,
      sendTime: new Date('2025-04-10T12:29:08.861Z'),
      day: 4
    }])
  }, 60000)
})