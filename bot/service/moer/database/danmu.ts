import { PrismaMongoClient } from '../../../model/mongodb/prisma'

export interface Danmu {
  liveId: string
  content: string
  time: number
  userId: string
  userName: string
  courseNo?: number
  sendTime?: Date
  day?: number
}

export class DanmuDB {
  public static async saveDanmu(damus: Danmu[]) {
    return PrismaMongoClient.getInstance().danmu.createMany({
      data: damus
    })
  }

  public static async getDanmusByMoerId(moerId: string) {
    return PrismaMongoClient.getInstance().danmu.findMany({
      where: {
        userId: moerId
      },
      orderBy: {
        time: 'asc'
      }
    })
  }


  public static async getDanmusByMoerIdAndLiveId(moerId: string, liveId: string) {
    return PrismaMongoClient.getInstance().danmu.findMany({
      where: {
        userId: moerId,
        liveId: liveId
      },
      orderBy: {
        time: 'asc'
      }
    })
  }
}