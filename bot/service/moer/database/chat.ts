import { IChatState } from '../storage/chat_state_store'
import { PrismaMongoClient } from '../../../model/mongodb/prisma'
import { Config } from '../../../config/config'

export interface Chat {
    id: string
    round_ids: string[]
    contact: {
        wx_id: string
        wx_name: string
    }
    wx_id: string
    created_at: Date | null
    chat_state: IChatState
    moer_id?: string
    is_human_involved?:boolean
    course_no?: number
    is_stop_group_push?: boolean
    is_test?: boolean // 是否为测试账号
}

export class ChatDB {
  public static async updateMoerIdAndCourseNo(chat_id: string, moer_id: string, course_no: number) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat_id
      },
      data: {
        moer_id,
        course_no,
      }
    })
  }

  public static async removeById(chat_id: string) {
    return PrismaMongoClient.getInstance().chat.delete({
      where: {
        id: chat_id
      }
    })
  }



  public static async updateState(chat_id: string, chatState: IChatState) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat_id
      },
      data: {
        chat_state: chatState
      }
    })
  }

  public static async create(chat: Chat): Promise<Chat> {
    // @ts-ignore fuck you, primsa
    return PrismaMongoClient.getInstance().chat.create({
      data: chat
    })
  }

  public static async pushRoundId(chatId: string, roundId: string) {
    if (!await this.getById(chatId)) {
      return
    }

    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        round_ids: {
          push: roundId
        }
      }
    })
  }

  public static async getById(id: string): Promise<Chat | null> {
    // @ts-ignore fuck you, primsa
    return PrismaMongoClient.getInstance().chat.findUnique({
      where: {
        id
      }
    })
  }

  public static async deleteById(id: string) {
    return PrismaMongoClient.getInstance().chat.delete({
      where: {
        id
      }
    })
  }

  public static async isHumanInvolvement(chatId: string): Promise<boolean> {
    const chat = await PrismaMongoClient.getInstance().chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return Boolean(chat.is_human_involved)
    }

    return false
  }

  public static async setHumanInvolvement(chatId: string, isHumanInvolved: boolean) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        is_human_involved: isHumanInvolved
      }
    })
  }

  public static async getCourseNo(chatId: string): Promise<number | null> {
    // @ts-ignore fuck you, primsa
    const chat = await PrismaMongoClient.getInstance().chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return chat.course_no
    }

    return null
  }


  public static async getByMoerId(moerId: string): Promise<Chat | null> {
    if (!moerId) {
      throw new Error('moer_id is required')
    }

    if (Config.setting.wechatConfig?.id && !Config.setting.eventForward) {
      // @ts-ignore fuck you, primsa
      return PrismaMongoClient.getInstance().chat.findFirst({
        where: {
          moer_id: moerId,
          wx_id: Config.setting.wechatConfig?.id as string
        }
      })
    } else {
      // @ts-ignore fuck you, primsa
      return PrismaMongoClient.getInstance().chat.findFirst({
        where: {
          moer_id: moerId
        }
      })
    }
  }

  static async removeMoerId(chat_id: string) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat_id
      },
      data: {
        moer_id: null
      }
    })
  }

  static async setStopGroupPush(chatId: string, stopPush: boolean) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        is_stop_group_push: stopPush
      }
    })
  }

  static async updateContact(chatId: string, userId: string, name: string) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        contact: {
          wx_id: userId,
          wx_name: name
        }
      }
    })

  }
}