import { DouyinParser } from './parser'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const douyinTexts = [
      '3.38 复制打开抖音，看看【Ai算法工程师Future的作品】深度学习话题：学深度学习是否要先学习机器学习呢？ ... https://v.douyin.com/iFUJpAw1/ 07/23 ATY:/ <EMAIL> ',
      '7.69 复制打开抖音，看看【Ai算法工程师Future的作品】逐行逐字的代码解读网站分享！ # 深度学习算法 #... https://v.douyin.com/iFURJQsS/ <EMAIL> yGV:/ 07/10 ',
      '9.20 复制打开抖音，看看【Ai算法工程师Future的作品】对于神经网络，硕士博士不需要弄明白原理，只需要应用... https://v.douyin.com/iFUdw3v2/ GiC:/ 03/04 <EMAIL> ',
      '4.30 复制打开抖音，看看【Ai算法工程师Future的作品】强烈给大家安利一个遥感深度学习方向的宝藏网站！ #... https://v.douyin.com/iFUdEUuU/ 06/21 <EMAIL> YZZ:/ ',
      '0.28 复制打开抖音，看看【Ai算法工程师Future的作品】人工智能深度学习领域三大入门神课完整版笔记！ # ... https://v.douyin.com/iFUR5aeT/ DUY:/ <EMAIL> 08/04 ',
      '7.41 复制打开抖音，看看【Ai算法工程师Future的作品】【练手+复试+毕设必备】最优质的13个深度学习入门... https://v.douyin.com/iFURyoVY/ <EMAIL> 07/11 QKW:/ ',
      '1.51 复制打开抖音，看看【Ai算法工程师Future的作品】强烈建议所有神经网络初学者来阅读这篇24年的新论文... https://v.douyin.com/iFURqvqc/ <EMAIL> 06/02 uSY:/ ',
      '8.92 复制打开抖音，看看【Ai算法工程师Future的作品】不愧是计算机视觉领军人物，时隔一年，YOLO V9... https://v.douyin.com/iFURaJxM/ 09/17 <EMAIL> FhO:/ '
    ]

    douyinTexts.forEach((text) => {
      if (DouyinParser.isDouyinForward(text)) {
        const parsed = DouyinParser.getDouyinForwardText(text)
        console.log(parsed)
      }
    })
  })
})