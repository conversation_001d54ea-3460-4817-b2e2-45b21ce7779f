import { JuZiWecomClient } from '../../juzi/client'
import { Config } from '../../../config/config'
import { IMessageType, MessageType } from '../../../service/message/message'
import { MsgTypeEnum, WechatContact, WechatGroup } from '../event/type'


interface ISendMsgPayload {
    content: any
    msgType: MsgTypeEnum
    wechatAccountId: string
    wechatFriendId?: string
    wechatChatroomId?: string
}

export class AoChuang {
  private static _client: JuZiWecomClient | null = null

  public static get client() {
    if (this._client === null) {
      this._client = new JuZiWecomClient()
    }
    return this._client
  }

  public static async getWechatAccountList() {
    const accountId = '886a1741c2324953ae41bc8c6bca0fce'
    const url = `/api/v1/accounts/${accountId}/wechatAccounts`

    return await this.client.get(url, {})
  }

  public static async sendMsg(toUser: string, msg: MessageType, toGroup?: boolean) {
    let url =  '/api/v1/wechat/friendMessages'
    if (toGroup) {
      url = '/api/v1/wechat/chatroomMessages'
    }

    const wechatAccountId = Config.setting.wechatConfig?.id as string

    const defaultPayload: ISendMsgPayload = {
      'content': typeof msg === 'string' ? msg : msg.content,
      'msgType': MsgTypeEnum.Text,
      'wechatAccountId': wechatAccountId,
      'wechatFriendId': toUser
    }

    if (toGroup) {
      delete defaultPayload.wechatFriendId
      defaultPayload.wechatChatroomId = toUser
    }

    if (typeof msg === 'string') {
      return await this.client.post(url, defaultPayload)
    }

    switch (msg.type) {
      case IMessageType.Text:
        return await this.client.post(url, defaultPayload)
      case IMessageType.Image:
        defaultPayload.msgType = MsgTypeEnum.Image
        return await this.client.post(url, defaultPayload)
      case IMessageType.CustomEmoticon:
        defaultPayload.msgType = MsgTypeEnum.CustomEmoticon
        return await this.client.post(url, defaultPayload)

      case IMessageType.GroupAt: // @群成员
        defaultPayload.content = {
          'text': msg.content,
          'atId': msg.at_id
        }

        defaultPayload.msgType = MsgTypeEnum.TextWithAt

        return await this.client.post(url, defaultPayload)
    }
  }

  // 获取好友信息
  public static async getFriendInfo(wechatFriendId: string): Promise<WechatContact> {
    const url = `/api/v1/wechatFriends/${wechatFriendId}`
    const response = await this.client.get<WechatContact>(url, {})
    if (response.status === 200) {
      return response.data
    } else {
      throw new Error(`获取好友信息失败${  response.status  }${response.data}`)
    }
  }

  public static async getAllFriends() {
    const allFriends: WechatContact[] = []
    let friends = await this.getFriends()
    allFriends.push(...friends)
    while (friends.length > 0) {
      friends = await this.getFriends(friends[friends.length - 1].id)
      allFriends.push(...friends)
    }
    // 根据 wechatId 做一下去重
    return allFriends
  }

  public static async getAllGroups() {
    const allGroups: WechatGroup[] = []
    let groups = await this.getGroups()
    allGroups.push(...groups)
    while (groups.length > 0) {
      groups = await this.getGroups(groups[groups.length - 1].id)
      allGroups.push(...groups)
    }
    // 根据 wechatId 做一下去重
    return allGroups
  }

  private static async getFriends(maxWechatFriendId?: string) {
    const url = '/api/v1/wechatFriends'
    const response = await this.client.get(url, {
      maxWechatFriendId: maxWechatFriendId ? maxWechatFriendId : ''
    })
    return response.data as WechatContact[]
  }

  public static async getGroups(maxWechatChatRoomId?: string) {
    const url = '/api/v1/wechatChatrooms'
    const response = await this.client.get(url, {
      maxWechatChatroomId: maxWechatChatRoomId ? maxWechatChatRoomId : ''
    })
    return response.data as WechatGroup[]
  }

  public static async inviteFriendToGroup(wechatChatRoomId: string, wechatFriendIds: string[]) {
    const url = `/api/v1/wechatChatrooms/${wechatChatRoomId}/inviteMembers`
    const response = await this.client.post(url, {
      'wechatAccountId': Config.setting.wechatConfig?.id,
      'wechatFriendIds': wechatFriendIds
    })

    return response.data
  }

  public static async updateUserAlias(friendId: string, alias: string) {
    const url = `/api/v1/wechatFriends/${friendId}/remark`
    const response = await this.client.put(url, {
      'remark': alias
    })

    return response.data
  }


  static async getContactByFriendId(id: string) {
    const url = `/api/v1/wechatFriends/${id}`
    const response = await this.client.get(url, {
    })

    return response.data as WechatContact
  }

  /**
     * 通过好友请求
     */
  static async acceptFriendRequest(friendId: string): Promise<{taskId: string}> {
    const url = `/api/v1/wechatFriends/${friendId}/acceptFriend`
    const response = await this.client.put(url, {
    })

    return response.data as {taskId: string}
  }

  static async getHDImage(messageId: string, wechatTime: number) {
    const url = `/api/v1/wechat/friendMessage/downloadImage/${messageId}/${wechatTime}`
    const response = await this.client.get(url, {
    })

    return response.data as {taskId: string}
  }

  // public async getFileSize(url: string): Promise<number> {
  //     const head = await this.head(url)
  //     return parseInt(head['content-length'] as string, 10)
  // }

}

