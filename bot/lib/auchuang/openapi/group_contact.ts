import { AoChuang } from './a<PERSON><PERSON>'
import { WechatContact, WechatGroup } from '../event/type'
import { MessageType } from '../../../service/message/message'
import { Config } from '../../../config/config'

export class AoChuangWechatGroupContact {
  private static idMap: Map<string, WechatGroup> = new Map()
  private static wechatNameMap: Map<string, WechatGroup> = new Map()
  private static wechatAliasMap: Map<string, WechatGroup> = new Map()
  private static chatRoomIdMap: Map<string, WechatGroup> = new Map()

  public static async pullContacts() {
    const groups = await AoChuang.getAllGroups()

    // 构建 Map
    for (const group of groups) {
      if (group.wechatAccountId !== Config.setting.wechatConfig?.id) { // TODO 只保留当前微信号的好友
        continue
      }

      if (group.nickname) {
        this.wechatNameMap.set(group.nickname, group)
      }

      if (group.id) {
        this.idMap.set(group.id, group)
      }

      if (group.conRemark) {
        this.wechatAliasMap.set(group.conRemark, group)
      }

      if (group.chatroomId) {
        this.chatRoomIdMap.set(group.chatroomId, group)
      }
    }
  }

  public static async getContactByName(name: string) {
    await this.pullContacts()

    return this.wechatNameMap.get(name)
  }

  public static async getContactById(id: string) {
    await this.pullContacts()

    return this.idMap.get(id)
  }

  static async getContactByChatRoomId(groupName: string) {
    await this.pullContacts()
    return this.chatRoomIdMap.get(groupName)
  }

  static async isInvitedToLargeGroup(id: string) {
    const group = await this.getContactByChatRoomId(id)
    if (!group) {
      console.log('未找到群组：', id)
      return false
    }

    if (group.members.length >= 10) {
      return true
    }
    return false
  }
}