import axios from 'axios'
import fs from 'fs'
import querystring from 'querystring'
import { Retry } from '../retry/retry'

export class HttpHelper {
  /**
     * 从 URL 下载网络文件， URL 必须 escaped
     * @param url
     * @param filePath
     */
  static async downloadFile(url: string, filePath: string) {
    try {
      // axios.defaults.adapter = require('axios/lib/adapters/http')
      // 使用 axios 下载文件
      const response = await axios.get(url, { responseType: 'stream' })
      const fileWriteStream = fs.createWriteStream(filePath)

      return new Promise<void>((resolve) => {
        response.data.pipe(fileWriteStream)
          .on('finish', () => {
            resolve()
          })
          .on('error', (err: any) => {
            fileWriteStream.close()
            throw err
          })
      })
    } catch (error) {
      throw new Error(`下载网络文件失败。${error}`)
    }
  }

  /**
     * 将参数列表转为对象，如 https://baidu.com?a=b
     * 返回
     * {
     *    a: 'b'
     * }
     * @param url
     */
  static covertQueryToObject(url: string) {
    const query = new URL(url)
    return querystring.parse(query.searchParams.toString())
  }

  /**
   *
   * @param requestUri
   * @param headers 如果不需要则传入undefined
   * @param params
   */
  public static async get<T>(requestUri: string, headers?: object, params?: object) {

    try {
      return await Retry.retry(3, async () => {
        return await axios.get<T>(requestUri, {
          headers: headers,
          params: params,
        })
      })
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }

  /**
   *
   * @param requestUri
   * @param payload
   * @param headers 如果不需要则传入undefined
   * @param params
   */
  public static async post<T>(requestUri: string, payload: any, headers?: any, params?: any) {
    try {
      const response = await Retry.retry(3, async () => {
        return await axios.post<T>(requestUri, payload, {
          headers:headers,
          params: params,
        })
      }, {
        delayFunc: (count) => count * 200
      })

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${requestUri}${e}`)
    }
  }

  /**
   *
   * @param requestUri
   * @param payload
   * @param headers 如果不需要则传入undefined
   * @param params
   */
  public static async put<T>(requestUri: string, payload: any, headers?: any, params?: any) {

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.put<T>(requestUri, payload, {
          headers:headers,
          params: params,
        })
      }, {
        delayFunc: (count) => count * 200
      })

      return { status: response.status, data: response.data }
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败${  requestUri  }${e}`)
    }
  }


}