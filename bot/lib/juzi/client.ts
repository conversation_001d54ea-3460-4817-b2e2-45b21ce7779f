import axios from 'axios'
import { Retry } from '../retry/retry'
import { Config } from '../../config/config'


interface ApiResponse<T> {
    status: number
    data: T
}

export class JuZiWecomClient {
  private enterpriseBaseURL: string // 企业 URL
  private orgBaseURL: string // 小组 URL

  private readonly token: string

  constructor() {
    this.enterpriseBaseURL = Config.setting.juziWecom.enterpriseBaseUrl
    this.orgBaseURL = Config.setting.juziWecom.orgBaseUrl
    this.token = Config.setting.juziWecom.token
  }

  private getFullUrl(requestUri: string, useOrgUrl?: boolean) {
    let base = useOrgUrl ? this.orgBaseURL : this.enterpriseBaseURL
    // 确保末尾有 slash，否则 new URL 会把最后一段当“文件”丢掉
    if (!base.endsWith('/')) {
      base = `${base}/`
    }

    // 统一用 URL 拼接，能容错多斜杠问题
    return new URL(requestUri, base).toString()
  }

  async get<T>(requestUri: string, payload: object, useOrgUrl?: boolean): Promise<ApiResponse<T>> {
    payload = Object.assign(payload, { token: this.token })

    const url = this.getFullUrl(requestUri, useOrgUrl)

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.get<T>(url, {
          params: payload,
        })
      })

      JuZiWecomClient.logError(requestUri, payload, response)

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }

  async post<T>(requestUri: string, payload: any, useOrgUrl?: boolean): Promise<ApiResponse<T>> {
    const url = this.getFullUrl(requestUri, useOrgUrl)

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.post<T>(url, payload,  {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      JuZiWecomClient.logError(requestUri, payload, response)

      return response
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }


  async put<T>(requestUri: string, payload: any, useOrgUrl?: boolean): Promise<ApiResponse<T>> {
    const url = this.getFullUrl(requestUri, useOrgUrl)

    try {
      const response = await Retry.retry(3, async () => {
        return await axios.put<T>(url, payload, {
          params: {
            token: this.token
          }
        })
      }, {
        delayFunc: (count) => count * 200
      })

      return { status: response.status, data: response.data }
    } catch (e: any) {
      if (e.response) {
        console.error(JSON.stringify(e.response.data, null, 2))
      }

      throw new Error(`请求失败：${  requestUri  }\n${e}`)
    }
  }


  private static logError<T>(requestUri: string, payload: any, response: ApiResponse<T>) {
    const data = response.data as any
    if (data && data.errcode && data.errcode !== 0) {
      console.log(`请求失败：${  requestUri  }\n请求参数：${JSON.stringify(payload)}\n返回结果：${JSON.stringify(data)}`)
    }
  }
}