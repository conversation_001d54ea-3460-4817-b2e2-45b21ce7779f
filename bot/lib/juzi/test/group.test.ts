import { <PERSON>ziAP<PERSON> } from '../api'
import { Config } from '../../../config/config'
import { MessageSender } from '../../../service/moer/components/message/message_send'
import { ChatHistoryService } from '../../../service/moer/components/chat_history/chat_history'
import { IWecomMsgType } from '../type'
import { loadConfigByWxId } from '../../../../test/tools/load_config'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      id: '1688856007524229',
      botUserId: 'HaHaHa',
      name: '暴叔',
      notifyGroupId: 'R:10840704064745792',

      classGroupId: 'xx',
      courseNo: 1
    }
  })

  it('查询群消息', async () => {
    const groupDetail = await JuziAP<PERSON>.groupDetail({
      imBotId: '1688857443695295',
      imRoomId: 'R:10963925875419526'
    })

    console.log(JSON.stringify(groupDetail.data, null, 4))
  }, 60000)


  it('查询是否在群内', async () => {
    console.log(JSON.stringify(await JuziAPI.isInGroup({
      imBotId: '1688854546332791',
      imRoomId: 'R:10926892688487567',
      imContactId: '7881300846030208'
    }), null, 4))
  }, 60000)

  it('拉群测试2', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688855548631328')

    const response = await JuziAPI.addToGroup({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      contactWxid: '7881299592973278',
      roomWxid: Config.setting.wechatConfig?.classGroupId as string
    })

    console.log(JSON.stringify(response, null, 4))
  }, 60000)

  it('拉群', async () => {
    console.log(JSON.stringify(await JuziAPI.createRoom({
      botUserId: 'HaHaHa',
      userIds: ['ZhangMeng'],
      name: '澳洲 交流',
      greeting: '拉群测试',
      externalUserIds: ['wmagdSKgAAwax00OkOQH81zvMYndO5sQ']
    }), null, 4))
  }, 30000)

  it('发送群聊消息', async () => {
    await JuziAPI.sendMsg({
      imBotId: Config.setting.wechatConfig?.id as string,
      imContactId: undefined,
      imRoomId: 'R:10947148725369922',
      msg: {
        type: IWecomMsgType.Text,
        text: `聊天记录：
        
SYQ：暴叔我g了，成绩不行。可以约个电话的，看看我怎么稳吗啊
暴叔：咱们是高考生吗
SYQ：没，我现在高二，明年高考。

客户画像：
预算：100 万
当前学历水平：本科
GPA：73
目标：出国读研
城市：烟台
学校：滑铁卢
是否留学：是

- 由企微微工具整理生成
`
      }
    })
  }, 60000)

  it('1', async () => {
    const chat_id = '7881301047907394_168885513550980'
    await MessageSender.sendById({
      user_id: '1688854611469274',
      chat_id: chat_id,
      ai_msg: `以下是 麦子 和 暴叔的聊天记录:
${await ChatHistoryService.getFormatChatHistoryByChatId(chat_id)}`,
    }, { notAddBotMessage: true })
  }, 60000)

  it('customer info', async () => {
    console.log(JSON.stringify(await JuziAPI.getMemberInfo('ZhangMeng'), null, 4))
  }, 60000)

  it('xx', async () => {
    await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string, 'R:10735753744477170', {
      type: IWecomMsgType.Text,
      text: 'hi'
    })
  }, 60000)

  it('', async () => {
    console.log(JSON.stringify(await JuziAPI.externalIdToWxId('<EMAIL>'), null, 4))
  }, 60000)
})