import { generate, uuid } from 'short-uuid'

export class UUID {
  private static readonly uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i
  private static readonly shortUUIDRegex = /^[0-9a-zA-Z]{22}$/

  public static short(): string {
    return generate()
  }

  public static v4(): string {
    return uuid()
  }

  public static isUUID(uuid: string): boolean {
    return this.uuidRegex.test(uuid)
  }

  public static isShortUUID(uuid: string): boolean {
    return this.shortUUIDRegex.test(uuid)
  }

}
