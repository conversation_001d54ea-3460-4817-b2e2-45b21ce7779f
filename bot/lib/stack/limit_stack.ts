export class LimitedStack<T> {
  private stack: T[] = []
  private maxSize: number

  constructor(maxSize: number, initialElements?: T[]) {
    if (maxSize <= 0) {
      throw new Error('Max size must be greater than 0')
    }
    this.maxSize = maxSize

    if (initialElements) {
      // 如果传入了初始数组，裁剪数组以符合最大大小限制
      this.stack = initialElements.slice(-maxSize)
    }
  }

  // 添加元素到栈顶
  push(item: T): void {
    if (this.stack.length >= this.maxSize) {
      // 删除栈底元素
      this.stack.shift()
    }
    this.stack.push(item)
  }

  // 移除并返回栈顶元素
  pop(): T | undefined {
    return this.stack.pop()
  }

  // 查看栈顶元素
  peek(): T | undefined {
    return this.stack[this.stack.length - 1]
  }

  // 检查栈是否为空
  isEmpty(): boolean {
    return this.stack.length === 0
  }

  // 获取栈的大小
  size(): number {
    return this.stack.length
  }

  // 获取栈的所有元素
  getElements(): T[] {
    return [...this.stack]
  }
}
