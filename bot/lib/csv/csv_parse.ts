import csv_parser from 'csv-parse'
import { FileHelper } from '../file'
import fs from 'fs'
import * as Papa from 'papaparse'


type JsonRow = { [key: string]: { [col: string]: string } }

export class CsvHelper {
  public static async read(csvPath: string): Promise<string[][]> {
    const stream = await FileHelper.readFileAsStream(csvPath)

    const table: string[][] = []
    const parser = stream.pipe(csv_parser({
      bom: true,
      relax: true, // 允许单引号单独出现 allow quoted fields
      relax_column_count: true, // 允许每行列数不一致
    }))

    let maxColumns = 0
    for await (const row of parser) {
      if (row.length > maxColumns) {
        maxColumns = row.length
      }

      if (row.join('').trim() === '') {
        continue
      }

      table.push(row)
    }

    for (const row of table) {
      if (row.length < maxColumns) {
        const padding = maxColumns - row.length
        for (let i = 0; i < padding; i++) {
          row.push('')
        }
      }
    }
    return table
  }


  public static write(csvPath: string, data: Record<string, any>[]) {
    const csvString = Papa.unparse(data)
    fs.writeFileSync(csvPath, csvString)
  }

  /**
   * 将 JSON 数据转换为 CSV，key 作为行名，sub_key 作为列名。比如：
   * {
   *     "小张": {
   *         "姓名": "小张",
   *         "身高": 175
   *     }
   * }
   * @param filePath
   * @param jsonData JSON 数据
   * @param columnNames
   * @returns 返回 CSV 字符串
   */
  public static write2DJson(filePath: string, jsonData: JsonRow, columnNames: string[]) {
    // 创建 CSV 数据的二维数组
    const csvData: string[][] = []

    // 添加列名行：第一列是 'Key'
    csvData.push(['', ...columnNames])

    // 遍历所有的键，创建每一行数据
    Object.keys(jsonData).forEach((key) => {
      const row = [key, ...Object.keys(jsonData[key]).map((col) => jsonData[key][col] || '')]
      csvData.push(row)
    })

    // 使用 PapaParse 转换为 CSV 格式
    this.write(filePath, csvData)
  }

}
