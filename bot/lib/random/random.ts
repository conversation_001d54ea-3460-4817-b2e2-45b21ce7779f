export class RandomHelper {
  /**
   * 随机从数组中选取一个元素
   * @param arr
   */
  public static select<T>(arr: T[]) {
    return arr[Math.floor(Math.random() * arr.length)]
  }

  /**
   * 从数组中随机选取指定数量的元素
   * @param array - 源数组
   * @param count - 要选取的数量
   * @returns 随机选取的元素数组
   */
  public static randomSelect<T>(array: T[], count: number): T[] {
    if (count >= array.length) {
      return [...array]
    }

    // Fisher-Yates 洗牌算法的变体，只洗前 count 个
    const result = [...array]
    for (let i = 0; i < count; i++) {
      const j = Math.floor(Math.random() * (result.length - i)) + i;

      [result[i], result[j]] = [result[j], result[i]]
    }

    return result.slice(0, count)
  }



  /**
   * 根据不同 index 的权重，选取一个 index。 参考：https://leetcode.cn/problems/cuyjEf/description/
   * @param weights  w[i] 代表下标 i 的权重， 随机地获取下标 i，选取下标 i 的概率与 w[i] 成正比。
   */
  public static pickIndexByWeights(weights: number[]): number {
    const weightPrefixSums: number[] = []
    let totalWeight: number = 0

    for (const weight of weights) {
      totalWeight += weight
      weightPrefixSums.push(totalWeight)
    }

    function binarySearch(target: number): number {
      let left = 0
      let right = weightPrefixSums.length - 1

      while (left < right) {
        const mid = Math.floor((left + right) / 2)
        if (weightPrefixSums[mid] < target) {
          left = mid + 1
        } else {
          right = mid
        }
      }

      return left
    }

    const randomWeight = Math.random() * totalWeight
    return binarySearch(randomWeight)
  }

  static randomString(number: number) {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    const maxPos = chars.length
    let pwd = ''
    for (let i = 0; i < number; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return pwd
  }
}