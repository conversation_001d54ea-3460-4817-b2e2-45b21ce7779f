export interface IDateFormat {
  date: string
  time: string
}

/**
 * 字符串类型辅助工具
 */
export class StringHelper {
  public static replaceMultipleBlankLines(input: string): string {
    return input.replace(/(\r?\n\s*){2,}/g, '\n\n')
  }


  /**
   * 句首首字母大写
   * @param text
   * @constructor
   */
  public static upperCaseFirstLetter(text: string): string {
    if (text.length > 0) {
      const firstCharAscii = text.charCodeAt(0)
      if (firstCharAscii >= 97 && firstCharAscii <= 122) {
        return `${text[0].toUpperCase()}${text.substr(1)}`
      }
    }
    return text
  }

  /**
   * 判断是否是邮箱
   */
  public static isEmailString(str: string): boolean {
    if (!str) {
      return false
    }

    return str.indexOf('@') > 0 && str.indexOf('.') > 0
  }

  /**
   * 判断是否是手机号
   * @param str
   */
  public static isMobileString(str: string): boolean {
    if (!str) {
      return false
    }

    return /^\d+$/.test(str)
  }

  public static replaceRegexLetter(str: any): string {
    if (!str) {
      return ''
    }

    let regexStr = str

    if (typeof str !== 'string') {
      regexStr = str.toString()
    }

    const result: string[] = []

    // 替换包含正则的特殊字符
    for (let i = 0; i < regexStr.length; i++) {
      const strCharCode = regexStr.charCodeAt(i)

      if (strCharCode < 48) {
        result.push('\\')
      }

      result.push(regexStr[i])
    }

    return result.join('')
  }

  /**
   * 是否是多选
   */
  public static isMultiSelect(text: string): boolean {
    return text.split(',').length > 1
  }

  /**
   * 是否是数字
   */
  public static isNumber(text: string): boolean {
    if (!text.trim()) {
      // 避免 Number('') = true
      return false
    }

    if (text.startsWith('0') && text.length > 1) {
      // isNumber('01') = false
      return false
    }

    return !isNaN(Number(text))
  }

  /**
   * 是否是 Boolean 类型字符串
   */
  public static isBoolean(text: string): boolean {
    return ['yes', 'no', 'true', 'false', '是', '否', '已勾选', '未勾选', '✅', '❎', '☑', '☐'].includes(
      text.toLowerCase(),
    )
  }

  /**
   * 是否是日期字符串
   */
  public static isDate(text: string): boolean {
    if (!isNaN(Number(text))) {
      // 排除纯数字
      return false
    }

    return !isNaN(Date.parse(text)) || this.isChineseDate(text)
  }

  public static isChineseDate(text: string): boolean {
    return /^\d{4}(\s*)年(\s*)\d{1,2}(\s*)月(\s*)\d{1,2}(\s*)日(\s*)$/.test(text)
  }

  /**
   * 是否是邮箱字符串
   */
  public static isEmail(text: string): boolean {
    return /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
      text,
    )
  }

  /**
   * 是否是手机号码，这个要严格点，要不容易被判断为大整数。 这里只严格检查中国和美国手机号，其他地区暂不考虑。
   */
  public static isPhone(text: string): boolean {
    return (
      /^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(text) ||
      /^(\+1)?\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(text)
    )
  }

  /**
   * 是否是网站链接
   */
  public static isUrl(text: string): boolean {
    return /^(http|https):\/\/[^\s]+$/.test(text)
  }

  /**
   * 格式化日期为 YYYY/MM/DD 和 HH:MM
   * @param date
   */
  public static formatDate(date: Date): IDateFormat {
    const hour = date.getHours() < 10 ? `0${date.getHours()}` : date.getHours()
    const minute = date.getMinutes() < 10 ? `0${date.getMinutes()}` : date.getMinutes()
    const month = date.getMonth() + 1 < 10 ? `0${date.getMonth() + 1}` : date.getMonth() + 1
    const day = date.getDate() < 10 ? `0${date.getDate()}` : date.getDate()

    return {
      date: `${date.getFullYear()}/${month}/${day}`,
      time: `${hour}:${minute}`,
    }
  }

  // example: xxxx年x月x日
  public static convertChineseDateToDate(text: string): Date {
    const year = Number(text.split('年')[0])
    const month = Number(text.split('年')[1].split('月')[0])
    const day = Number(text.split('月')[1].split('日')[0])

    return new Date(year, month - 1, day)
  }

  // 判断千分符字符串
  public static isThousandth(text: string) {
    return /^\d+(,\d{3})*(\.\d+)?$/.test(text)
  }

  // 识别以 % 结尾的数字
  public static isPercent(text: string) {
    return /^\d+(?:\.\d+)?%$/.test(text)
  }

  /**
   * 从文本中提取数字并转换为对应的文字描述
   * 支持多种分隔符格式：1-5-8、1，5，8、1/5/8、1 5 8
   * 处理极端情况：1018 → 10, 1, 8
   * @param text 输入文本
   * @returns 提取的数字对应的文字描述数组
   */
  public static extractNumbersToText(text: string): string[] {
    const numbers = this.extractNumbers(text)
    return this.numbersToText(numbers)
  }

  /**
   * 从文本中提取数字
   * @param text 输入文本
   * @returns 提取的数字数组
   */
  public static extractNumbers(text: string): number[] {
    const numbers: number[] = []

    // 1. 首先检查是否有分隔符格式
    const hasSeparators = /[\s\-，,/]/.test(text)

    if (hasSeparators) {
      // 提取所有数字，不管分隔符
      const allNumbers = text.match(/\d+/g)
      if (allNumbers) {
        for (const numStr of allNumbers) {
          const num = parseInt(numStr, 10)
          if (num >= 1 && num <= 13) {
            numbers.push(num)
          }
        }
      }
    } else {
      // 2. 处理没有分隔符的情况：连续数字如1018 → 10, 1, 8
      const consecutiveNumbers = text.match(/\d+/g)
      if (consecutiveNumbers) {
        for (const numStr of consecutiveNumbers) {
          if (numStr.length > 2) {
            // 只有当数字长度大于2且包含有效数字时才拆分
            const splitNumbers = this.splitConsecutiveNumbers(numStr)
            if (splitNumbers.length > 0) {
              numbers.push(...splitNumbers)
            }
          } else {
            const num = parseInt(numStr, 10)
            if (num >= 1 && num <= 13) {
              numbers.push(num)
            }
          }
        }
      }
    }

    // 3. 最后的备选方案：处理单独的数字
    if (numbers.length === 0) {
      const singleNumbers = text.match(/\b([1-9]|1[0-3])\b/g)
      if (singleNumbers) {
        numbers.push(...singleNumbers.map((n) => parseInt(n, 10)))
      }
    }

    // 去重并排序
    return [...new Set(numbers)].filter((n) => n >= 1 && n <= 13).sort((a, b) => a - b)
  }

  /**
   * 拆分连续数字，尝试识别有效的1-13范围内的数字
   * 例如：1018 → [10, 1, 8]
   * @param numStr 连续数字字符串
   * @returns 拆分后的数字数组
   */
  private static splitConsecutiveNumbers(numStr: string): number[] {
    const numbers: number[] = []
    const validNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13]

    // 如果数字本身就是有效的，不拆分
    const wholeNumber = parseInt(numStr, 10)
    if (validNumbers.includes(wholeNumber)) {
      return [wholeNumber]
    }

    // 尝试不同的拆分方式
    for (let i = 0; i < numStr.length; i++) {
      for (let j = i + 1; j <= numStr.length; j++) {
        const substr = numStr.substring(i, j)
        const num = parseInt(substr, 10)
        if (validNumbers.includes(num)) {
          numbers.push(num)
        }
      }
    }

    // 如果没有找到任何有效数字，返回空数组
    if (numbers.length === 0) {
      return []
    }

    // 返回去重后的数字，优先保留较大的数字（如10优先于1和0）
    const uniqueNumbers = [...new Set(numbers)]

    // 对于像 "100" 这样的情况，如果原始数字看起来像一个完整的数字（比如100、200等），
    // 而不是客户想要表达的多个选择，就不拆分
    if (numStr.length === 3 && numStr.endsWith('00')) {
      return []
    }

    // 只有当能拆分出至少3个有效数字时，才认为是有效拆分
    if (uniqueNumbers.length < 3) {
      return []
    }

    // 如果包含10、11、12、13，优先保留这些
    const priorityNumbers = uniqueNumbers.filter((n) => n >= 10)
    const singleDigits = uniqueNumbers.filter((n) => n < 10)

    return [...priorityNumbers, ...singleDigits].sort((a, b) => a - b)
  }

  /**
   * 将数字转换为对应的文字描述
   * @param numbers 数字数组
   * @returns 文字描述数组
   */
  public static numbersToText(numbers: number[]): string[] {
    const numberToTextMap: Record<number, string> = {
      // 生活角色 (1-4)
      1: '职场奋斗者',
      2: '家庭管理者',
      3: '退休精进者',
      4: '修行者',
      // 冥想经验 (5-7)
      5: '纯小白',
      6: '接触过',
      7: '有基础',
      // 人生议题 (8-13)
      8: '情绪减压',
      9: '专注提升',
      10: '睡眠改善',
      11: '财富能量',
      12: '亲密关系',
      13: '灵性成长'
    }

    return numbers
      .filter((n) => n >= 1 && n <= 13)
      .map((n) => numberToTextMap[n])
      .filter((text) => text !== undefined)
  }

  /**
   * 设置一个字符串的前缀
   * setPrefix('345', '1234') === '12345'
   * @param source
   * @param prefix
   * @returns
   */
  public static setPrefix(source: string, prefix: string): string {
    for (let i = 0; i < prefix.length; i++) {
      if (source.startsWith(prefix.slice(i))) {
        return `${prefix.slice(0, i)}${source}`
      }
    }
    return `${prefix}${source}`
  }

  public static isEmpty(text?: string): boolean {
    if (text == null) {
      return true
    }

    return text.trim() === ''
  }

  /**
   * 获取随机字符串
   * @param length
   * @returns
   */
  public static getRandomString(length = 6): string {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    const maxPos = chars.length
    let pwd = ''
    for (let i = 0; i < length; i++) {
      pwd += chars.charAt(Math.floor(Math.random() * maxPos))
    }
    return pwd
  }

  /**
   * 判断是否为 shortUUID
   */
  public static isShortUUID(text: string): boolean {
    return /[0-9a-zA-Z]{22}/.test(text)
  }

  static removeStringBeforePrefix(prefix: string, s: string) {
    const idx = s.indexOf(prefix)
    // 如果找不到 symbol，则返回原字符串
    if (idx === -1) {
      return s
    }

    // 从 symbol 后面开始截取
    return s.substring(idx + prefix.length).trim()
  }
}
