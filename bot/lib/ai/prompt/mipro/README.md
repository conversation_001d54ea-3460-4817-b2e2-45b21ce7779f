# MIPRO V2 自动化 Prompt 优化器

基于 DSPy 论文中的 MIPRO V2 算法实现的 TypeScript 版本，用于自动优化 Language Model 的 prompt。

## 🌟 特性

- **自动化 Few-shot 示例生成**: 通过 Bootstrap 方法自动筛选高质量示例
- **智能指令生成**: 使用 LLM 生成多样化的候选指令
- **贝叶斯优化**: 使用 TPE (Tree-structured Parzen Estimator) 算法高效搜索最优参数
- **完整 XML 格式输出**: 包含指令和示例的完整格式化 prompt
- **灵活的评估指标**: 支持自定义评估函数
- **TypeScript 类型安全**: 完整的类型定义和 IntelliSense 支持

## 🚀 快速开始

### 基本使用

```typescript
import { MIPROv2Optimizer, createSampleDataset, semanticSimilarityMetric } from './mipro';
import { LLM } from '../../llm/LLM';

// 1. 准备训练数据
const trainset = [
  { question: "What is the capital of France?", answer: "Paris" },
  { question: "Who wrote Romeo and Juliet?", answer: "William Shakespeare" },
  // ... 更多训练数据
];

// 2. 创建你的程序
class MyProgram {
  // 实现 LMProgram 接口
  async call(input) { /* ... */ }
  updatePrompts(newPrompts) { /* ... */ }
  getPredictors() { /* ... */ }
}

// 3. 配置优化器
const optimizer = new MIPROv2Optimizer({
  metric: semanticSimilarityMetric,
  promptModel: new LLM({ model: 'gpt-4.1' }),
  taskModel: new LLM({ model: 'gpt-4.1' }),
  numTrials: 20,
  numCandidates: 5
});

// 4. 运行优化
const result = await optimizer.optimize(program, trainset);

// 5. 查看结果
console.log('最佳分数:', result.bestScore);
console.log('优化后的 Prompts:', result.bestPrompts);
```

### 快速优化

```typescript
import { quickOptimize } from './mipro';

const result = await quickOptimize(program, trainset, {
  numTrials: 10
});
```

## 📊 优化结果格式

优化器返回包含完整 prompt 信息的结果：

```typescript
interface OptimizedPrompt {
  predictorName: string;
  instruction: string;        // 优化后的指令
  examples: Example[];        // 优化后的示例
  fullPrompt: string;         // 完整的格式化 prompt
  score: number;              // 该 prompt 的评估分数
}
```

### 完整 Prompt 格式示例

```xml
<task_signature>question -> answer</task_signature>

<instructions>
Answer the question accurately and concisely based on your knowledge. 
Provide factual information and avoid speculation.
</instructions>

<examples>
<example_1>
<question>What is the capital of France?</question>
<answer>Paris</answer>
</example_1>

<example_2>
<question>Who wrote Romeo and Juliet?</question>
<answer>William Shakespeare</answer>
</example_2>
</examples>

<input>
{input}
</input>

Please provide your response:
```

## 🔧 配置选项

```typescript
interface MiproConfig {
  metric: Metric;                    // 评估函数
  promptModel: LLM;                  // 用于生成 prompt 的模型
  taskModel: LLM;                    // 用于执行任务的模型
  maxBootstrappedDemos?: number;     // 最大 bootstrap 示例数 (默认: 4)
  maxLabeledDemos?: number;          // 最大标注示例数 (默认: 4)
  numTrials?: number;                // 优化试验次数 (默认: 20)
  minibatchSize?: number;            // 评估批次大小 (默认: 10)
  numCandidates?: number;            // 候选数量 (默认: 5)
}
```

## 📈 评估指标

### 内置指标

```typescript
// 精确匹配
import { exactMatchMetric } from './mipro';

// 包含关系匹配
import { containsMetric } from './mipro';

// 语义相似度 (使用 LLM 评估)
import { semanticSimilarityMetric } from './mipro';
```

### 自定义指标

```typescript
const customMetric: Metric = async (prediction, groundTruth) => {
  // 实现你的评估逻辑
  const score = yourEvaluationFunction(prediction, groundTruth);
  return score; // 返回 0-1 之间的分数
};
```

## 🏗️ 实现自己的程序

```typescript
import { LMProgram, Predictor, PredictorConfig } from './mipro/types';

class YourProgram implements LMProgram {
  private predictors: { [name: string]: Predictor } = {};

  constructor() {
    this.predictors['main'] = {
      name: 'main',
      signature: 'input -> output',
      config: {
        instruction: 'Initial instruction',
        demos: []
      }
    };
  }

  async call(input: any): Promise<{ prediction: any; trace: any }> {
    // 实现你的预测逻辑
    const prompt = this.buildPrompt(input);
    const response = await yourLLM.predict(prompt);
    
    return {
      prediction: { output: response },
      trace: { prompt, response }
    };
  }

  updatePrompts(newPrompts: { [name: string]: Partial<PredictorConfig> }): void {
    // 更新 prompt 配置
    for (const [name, config] of Object.entries(newPrompts)) {
      if (this.predictors[name]) {
        Object.assign(this.predictors[name].config, config);
      }
    }
  }

  getPredictors(): { [name: string]: Predictor } {
    return { ...this.predictors };
  }

  private buildPrompt(input: any): string {
    // 构建完整的 prompt
    // 可以使用 PromptFormatter 辅助工具
  }
}
```

## 🧪 运行示例

```bash
# 运行完整示例
npm run ts-node bot/lib/ai/prompt/mipro/usage_example.ts

# 运行测试
npm test -- mipro.test.ts
```

## 📚 算法原理

MIPRO V2 包含三个主要步骤：

1. **Bootstrap Demonstrations**: 在训练集上运行程序，筛选高分结果作为 few-shot 示例
2. **Propose Instructions**: 使用 LLM 基于任务信息生成多样化的指令候选
3. **Bayesian Optimization**: 使用 TPE 算法在"指令 × 示例"空间中搜索最优组合

### TPE 算法

Tree-structured Parzen Estimator 通过以下方式工作：
- 将历史试验分为"好"和"差"两组
- 用两个分布 l(x) 和 g(x) 分别建模
- 寻找 l(x)/g(x) 比率最大的点作为下一个候选

## 🔍 调试和监控

优化过程中会输出详细的日志：

```
🚀 Starting MIPRO V2 Optimization...
📚 Step 1: Bootstrapping few-shot examples...
   Generated 5 demo sets for qa
💡 Step 2: Proposing instruction candidates...
   Generated 5 instructions for qa
🎯 Step 3: Finding optimal parameters with TPE...
   Trial 1/20
     Score: 0.7500
   Trial 2/20
     Score: 0.8000
     🎉 New best score: 0.8000
...
✅ MIPRO V2 Optimization completed!
   Best score: 0.8500
   Total trials: 20
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
