/**
 * Tree-structured Parzen Estimator (TPE) 优化器
 * 基于 DSPy 论文中的贝叶斯优化算法实现
 */

import { Trial } from './types'

/**
 * 简单的统计函数实现
 */
class Statistics {
  static mean(values: number[]): number {
    if (values.length === 0) return 0
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  static std(values: number[]): number {
    if (values.length <= 1) return 1.0
    const mean = this.mean(values)
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1)
    return Math.sqrt(variance)
  }

  static normalPdf(x: number, mean: number, std: number): number {
    const coefficient = 1 / (std * Math.sqrt(2 * Math.PI))
    const exponent = -0.5 * Math.pow((x - mean) / std, 2)
    return coefficient * Math.exp(exponent)
  }
}

/**
 * TPE 算法的 TypeScript 实现
 * 用于在 MIPRO V2 中进行贝叶斯优化
 */
export class TPEOptimizer {
  private trials: Trial[] = []
  private readonly n_startup_jobs: number
  private readonly n_ei_candidates: number
  private readonly gamma: number

  constructor({
    n_startup_jobs = 10,
    n_ei_candidates = 24,
    gamma = 0.25
  } = {}) {
    this.n_startup_jobs = n_startup_jobs
    this.n_ei_candidates = n_ei_candidates
    this.gamma = gamma
  }

  /**
   * 记录一次试验结果
   */
  public report(trial: Trial): void {
    this.trials.push(trial)
  }

  /**
   * 获取所有试验历史
   */
  public getTrials(): Trial[] {
    return [...this.trials]
  }

  /**
   * 提出下一组要尝试的参数
   */
  public suggest(searchSpace: { [key: string]: number[] }): { [key: string]: number } {
    if (this.trials.length < this.n_startup_jobs) {
      // 随机搜索阶段
      return this.randomSample(searchSpace)
    }

    // TPE 优化阶段
    return this.tpeSample(searchSpace)
  }

  /**
   * 随机采样
   */
  private randomSample(searchSpace: { [key: string]: number[] }): { [key: string]: number } {
    const suggestion: { [key: string]: number } = {}
    for (const param in searchSpace) {
      const values = searchSpace[param]
      suggestion[param] = values[Math.floor(Math.random() * values.length)]
    }
    return suggestion
  }

  /**
   * TPE 采样
   */
  private tpeSample(searchSpace: { [key: string]: number[] }): { [key: string]: number } {
    // 按分数排序（假设分数越高越好）
    const sortedTrials = [...this.trials].sort((a, b) => b.value - a.value)
    const splitIndex = Math.floor(sortedTrials.length * this.gamma)

    // 分为 'good' (l) 和 'bad' (g) 两组
    const goodTrials = sortedTrials.slice(0, splitIndex)
    const badTrials = sortedTrials.slice(splitIndex)

    const bestParams: { [key: string]: number } = {}

    for (const param in searchSpace) {
      const paramValues = searchSpace[param]
      let bestCandidate: number | null = null
      let maxEi = -Infinity

      // 生成候选值并计算 Expected Improvement
      for (let i = 0; i < this.n_ei_candidates; i++) {
        let candidateValue: number

        if (goodTrials.length > 0 && Math.random() < 0.8) {
          // 80% 的时间从好的试验中采样
          const randomGoodTrial = goodTrials[Math.floor(Math.random() * goodTrials.length)]
          candidateValue = randomGoodTrial.params[param]
        } else {
          // 20% 的时间随机采样
          candidateValue = paramValues[Math.floor(Math.random() * paramValues.length)]
        }

        // 计算 Expected Improvement
        const ei = this.calculateExpectedImprovement(
          candidateValue,
          param,
          goodTrials,
          badTrials
        )

        if (ei > maxEi) {
          maxEi = ei
          bestCandidate = candidateValue
        }
      }

      bestParams[param] = bestCandidate !== null ?
        bestCandidate :
        paramValues[Math.floor(Math.random() * paramValues.length)]
    }

    return bestParams
  }

  /**
   * 计算 Expected Improvement
   */
  private calculateExpectedImprovement(
    candidateValue: number,
    param: string,
    goodTrials: Trial[],
    badTrials: Trial[]
  ): number {
    // 计算 l(x) - 好的试验的对数似然
    const logLikelihoodL = this.calculateLogLikelihood(goodTrials, param, candidateValue)

    // 计算 g(x) - 差的试验的对数似然
    const logLikelihoodG = this.calculateLogLikelihood(badTrials, param, candidateValue)

    // Expected Improvement = l(x) / g(x) 的对数形式
    return logLikelihoodL - logLikelihoodG
  }

  /**
   * 使用核密度估计（KDE）计算对数似然
   */
  private calculateLogLikelihood(trials: Trial[], param: string, value: number): number {
    if (trials.length === 0) return -Infinity

    const values = trials.map((t) => t.params[param])

    // 使用简单的高斯核密度估计
    const mean = Statistics.mean(values)
    const std = Statistics.std(values)

    // 避免标准差为0
    const sigma = std > 0 ? std : 1.0

    // 计算概率密度
    const density = Statistics.normalPdf(value, mean, sigma)

    // 返回对数似然，加一个小常数避免 log(0)
    return Math.log(density + 1e-9)
  }

  /**
   * 获取当前最佳试验
   */
  public getBestTrial(): Trial | null {
    if (this.trials.length === 0) return null
    return this.trials.reduce((best, current) =>
      current.value > best.value ? current : best
    )
  }

  /**
   * 重置优化器状态
   */
  public reset(): void {
    this.trials = []
  }
}
