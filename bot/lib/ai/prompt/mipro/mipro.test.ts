/**
 * MIPRO V2 优化器测试
 */

import {
  MIPROv2Optimizer,
  TPEOptimizer,
  PromptFormatter,
  QuestionAnsweringProgram,
  exactMatchMetric,
  createSampleDataset,
  quickOptimize
} from './index'

describe('MIPRO V2 Optimizer Tests', () => {
  let mockLLM: any
  let sampleDataset: any[]

  beforeEach(() => {
    // Mock LLM for testing
    mockLLM = {
      predict: jest.fn().mockResolvedValue('Mocked LLM response')
    }

    sampleDataset = createSampleDataset().slice(0, 5) // 使用小数据集进行测试
  })

  describe('TPEOptimizer', () => {
    it('should initialize correctly', () => {
      const optimizer = new TPEOptimizer()
      expect(optimizer).toBeDefined()
      expect(optimizer.getTrials()).toHaveLength(0)
    })

    it('should suggest random parameters initially', () => {
      const optimizer = new TPEOptimizer({ n_startup_jobs: 2 })
      const searchSpace = {
        param1: [0, 1, 2],
        param2: [10, 20, 30]
      }

      const suggestion = optimizer.suggest(searchSpace)

      expect(suggestion).toHaveProperty('param1')
      expect(suggestion).toHaveProperty('param2')
      expect([0, 1, 2]).toContain(suggestion.param1)
      expect([10, 20, 30]).toContain(suggestion.param2)
    })

    it('should use TPE after startup phase', () => {
      const optimizer = new TPEOptimizer({ n_startup_jobs: 1 })
      const searchSpace = {
        param1: [0, 1, 2]
      }

      // First suggestion (random)
      const suggestion1 = optimizer.suggest(searchSpace)
      optimizer.report({
        id: 0,
        params: suggestion1,
        value: 0.5,
        state: 'COMPLETE'
      })

      // Second suggestion (should use TPE)
      const suggestion2 = optimizer.suggest(searchSpace)
      expect(suggestion2).toHaveProperty('param1')
      expect([0, 1, 2]).toContain(suggestion2.param1)
    })
  })

  describe('PromptFormatter', () => {
    it('should format examples to XML correctly', () => {
      const example = {
        question: 'What is 2+2?',
        answer: '4'
      }

      const xml = PromptFormatter.formatExampleToXml(example)
      expect(xml).toContain('<question>What is 2+2?</question>')
      expect(xml).toContain('<answer>4</answer>')
    })

    it('should create full prompt with instruction and examples', () => {
      const instruction = 'Answer the question accurately.'
      const examples = [{ question: 'Test?', answer: 'Test answer' }]
      const signature = 'question -> answer'

      const fullPrompt = PromptFormatter.createFullPrompt(instruction, examples, signature)

      expect(fullPrompt).toContain('<task_signature>question -> answer</task_signature>')
      expect(fullPrompt).toContain('<instructions>')
      expect(fullPrompt).toContain('Answer the question accurately.')
      expect(fullPrompt).toContain('<examples>')
      expect(fullPrompt).toContain('<input>')
    })

    it('should extract fields correctly', () => {
      const examples = [
        { question: 'Q1', answer: 'A1' },
        { input: 'I1', output: 'O1' }
      ]

      const { inputFields, outputFields } = PromptFormatter.extractFields(examples)

      expect(inputFields).toContain('question')
      expect(inputFields).toContain('input')
      expect(outputFields).toContain('answer')
      expect(outputFields).toContain('output')
    })
  })

  describe('QuestionAnsweringProgram', () => {
    it('should initialize with default predictor', () => {
      const program = new QuestionAnsweringProgram()
      const predictors = program.getPredictors()

      expect(predictors).toHaveProperty('qa')
      expect(predictors.qa.signature).toBe('question -> answer')
    })

    it('should update prompts correctly', () => {
      const program = new QuestionAnsweringProgram()
      const newInstruction = 'New instruction for testing'

      program.updatePrompts({
        qa: {
          instruction: newInstruction,
          demos: [{ question: 'Test', answer: 'Test answer' }]
        }
      })

      const predictors = program.getPredictors()
      expect(predictors.qa.config.instruction).toBe(newInstruction)
      expect(predictors.qa.config.demos).toHaveLength(1)
    })
  })

  describe('Metrics', () => {
    it('should evaluate exact match correctly', async () => {
      const prediction = { answer: 'Paris' }
      const groundTruth = { answer: 'Paris' }

      const score = await exactMatchMetric(prediction, groundTruth)
      expect(score).toBe(1.0)
    })

    it('should handle case insensitive matching', async () => {
      const prediction = { answer: 'PARIS' }
      const groundTruth = { answer: 'paris' }

      const score = await exactMatchMetric(prediction, groundTruth)
      expect(score).toBe(1.0)
    })

    it('should return 0 for non-matching answers', async () => {
      const prediction = { answer: 'London' }
      const groundTruth = { answer: 'Paris' }

      const score = await exactMatchMetric(prediction, groundTruth)
      expect(score).toBe(0.0)
    })
  })

  describe('Integration Tests', () => {
    it('should create sample dataset correctly', () => {
      const dataset = createSampleDataset()

      expect(dataset).toBeInstanceOf(Array)
      expect(dataset.length).toBeGreaterThan(0)
      expect(dataset[0]).toHaveProperty('question')
      expect(dataset[0]).toHaveProperty('answer')
    })

    it('should handle empty datasets gracefully', () => {
      const summary = PromptFormatter.generateDatasetSummary([])
      expect(summary).toContain('Empty dataset')
    })
  })

  // 注意：以下测试需要真实的 LLM 调用，在 CI/CD 中可能需要跳过
  describe('End-to-End Tests (requires LLM)', () => {
    it.skip('should run quick optimization', async () => {
      const program = new QuestionAnsweringProgram()
      const trainset = sampleDataset

      const result = await quickOptimize(program, trainset, {
        llm: mockLLM,
        numTrials: 3
      })

      expect(result).toHaveProperty('bestPrompts')
      expect(result).toHaveProperty('bestScore')
      expect(result).toHaveProperty('totalTrials')
      expect(result.totalTrials).toBe(3)
    }, 30000) // 30秒超时

    it.skip('should optimize with custom metric', async () => {
      const program = new QuestionAnsweringProgram()
      const customMetric = jest.fn().mockResolvedValue(0.8)

      const optimizer = new MIPROv2Optimizer({
        metric: customMetric,
        promptModel: mockLLM,
        taskModel: mockLLM,
        numTrials: 2,
        numCandidates: 2
      })

      const result = await optimizer.optimize(program, sampleDataset)

      expect(result.bestScore).toBeGreaterThanOrEqual(0)
      expect(customMetric).toHaveBeenCalled()
    }, 30000)
  })

  describe('Error Handling', () => {
    it('should handle LLM errors gracefully', async () => {
      const errorLLM = {
        predict: jest.fn().mockRejectedValue(new Error('LLM Error'))
      }

      const optimizer = new MIPROv2Optimizer({
        metric: exactMatchMetric,
        promptModel: errorLLM,
        taskModel: errorLLM,
        numTrials: 1,
        numCandidates: 1
      })

      const program = new QuestionAnsweringProgram()

      // 应该不会抛出错误，而是优雅地处理
      await expect(optimizer.optimize(program, sampleDataset.slice(0, 2)))
        .resolves.toBeDefined()
    })

    it('should handle empty instruction generation', () => {
      const optimizer = new MIPROv2Optimizer({
        metric: exactMatchMetric,
        promptModel: mockLLM,
        taskModel: mockLLM
      })

      // 测试私有方法的备用指令生成
      // 注意：这需要将方法设为 public 或使用其他测试策略
      expect(true).toBe(true) // 占位符测试
    })
  })
})
