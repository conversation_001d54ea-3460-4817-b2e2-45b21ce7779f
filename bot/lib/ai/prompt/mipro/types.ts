/**
 * MIPRO V2 Prompt Optimizer Types
 * 基于 DSPy 论文中的 MIPRO V2 算法实现
 */

/**
 * 代表一个数据集中的单条样本
 */
export interface Example {
  [key: string]: any; // e.g., { question: "...", answer: "..." }
}

/**
 * 代表一个完整的 Language Model Program
 * 它可以接收一个输入，并返回一个包含预测和追踪信息的输出
 */
export interface LMProgram {
  // 主要的调用方法
  call?(input: Example): Promise<{ prediction: Example; trace: any }>;
  // 函数调用接口（可选）
  __call__(input: Example): Promise<{ prediction: Example; trace: any }>;
  // 用于更新程序内部（如prompt）的方法
  updatePrompts(newPrompts: { [predictorName: string]: Partial<PredictorConfig> }): void;
  // 用于获取当前程序配置的方法
  getPredictors(): { [name: string]: Predictor };
}

/**
 * 代表一个可预测的模块（如 dspy.Predict, dspy.ChainOfThought）
 */
export interface Predictor {
  name: string;
  signature: string; // e.g., "question -> answer"
  config: PredictorConfig;
}

/**
 * Predictor 的配置，包含指令和 few-shot 示例
 */
export interface PredictorConfig {
  instruction: string;
  demos: Example[];
}

/**
 * 评估函数，计算预测结果的分数
 */
export type Metric = (prediction: Example, groundTruth: Example) => Promise<number>;

/**
 * TPE 算法的试验结果
 */
export interface Trial {
  id: number;
  params: { [key: string]: number }; // 参数空间中的索引
  value: number; // 评估分数
  state: 'COMPLETE' | 'RUNNING' | 'WAITING';
}

/**
 * 代表一个候选的 few-shot demonstration 集合
 */
export interface DemonstrationSet {
  id: number;
  demos: Example[];
}

/**
 * 代表一个候选的指令
 */
export interface Instruction {
  id: number;
  text: string;
}

/**
 * 优化后的完整 Prompt 结果
 */
export interface OptimizedPrompt {
  predictorName: string;
  instruction: string;
  examples: Example[];
  fullPrompt: string; // 包含指令和示例的完整格式化 prompt
  score: number; // 该 prompt 的评估分数
}

/**
 * MIPRO 优化器的配置
 */
export interface MiproConfig {
  metric: Metric;
  promptModel: any; // LLM instance for generating prompts
  taskModel: any; // LLM instance for task execution
  maxBootstrappedDemos?: number;
  maxLabeledDemos?: number;
  numTrials?: number;
  minibatchSize?: number;
  numCandidates?: number; // 每个 predictor 生成的指令和示例集的数量
}

/**
 * 优化结果
 */
export interface OptimizationResult {
  bestPrompts: { [predictorName: string]: OptimizedPrompt };
  bestScore: number;
  totalTrials: number;
  optimizationHistory: Trial[];
}

/**
 * Prompt 模板格式
 */
export interface PromptTemplate {
  instruction: string;
  examples: Example[];
  signature: string;
}

/**
 * 数据集摘要信息
 */
export interface DatasetSummary {
  totalSamples: number;
  inputFields: string[];
  outputFields: string[];
  sampleInputs: string[];
  sampleOutputs: string[];
  taskDescription: string;
}
