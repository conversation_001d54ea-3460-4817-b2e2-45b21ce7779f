/**
 * MIPRO V2 自动化 Prompt 优化器
 * 基于 DSPy 论文中的 MIPRO V2 算法实现
 */

import {
  Example,
  LMProgram,
  MiproConfig,
  OptimizationResult,
  OptimizedPrompt,
  Instruction,
  DemonstrationSet,
  PredictorConfig,
  Trial
} from './types'
import { TPEOptimizer } from './tpe'
import { PromptFormatter } from './prompt_formatter'
import { XMLHelper } from '../../../xml/xml'

export class MIPROv2Optimizer {
  private readonly config: Required<MiproConfig>

  constructor(config: MiproConfig) {
    this.config = {
      ...config,
      maxBootstrappedDemos: config.maxBootstrappedDemos ?? 4,
      maxLabeledDemos: config.maxLabeledDemos ?? 4,
      numTrials: config.numTrials ?? 20,
      minibatchSize: config.minibatchSize ?? 10,
      numCandidates: config.numCandidates ?? 5,
    }
  }

  /**
   * 主要的优化入口点
   */
  public async optimize(program: LMProgram, trainset: Example[]): Promise<OptimizationResult> {
    console.log('🚀 Starting MIPRO V2 Optimization...')

    // 步骤 1: Bootstrap Few-shot Examples
    console.log('📚 Step 1: Bootstrapping few-shot examples...')
    const predictors = program.getPredictors()
    const demoCandidates: { [predictorName: string]: DemonstrationSet[] } = {}

    for (const name in predictors) {
      demoCandidates[name] = await this.bootstrapDemonstrations(
        program,
        trainset,
        this.config.numCandidates
      )
      console.log(`   Generated ${demoCandidates[name].length} demo sets for ${name}`)
    }

    // 步骤 2: Propose Instruction Candidates
    console.log('💡 Step 2: Proposing instruction candidates...')
    const instructionCandidates: { [predictorName: string]: Instruction[] } = {}

    for (const name in predictors) {
      const predictor = predictors[name]
      instructionCandidates[name] = await this.proposeInstructions(
        predictor.signature,
        demoCandidates[name][0]?.demos || [],
        trainset,
        this.config.numCandidates
      )
      console.log(`   Generated ${instructionCandidates[name].length} instructions for ${name}`)
    }

    // 步骤 3: Bayesian Optimization
    console.log('🎯 Step 3: Finding optimal parameters with TPE...')
    const optimizationResult = await this.findOptimalParameters(
      program,
      trainset,
      instructionCandidates,
      demoCandidates
    )

    console.log('✅ MIPRO V2 Optimization completed!')
    console.log(`   Best score: ${optimizationResult.bestScore.toFixed(4)}`)
    console.log(`   Total trials: ${optimizationResult.totalTrials}`)

    return optimizationResult
  }

  /**
   * 步骤 1: 通过在训练集上运行程序并筛选成功案例来生成 few-shot 示例
   */
  private async bootstrapDemonstrations(
    program: LMProgram,
    trainset: Example[],
    numSets: number
  ): Promise<DemonstrationSet[]> {
    const demonstrationSets: DemonstrationSet[] = []

    for (let i = 0; i < numSets; i++) {
      const demos: Example[] = []
      let attempts = 0
      const maxAttempts = Math.min(trainset.length * 2, 100) // 限制尝试次数

      while (demos.length < this.config.maxBootstrappedDemos && attempts < maxAttempts) {
        const randomExample = trainset[Math.floor(Math.random() * trainset.length)]

        try {
          const { prediction, trace } = await this.callProgram(program, randomExample)
          const score = await this.config.metric(prediction, randomExample)

          // 如果预测质量足够好，将其作为示例
          if (score > 0.7) { // 可配置的阈值
            // 构建 demo：输入 + 正确输出
            const demo = { ...randomExample }
            demos.push(demo)
          }
        } catch (error) {
          console.warn(`Error in bootstrap attempt ${attempts}:`, error)
        }

        attempts++
      }

      // 如果没有足够的高质量示例，使用原始训练数据
      if (demos.length === 0) {
        const fallbackDemos = trainset
          .slice(0, this.config.maxBootstrappedDemos)
          .map((example) => ({ ...example }))
        demonstrationSets.push({ id: i, demos: fallbackDemos })
      } else {
        demonstrationSets.push({ id: i, demos })
      }
    }

    return demonstrationSets
  }

  /**
   * 步骤 2: 基于数据集、代码和示例，让 LLM 生成候选指令
   */
  private async proposeInstructions(
    signature: string,
    demos: Example[],
    trainset: Example[],
    numInstructions: number
  ): Promise<Instruction[]> {
    const datasetSummary = PromptFormatter.generateDatasetSummary(trainset.slice(0, 10))
    const demosXml = PromptFormatter.formatExamplesToXml(demos)

    const prompt = `You are an expert prompt engineer. Your task is to generate high-quality instructions for a language model.

<task_signature>${signature}</task_signature>

<dataset_summary>
${datasetSummary}
</dataset_summary>

<successful_examples>
${demosXml}
</successful_examples>

Based on the information above, generate ${numInstructions} distinct and effective instructions.
Each instruction should guide the model to perform the task defined by the signature.

Requirements:
1. Instructions should be clear, specific, and actionable
2. Consider the input-output pattern shown in the examples
3. Include relevant context about the task domain
4. Vary the approach and style across different instructions
5. Focus on what the model should do, not what it shouldn't do

Format your response as XML with multiple <instruction> tags:

<instructions>
<instruction>Your first proposed instruction...</instruction>
<instruction>Your second proposed instruction...</instruction>
<instruction>Your third proposed instruction...</instruction>
</instructions>`

    try {
      const response = await this.config.promptModel.predict(prompt)
      const instructions = XMLHelper.extractContents(response, 'instruction')

      if (!instructions || instructions.length === 0) {
        console.warn('No instructions extracted, using fallback')
        return this.generateFallbackInstructions(signature, numInstructions)
      }

      return instructions.map((text, id) => ({ id, text: text.trim() }))
    } catch (error) {
      console.error('Error generating instructions:', error)
      return this.generateFallbackInstructions(signature, numInstructions)
    }
  }

  /**
   * 生成备用指令（当 LLM 生成失败时）
   */
  private generateFallbackInstructions(signature: string, numInstructions: number): Instruction[] {
    const fallbackInstructions = [
      `Please analyze the input and provide an appropriate response based on the task: ${signature}`,
      `Given the input, carefully consider the requirements and generate a high-quality output for: ${signature}`,
      `Your task is to process the input and produce the expected output according to: ${signature}`,
      `Follow the pattern shown in the examples to complete this task: ${signature}`,
      `Apply your knowledge to transform the input into the desired output format for: ${signature}`
    ]

    return fallbackInstructions
      .slice(0, numInstructions)
      .map((text, id) => ({ id, text }))
  }

  /**
   * 步骤 3: 使用 TPE 算法搜索最佳的指令和示例组合
   */
  private async findOptimalParameters(
    program: LMProgram,
    trainset: Example[],
    instructionCandidates: { [predictorName: string]: Instruction[] },
    demoCandidates: { [predictorName: string]: DemonstrationSet[] }
  ): Promise<OptimizationResult> {
    const optimizer = new TPEOptimizer()
    const predictors = program.getPredictors()
    const searchSpace: { [key: string]: number[] } = {}

    // 构建搜索空间
    for (const name in predictors) {
      searchSpace[`${name}_instruction`] = instructionCandidates[name].map((i) => i.id)
      searchSpace[`${name}_demos`] = demoCandidates[name].map((d) => d.id)
    }

    let bestScore = -Infinity
    let bestParams: { [key: string]: number } | null = null
    const allTrials: Trial[] = []

    // 准备评估用的 mini-batch
    const minibatch = this.selectMinibatch(trainset)

    for (let i = 0; i < this.config.numTrials; i++) {
      console.log(`   Trial ${i + 1}/${this.config.numTrials}`)

      const params = optimizer.suggest(searchSpace)

      // 应用新参数到程序
      const newPrompts = this.buildPromptsFromParams(
        params,
        predictors,
        instructionCandidates,
        demoCandidates
      )

      program.updatePrompts(newPrompts)

      // 在 mini-batch 上评估
      const score = await this.evaluateProgram(program, minibatch)

      const trial: Trial = {
        id: i,
        params,
        value: score,
        state: 'COMPLETE'
      }

      optimizer.report(trial)
      allTrials.push(trial)

      console.log(`     Score: ${score.toFixed(4)}`)

      if (score > bestScore) {
        bestScore = score
        bestParams = params
        console.log(`     🎉 New best score: ${bestScore.toFixed(4)}`)
      }
    }

    if (!bestParams) {
      throw new Error('Optimization failed to find any valid parameters.')
    }

    // 构建最终结果
    const bestPrompts = this.buildOptimizedPrompts(
      bestParams,
      predictors,
      instructionCandidates,
      demoCandidates,
      bestScore
    )

    return {
      bestPrompts,
      bestScore,
      totalTrials: this.config.numTrials,
      optimizationHistory: allTrials
    }
  }

  /**
   * 选择用于评估的 mini-batch
   */
  private selectMinibatch(trainset: Example[]): Example[] {
    const batchSize = Math.min(this.config.minibatchSize, trainset.length)

    // 简单随机采样
    const shuffled = [...trainset].sort(() => Math.random() - 0.5)
    return shuffled.slice(0, batchSize)
  }

  /**
   * 从参数构建 prompt 配置
   */
  private buildPromptsFromParams(
    params: { [key: string]: number },
    predictors: { [name: string]: any },
    instructionCandidates: { [predictorName: string]: Instruction[] },
    demoCandidates: { [predictorName: string]: DemonstrationSet[] }
  ): { [predictorName: string]: Partial<PredictorConfig> } {
    const newPrompts: { [predictorName: string]: Partial<PredictorConfig> } = {}

    for (const name in predictors) {
      const instId = params[`${name}_instruction`]
      const demoId = params[`${name}_demos`]

      const instruction = instructionCandidates[name].find((c) => c.id === instId)
      const demoSet = demoCandidates[name].find((c) => c.id === demoId)

      if (instruction && demoSet) {
        newPrompts[name] = {
          instruction: instruction.text,
          demos: demoSet.demos,
        }
      }
    }

    return newPrompts
  }

  /**
   * 评估程序在给定数据集上的性能
   */
  private async evaluateProgram(program: LMProgram, examples: Example[]): Promise<number> {
    let totalScore = 0
    let validPredictions = 0

    for (const example of examples) {
      try {
        const { prediction } = await this.callProgram(program, example)
        const score = await this.config.metric(prediction, example)
        totalScore += score
        validPredictions++
      } catch (error) {
        console.warn('Error in evaluation:', error)
        // 对失败的预测给予 0 分
      }
    }

    return validPredictions > 0 ? totalScore / validPredictions : 0
  }

  /**
   * 构建最终的优化结果
   */
  private buildOptimizedPrompts(
    bestParams: { [key: string]: number },
    predictors: { [name: string]: any },
    instructionCandidates: { [predictorName: string]: Instruction[] },
    demoCandidates: { [predictorName: string]: DemonstrationSet[] },
    score: number
  ): { [predictorName: string]: OptimizedPrompt } {
    const optimizedPrompts: { [predictorName: string]: OptimizedPrompt } = {}

    for (const name in predictors) {
      const instId = bestParams[`${name}_instruction`]
      const demoId = bestParams[`${name}_demos`]

      const instruction = instructionCandidates[name].find((c) => c.id === instId)
      const demoSet = demoCandidates[name].find((c) => c.id === demoId)

      if (instruction && demoSet) {
        optimizedPrompts[name] = PromptFormatter.createOptimizedPrompt(
          name,
          instruction.text,
          demoSet.demos,
          predictors[name].signature,
          score
        )
      }
    }

    return optimizedPrompts
  }

  /**
   * 调用程序的辅助方法，处理不同的调用接口
   */
  private async callProgram(program: LMProgram, input: Example): Promise<{ prediction: Example; trace: any }> {
    if (typeof program.call === 'function') {
      return await program.call(input)
    } else if (typeof program.__call__ === 'function') {
      return await program.__call__(input)
    } else {
      throw new Error('Program must implement either call() or __call__() method')
    }
  }
}
