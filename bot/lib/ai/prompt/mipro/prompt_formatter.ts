/**
 * Prompt 格式化工具
 * 负责将指令和示例格式化为完整的 XML 格式 prompt
 */

import { Example, PredictorConfig, OptimizedPrompt } from './types'

export class PromptFormatter {
  /**
   * 将示例格式化为 XML 格式
   */
  static formatExampleToXml(example: Example): string {
    const xmlParts: string[] = []

    for (const [key, value] of Object.entries(example)) {
      if (value !== undefined && value !== null) {
        const cleanValue = String(value).trim()
        xmlParts.push(`<${key}>${cleanValue}</${key}>`)
      }
    }

    return xmlParts.join('\n')
  }

  /**
   * 将多个示例格式化为 XML 格式
   */
  static formatExamplesToXml(examples: Example[]): string {
    if (examples.length === 0) {
      return ''
    }

    const formattedExamples = examples.map((example, index) => {
      const xmlExample = this.formatExampleToXml(example)
      return `<example_${index + 1}>\n${xmlExample}\n</example_${index + 1}>`
    })

    return formattedExamples.join('\n\n')
  }

  /**
   * 创建完整的 prompt，包含指令和示例
   */
  static createFullPrompt(
    instruction: string,
    examples: Example[],
    signature: string
  ): string {
    const parts: string[] = []

    // 添加任务签名
    if (signature) {
      parts.push(`<task_signature>${signature}</task_signature>`)
      parts.push('')
    }

    // 添加指令
    if (instruction) {
      parts.push(`<instructions>\n${instruction.trim()}\n</instructions>`)
      parts.push('')
    }

    // 添加示例
    if (examples.length > 0) {
      parts.push('<examples>')
      parts.push(this.formatExamplesToXml(examples))
      parts.push('</examples>')
      parts.push('')
    }

    // 添加输入占位符
    parts.push('<input>\n{input}\n</input>')
    parts.push('')
    parts.push('Please provide your response:')

    return parts.join('\n')
  }

  /**
   * 从 PredictorConfig 创建完整的 prompt
   */
  static fromPredictorConfig(
    config: PredictorConfig,
    signature: string
  ): string {
    return this.createFullPrompt(config.instruction, config.demos, signature)
  }

  /**
   * 创建优化后的 prompt 对象
   */
  static createOptimizedPrompt(
    predictorName: string,
    instruction: string,
    examples: Example[],
    signature: string,
    score: number
  ): OptimizedPrompt {
    const fullPrompt = this.createFullPrompt(instruction, examples, signature)

    return {
      predictorName,
      instruction,
      examples,
      fullPrompt,
      score
    }
  }

  /**
   * 格式化优化结果为可读的字符串
   */
  static formatOptimizationResult(optimizedPrompts: { [key: string]: OptimizedPrompt }): string {
    const parts: string[] = []

    parts.push('='.repeat(80))
    parts.push('MIPRO V2 OPTIMIZATION RESULTS')
    parts.push('='.repeat(80))
    parts.push('')

    for (const [predictorName, prompt] of Object.entries(optimizedPrompts)) {
      parts.push(`PREDICTOR: ${predictorName}`)
      parts.push(`SCORE: ${prompt.score.toFixed(4)}`)
      parts.push('-'.repeat(60))
      parts.push('')

      parts.push('OPTIMIZED INSTRUCTION:')
      parts.push(prompt.instruction)
      parts.push('')

      if (prompt.examples.length > 0) {
        parts.push(`EXAMPLES (${prompt.examples.length} total):`)
        prompt.examples.forEach((example, index) => {
          parts.push(`Example ${index + 1}:`)
          parts.push(this.formatExampleToXml(example))
          parts.push('')
        })
      }

      parts.push('FULL PROMPT:')
      parts.push('```')
      parts.push(prompt.fullPrompt)
      parts.push('```')
      parts.push('')
      parts.push('='.repeat(80))
      parts.push('')
    }

    return parts.join('\n')
  }

  /**
   * 将示例数据转换为训练格式
   */
  static convertToTrainingFormat(examples: Example[]): string {
    const xmlExamples = examples.map((example, index) => {
      return `<training_example_${index + 1}>\n${this.formatExampleToXml(example)}\n</training_example_${index + 1}>`
    })

    return `<training_data>\n${xmlExamples.join('\n\n')}\n</training_data>`
  }

  /**
   * 提取任务的输入输出字段
   */
  static extractFields(examples: Example[]): { inputFields: string[], outputFields: string[] } {
    if (examples.length === 0) {
      return { inputFields: [], outputFields: [] }
    }

    const allFields = new Set<string>()
    examples.forEach((example) => {
      Object.keys(example).forEach((key) => allFields.add(key))
    })

    // 简单的启发式：通常 'question', 'input', 'query' 等是输入字段
    // 'answer', 'output', 'response' 等是输出字段
    const inputKeywords = ['question', 'input', 'query', 'prompt', 'text']
    const outputKeywords = ['answer', 'output', 'response', 'result', 'prediction']

    const inputFields: string[] = []
    const outputFields: string[] = []

    Array.from(allFields).forEach((field) => {
      const lowerField = field.toLowerCase()
      if (inputKeywords.some((keyword) => lowerField.includes(keyword))) {
        inputFields.push(field)
      } else if (outputKeywords.some((keyword) => lowerField.includes(keyword))) {
        outputFields.push(field)
      } else {
        // 默认归类为输入字段
        inputFields.push(field)
      }
    })

    return { inputFields, outputFields }
  }

  /**
   * 生成数据集摘要
   */
  static generateDatasetSummary(examples: Example[]): string {
    if (examples.length === 0) {
      return 'Empty dataset provided.'
    }

    const { inputFields, outputFields } = this.extractFields(examples)
    const sampleSize = Math.min(3, examples.length)
    const samples = examples.slice(0, sampleSize)

    const parts: string[] = []
    parts.push(`Dataset contains ${examples.length} examples.`)
    parts.push(`Input fields: ${inputFields.join(', ')}`)
    parts.push(`Output fields: ${outputFields.join(', ')}`)
    parts.push('')
    parts.push('Sample examples:')

    samples.forEach((example, index) => {
      parts.push(`Example ${index + 1}:`)
      parts.push(this.formatExampleToXml(example))
      parts.push('')
    })

    return parts.join('\n')
  }
}
