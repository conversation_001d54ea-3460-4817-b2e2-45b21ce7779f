import { LLM } from '../LLM'
import { tool } from '@langchain/core/tools'
import { z } from 'zod'
import { AIMessage } from '@langchain/core/messages'

describe('LLM', () => {
  describe('toolCall', () => {
    it('should call a tool with the correct parameters and return the result', async () => {
      const testTool = tool(
        async ({ a, b }: { a: number, b: number }) => {
          return (a + b).toString()
        },
        {
          name: 'add',
          description: 'Adds two numbers',
          schema: z.object({
            a: z.number(),
            b: z.number(),
          }),
        }
      )

      const result = await new LLM().toolCall([testTool], 'What is 5 + 10?')

      expect(result).toBe('15')
    })

    it('should handle no tool calls and return content', async () => {
      const llm = new LLM()
      const mockResponse = new AIMessage({
        content: 'Hello there!',
        tool_calls: [],
      });

      (llm as any).getClients = jest.fn().mockReturnValue([
        {
          bindTools: jest.fn().mockReturnValue({
            withConfig: jest.fn().mockReturnValue({
              invoke: jest.fn().mockResolvedValue(mockResponse),
            }),
          }),
        },
      ])

      const result = await llm.toolCall([], 'Say hi')

      expect(result).toBe('Hello there!')
    })

    it('should handle tool not found', async () => {
      const llm = new LLM()
      const mockResponse = new AIMessage({
        content: '',
        tool_calls: [{
          name: 'nonExistentTool',
          args: {},
          id: 'test-id-2',
        }],
      });

      (llm as any).getClients = jest.fn().mockReturnValue([
        {
          bindTools: jest.fn().mockReturnValue({
            withConfig: jest.fn().mockReturnValue({
              invoke: jest.fn().mockResolvedValue(mockResponse),
            }),
          }),
        },
      ])

      const result = await llm.toolCall([], 'Use a tool')

      expect(result).toBe('未找到名为 "nonExistentTool" 的工具')
    })

    it('should handle tool invocation error', async () => {
      const chat_id = '123'

      const errorTool = tool(
        async () => {
          console.log(chat_id)
          throw new Error('Tool failed')
        },
        {
          name: 'errorTool',
          description: 'A tool that always fails',
          schema: z.object({}),
        }
      )

      const llm = new LLM()
      const mockResponse = new AIMessage({
        content: '',
        tool_calls: [{
          name: 'errorTool',
          args: {},
          id: 'test-id-3',
        }],
      });

      (llm as any).getClients = jest.fn().mockReturnValue([
        {
          bindTools: jest.fn().mockReturnValue({
            withConfig: jest.fn().mockReturnValue({
              invoke: jest.fn().mockResolvedValue(mockResponse),
            }),
          }),
        },
      ])

      const result = await llm.toolCall([errorTool], 'Use error tool')

      expect(result).toContain('工具 "errorTool" 调用失败: Error: Tool failed')
    })

    it('should handle multiple tools and select the correct one based on user query', async () => {
      // 1. Define the tools for the planner
      const get_rumenying_detail = tool(
        async ({ course_name }) => {
          if (course_name === '红靴子') {
            return '红靴子课程的详细介绍...'
          }
          return `未找到课程: ${course_name}`
        },
        {
          name: 'get_rumenying_detail',
          description: '查询指定课程（如“红靴子”、“21天系统班”）的详细介绍、目标、适合人群。',
          schema: z.object({ course_name: z.string() }),
        }
      )

      const get_system_course_detail = tool(
        async ({ policy_name }) => {
          if (policy_name === '退款政策') {
            return '这是退款政策的详细说明...'
          }
          return `未找到政策: ${policy_name}`
        },
        {
          name: 'get_system_course_detail',
          description: '查询特定政策（如“退款政策”、“延期政策”）。',
          schema: z.object({ policy_name: z.string() }),
        }
      )

      const get_faq = tool(
        async ({ query }) => {
          return `关于“${query}”的FAQ解答...`
        },
        {
          name: 'get_faq',
          description: '根据客户问题，从FAQ文档中检索最相关的解答。',
          schema: z.object({ query: z.string() }),
        }
      )

      const tools = [get_rumenying_detail, get_system_course_detail, get_faq]
      const llm = new LLM()


      // 3. Call toolCall with a query that should trigger the course detail tool
      const result = await llm.toolCall(tools, '好的', '')

      console.log(JSON.stringify(result, null, 4))
    })


    it('planner knowledge search 正式实现', async () => {
      // 1. 构建一个包含完整背景和阶段性规则的System Prompt
      const salesStartDate = '2025/7/20 20:00:00'

      const systemPromptForToolCall = `# 背景信息
你是一个AI销售Planner的智能信息助理。你的任务是判断Planner在当前节点是否需要查询知识库，如果需要，就调用合适的工具。
- **当前时间**: ${new Date().toLocaleString()} 
- **销售流程**: 我们正在进行一个为期5天的冥想入门体验营。前3天（截止到 ${salesStartDate} 之前）是纯粹的体验和价值建立期。从第3天晚上（${salesStartDate} 开始）会正式介绍并售卖21天付费系统班（我们的核心商品）。

# 工具使用规则 (非常重要)
1.  **阶段性约束**:
    -   **在销售开始前 (当前时间 < ${salesStartDate})**: 【绝对禁止】调用 \`get_system_course_detail\` 工具。此时你的所有注意力都应该放在入门营体验上。
    -   **在销售开始后 (当前时间 >= ${salesStartDate})**: 你【可以】调用 \`get_system_course_detail\` 来处理与付费课程相关的咨询和异议。
2.  **工具职责区分**:
    -   \`get_entry_camp_detail\`: 只用来查5天免费体验营的课程。
    -   \`get_system_course_detail\`: 只用来查21天付费系统班（商品）的信息。
    -   \`get_faq_answer\`: 只用来查通用的、常见的一些问题答疑。
3.  **调用时机**: 
    -   **优先调用**: 当客户的需求或问题与某个【具体课程主题】或【FAQ关键词】高度匹配时，你应该【主动调用工具】来获取精准信息，以便Planner制定出更具说服力的个性化策略。
    -   **调用目的**: 调用工具的目的是为了【丰富规划的细节和依据】，而不是等到万不得已。例如，知道“财富解锁”课程的具体内容，可以帮助Planner更好地将其与客户的“财富焦虑”联系起来。
    -   **避免调用**: 如果客户的意图非常模糊，或者当前首要任务是进行情感共情而非信息传递，则可以回答“无需调用工具”。

# 你的任务
基于以上背景和规则，分析下面的对话信息，并决定是否以及如何调用工具。如果不需要调用，直接回答“无需调用工具”。`

      // 2. 将客户信息作为User Message传递
      const userPromptForToolCall = `**最近对话历史:**
${''}

**当前客户发言:**
${'在哪上课'}

请根据你的分析，进行工具调用或回答“无需调用工具”。`


      /**
       * Tool 1: 查询入门营课程信息
       * 职责：获取5天免费体验营的课程细节。
       */
      const getEntryCampDetail = tool(
        async ({ courseName }) => {
          return `【入门营课程 - ${courseName}】`
        },
        {
          name: 'get_entry_camp_detail',
          description: '【仅用于查询5天免费入门体验营】的课程信息。当需要将客户的初期体验或需求与某节入门营课程连接时调用。第一节课 《情绪减压》：让你心身放松，从情绪根源释放压力，沉浸式秒睡冥想练习。 第二天练习《财富解锁》：让你突破金钱的卡点，唤醒你的财富潜力，有财富果园练习。 第三天练习《效能提升》：《红靴子》冥想，让你做事更高效，增强能量。 第四节课 《蓝鹰预演》帮助清晰愿景，无限数息，延长呼吸',
          schema: z.object({
            courseName: z.string().describe('要查询的入门营课程的相关信息'),
          }),
        }
      )

      /**
       * Tool 2: 查询系统班（付费商品）信息
       * 职责：获取21天付费系统班的详细信息。
       */
      const getSystemCourseDetail = tool(
        async ({ query }) => {
          return  `【21天系统班 - ${query}】介绍`
        },
        {
          name: 'get_system_course_detail',
          description: '【仅用于查询21天付费系统班（商品）】的信息。当客户进入转化阶段，开始询问系统班细节、价格、政策，或需要处理相关异议时调用。',
          schema: z.object({
            query: z.string().describe('要查询的系统班相关主题'),
          }),
        }
      )

      /**
       * Tool 3: 查询通用FAQ
       * 职责：解答与课程内容无关的通用性问题。
       */
      const getFaqAnswer = tool(
        async ({ query }) => {
          return `关于“${query}”的常见解答`
        },
        {
          name: 'get_faq_answer',
          description: '查询通用的、与具体课程内容无关的常见问题。例如登录问题、时间安排等。',
          schema: z.object({
            query: z.string().describe('要查询的通用问题关键词'),
          }),
        }
      )

      const tools = [getEntryCampDetail, getSystemCourseDetail, getFaqAnswer]
      const llm = new LLM({ projectName: 'playground' })

      // 3. Call toolCall with a query that should trigger the course detail tool
      const result = await llm.toolCall(tools, userPromptForToolCall, systemPromptForToolCall)

      console.log(JSON.stringify(result, null, 4))
    }, 30000)
  })
})