// TODO 后面修改值和 value 的都抽象成 formatKey, formatValue 这两个方法内，不要暴露太多函数
export interface IDeepCloneOptions {
  ignoreKeys?: string[] // 不做以下处理的 key
  emptyStringToUndefined?: boolean
  ignoreUndefined?: boolean
  ignoreFalse?: boolean
  valueToUnderScoreCase?: string[] // 值需要做处理的 keys
  keyToUnderScoreCase?: boolean
  valueToCamelCase?: string[] // 值需要做处理的 keys
  keyToCamelCase?: string[]
  valueFormat?: (value: any, key: string) => any
}

export class ObjectUtil {
  public static iterate(obj: any, callback: (key: string, value: any) => any, objKey?: string): void {
    // 只处理基础类型
    if (objKey && typeof obj !== 'object') {
      callback(objKey, obj)
      return
    }

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const value = obj[key]

        if (typeof value === 'object') {
          this.iterate(value, callback, key)
        } else {
          callback(key, value)
        }
      }
    }
  }

  /**
   * 深度拷贝对象
   * @param obj
   * @param options
   */
  public static deepClone(obj: any, options?: IDeepCloneOptions): any {
    if (typeof obj !== 'object') {
      return obj
    }

    if (obj === null) {
      // 注意 null 也是 object, typeof null === 'object' = true
      return null
    }

    const newObj: any = obj instanceof Array ? [] : {}
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        let value = typeof obj[key] === 'object' ? ObjectUtil.deepClone(obj[key], options) : obj[key]

        if (options?.keyToUnderScoreCase) {
          key = key
            .replace(/[A-Z]/g, (letter, index) => {
              return index == 0 ? letter.toLowerCase() : `_${letter.toLowerCase()}`
            })
            .toLowerCase()
        }

        if (options?.keyToCamelCase && options.keyToCamelCase.includes(key)) {
          key = key.replace(/_([a-z])/g, (all, letter) => letter.toUpperCase())
        }

        if (options?.ignoreKeys?.includes(key)) {
          newObj[key] = value
          continue
        } else if (options?.emptyStringToUndefined && value === '') {
          value = undefined
        } else if (options?.ignoreUndefined && value === undefined) {
          continue
        } else if (options?.ignoreFalse && value === false) {
          continue
        } else if (typeof value === 'string' && options?.valueToUnderScoreCase?.includes(key)) {
          value = value.replace(/[A-Z]/g, (letter: string, index: number) => {
            return index == 0 ? letter.toLowerCase() : `_${letter.toLowerCase()}`
          })
          value = value.replace(/\s/g, '_').replace(/_+/g, '_').toLowerCase()
        } else if (typeof value === 'string' && options?.valueToCamelCase?.includes(key)) {
          value = value.replace(/_([a-z])/g, (all, letter) => letter.toUpperCase())
        } else if (options?.valueFormat) {
          value = options.valueFormat(value, key)
        }

        newObj[key] = value
      }
    }
    return newObj
  }

  /**
   * 判断两个对象是否深度相同，等同于 lodash 的 isDeepEqual
   * @param obj1
   * @param obj2
   */
  public static isEqual(obj1: any, obj2: any): boolean {
    if (obj1 === obj2) {
      return true
    }
    if (obj1 == null || obj2 == null) {
      return false
    }
    if (typeof obj1 !== typeof obj2) {
      return false
    }
    if (typeof obj1 === 'object') {
      const keys1 = Object.keys(obj1)
      const keys2 = Object.keys(obj2)
      if (keys1.length !== keys2.length) {
        return false
      }
      for (let i = 0; i < keys1.length; i++) {
        const key = keys1[i]
        if (!this.isEqual(obj1[key], obj2[key])) {
          return false
        }
      }
      return true
    }
    return obj1 === obj2
  }

  /**
   * 判断对象是否为空
   * @param obj
   */
  public static isEmptyObject(obj: any): boolean {
    if (obj === undefined || obj === null) {
      return true
    }

    return Object.keys(obj).length <= 0
  }

  /**
   * 获取对象长度
   */
  public static len(obj: any): number {
    return Object.keys(obj).length
  }

  /**
   * 返回 Object key 数组,
   * 注意这里会主动过滤掉 prototype, length 和 name 属性
   */
  public static keys<O extends object, K extends keyof O>(obj: O): Exclude<K, 'prototype'>[] {
    return Object.getOwnPropertyNames(obj).filter((key) => !['length', 'prototype', 'name'].includes(key)) as Exclude<
      K,
      'prototype'
    >[]
  }

  /**
   * 以 Enum 类型返回 enum key 列表, 用于遍历
   * @param obj
   */
  public static enumKeys<O extends Record<string, unknown>, K extends keyof O>(obj: O): K[] {
    return Object.keys(obj).filter((k) => Number.isNaN(Number(k))) as K[]
  }

  /**
   * 以 Enum 类型返回 enum value 列表, 用于遍历
   * @param obj
   */
  public static enumValues<O extends Record<string, unknown>, K extends keyof O>(obj: O): any[] {
    return this.enumKeys(obj).map((key) => obj[key])
  }

  /**
   * Enum to Object
   */
  public static enumToObject(enumObj: Record<any, unknown>) {
    const obj: Record<string, any> = {}

    this.enumKeys(enumObj).forEach((key) => {
      obj[key] = enumObj[key]
    })

    return obj
  }

  /**
   * Enum Value to Key
   * 将 enum 的 value 转换为 key
   * @param enumObj
   * @param value
   */
  public static enumValueToKey<E>(enumObj: E, value: any): keyof E | null {
    for (const key in enumObj) {
      if (enumObj[key] === value) {
        return key
      }
    }
    return null
  }

  /**
   * 递归清除简单对象中的空值
   * 此处未做尾递归优化，对复杂对象处理可能出现溢栈
   * @param obj
   * @param prune
   */
  public static cleanObjectKeys(
    obj: Record<string, any>,
    prune: Array<string | number | boolean | undefined> = [undefined],
  ) {
    return Object.keys(obj).reduce((all, cur) => {
      const val = obj[cur]
      if (prune.includes(val)) {
        return all
      }

      if (Object.prototype.toString.call(val) === '[object Object]') {
        all[cur] = this.cleanObjectKeys(val, prune)
      } else {
        all[cur] = val
      }
      return all
    }, {} as any)
  }

  /*
  * 打印有递归引用关系的对象
  */
  public static logCircular(obj: Record<string, any>) {
    const getCircularReplacer = () => {
      const seen = new WeakSet()
      return (key: string, value: object | null) => {
        if (typeof value === 'object' && value !== null) {
          if (seen.has(value)) {
            return
          }
          seen.add(value)
        }
        return value
      }
    }

    const removedCircularObj = JSON.stringify(obj, getCircularReplacer(), 4)
    console.log(removedCircularObj)

    return removedCircularObj
  }

  public static isEmptyObjectArray(objects: any[]) {
    if (!objects.length) return true

    for (const matchCondition of objects) {
      if (!this.isEmptyObject(matchCondition)) {
        return false
      }
    }

    return true
  }

  public static toObject(obj: any) {
    const obj2: any = {}

    if (Object.keys(obj).length === 0 && this.keys(obj).length) {
      // 不可遍历对象
      this.keys(obj).forEach((key: any) => {
        obj2[key] = obj[key]
      })
    } else {
      // 可遍历对象
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          obj2[key] = obj[key]
        }
      }
    }

    return obj2
  }

  /**
   * 以第一个对象的值为标准，将第二个对象的值合并到第一个对象中。
   * @param obj1
   * @param obj2
   */
  public static merge (obj1: Record<string, any>, obj2: Record<string, any>) {
    for (const key in obj2) {
      const value = obj2 [key]

      if (obj1.hasOwnProperty (key)) {
        if (typeof obj1 [key] === 'object' && typeof value === 'object') {
          if (Array.isArray (obj1 [key]) && Array.isArray (value)) {
            obj1 [key] = [...value]
          } else if (!Array.isArray (obj1 [key]) && !Array.isArray (value)) {
            this.merge (obj1 [key], value)
          } else {
            obj1 [key] = value
          }
        } else {
          obj1 [key] = value
        }
      } else {
        if (Array.isArray (value)) {
          obj1 [key] = [...value]
        } else if (typeof obj1 [key] === 'object' && typeof value === 'object') {
          obj1 [key] = this.merge ({}, value)
        } else {
          obj1 [key] = value
        }
      }
    }

    return obj1
  }

  /**
   * 对没有特殊属性且没有循环引用的对象进行深拷贝
   *
   * 通过 JSON.parse(JSON.stringify(obj)) 实现
   * @param obj
   * @returns
   */
  public static fastCopy<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj))
  }

  /**
   * 检查数组的元素是否重复
   */
  public static isArrayRepeat(arr: any[]) {
    const set = new Set(arr)
    return set.size !== arr.length
  }


  /**
   * 按指定属性去重 对象数组， 如 {a:1}, {a:2, b:2}, 去重后为 {a:1}
   * @param arr
   * @param key
   */
  static removeDuplicate(arr: any[], key: string) {
    const tempMap = new Map<any, any>()

    for (const arrElement of arr) {
      if (!arrElement.hasOwnProperty(key)) {
        throw new Error(`数组元素缺少属性${  key}`)
      }

      if (!tempMap.has(arrElement[key])) {
        tempMap.set(arrElement[key], arrElement)
      }
    }

    return Array.from(tempMap.values())
  }

  /**
   * 计算对象中空值的个数，undefined 或者 null
   * @param keys
   * @param object
   */
  static countEmptyKeys(keys: string[], object: Record<string, any>) {
    let count = 0
    keys.forEach((key) => {
      if (object[key] === undefined || object[key] === null) {
        count++
      }
    })

    return count
  }

  /**
   * 移除对象中的 null, undefined, '', false。对对象进行清洗
   * @param projectsCleaned
   */
  static removeEmptyKeys(projectsCleaned: Record<string, any>) {
    for (const key in projectsCleaned) {
      if (projectsCleaned[key] === null || projectsCleaned[key] === undefined || projectsCleaned[key] === '' || projectsCleaned[key] === false) {
        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
        delete projectsCleaned[key]
      }
    }

    return projectsCleaned
  }
}
