import { ObjectUtil } from './index'
import { HumanTransferType } from '../../service/moer/components/human_transfer/human_transfer'

describe('Test', function () {

  it('should pass', async () => {
    const obj = {
      a: ['1'],
      b: {
        c: 1,
      },
      d: 2,
      e: '2',
    }

    const clone = ObjectUtil.deepClone(obj)
    console.log(JSON.stringify(clone, null, 4))
    expect(clone === obj).toBe(false)
  })
})


describe('CompareHelper Test', function () {
  it('isEqual', async () => {
    expect(ObjectUtil.isEqual(undefined, undefined)).toBe(true)
    expect(ObjectUtil.isEqual(null, null)).toBe(true)
    expect(ObjectUtil.isEqual({}, {})).toBe(true)

    expect(ObjectUtil.isEqual(1, 1)).toBe(true)

    expect(ObjectUtil.isEqual({ a: 1 }, { a: 1 })).toBe(true)
    expect(ObjectUtil.isEqual({ a: 1 }, { a: 2 })).toBe(false)
    expect(ObjectUtil.isEqual({ a: [{ b: { c: 1 } }] }, { a: [{ b: { c: 1 } }] })).toBe(true)
  }, 30000)
})

describe('enumKeys', function () {
  it('enumKeys', async () => {
    enum TestEnum {
      Up = 'up',
      Down = 'down',
      Left = 'left',
      Right = 'right'
    }

    for (const key of ObjectUtil.enumKeys(TestEnum)) {
      console.log(key)
      console.log(TestEnum[key])
    }
  }, 30000)

  it('enumValues', async () => {
    enum TestEnum {
      Up = 'up',
      Down = 'down',
      Left = 'left',
      Right = 'right'
    }

    console.log(JSON.stringify(ObjectUtil.enumValues(TestEnum), null, 4))

    enum TestEnum2 {
      A = 1,
      B = 2,
      C = 3
    }

    console.log(JSON.stringify(ObjectUtil.enumValues(TestEnum2), null, 4))
  })
})

describe('logCircular', function () {
  it('logCircular', async () => {
    const obj = {
      address: { country: 'Chile' }, numbers: [1, 2, 3], age: 30,
    }
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    obj.name = obj

    ObjectUtil.logCircular(obj)
  }, 30000)
})


describe('mergeObject', function () {
  it('merge', async () => {
    const obj1 = {
      c: {
        a: undefined,
        b: 'c'
      }
    }

    const obj2 = {
      c: {
        a: 1,
        b: {
          a: [1, 2]
        }
      }
    }

    console.log(JSON.stringify(ObjectUtil.merge(obj1, obj2), null, 4))
  }, 30000)
})
