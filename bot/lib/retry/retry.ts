type RetrySyncFunc<T> = (count: number) => T
type RetryAsyncFunc<T> = (count: number) => Promise<T>

export class Retry {
  /**
   * 重试机制
   * @param count
   * @param func
   * @param options
   * @returns
   */
  public static async retry<T>(
    count: number,
    func: RetrySyncFunc<T> | RetryAsyncFunc<T>,
    options: {
      delayFunc?: (count: number) => number
    } = {},
  ): Promise<T> {
    if (!options.delayFunc) {
      options.delayFunc = (c) => c * 50
    }

    let tryCount = 0
    let error: Error | undefined

    // 重试三次
    while (tryCount < count) {
      error = undefined

      // 暂停一下
      if (tryCount > 0) {
        await this.sleep(options.delayFunc(tryCount))
      }

      tryCount++
      try {
        return await func(tryCount)
      } catch (err: any) {
        console.error(err)
        error = err
      }
    }

    if (error) {
      throw error
    }

    throw new Error(`retry failed for ${tryCount} times`)
  }

  // 暂停函数
  private static async sleep(ms: number): Promise<void> {
    if (ms <= 0) {
      return
    }

    return new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve()
      }, ms)
    })
  }
}
