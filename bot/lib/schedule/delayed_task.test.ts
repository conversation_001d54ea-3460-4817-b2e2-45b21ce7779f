// 假设您的 DelayedTask 类位于 delayedTask.ts 文件中


import { DelayedTask } from './delayed_task'
import { sleep } from './schedule'

describe('DelayedTask 类测试 - autoRetry 为 true，模拟第一次失败，第二次成功', () => {
  let taskExecutionCount = 0
  let conditionCheckCount = 0

  // 具体实现的 updateChecker
  const updateChecker = async () => {
    conditionCheckCount++
    console.log(`执行 updateChecker，第 ${conditionCheckCount} 次`)
    // 模拟更新一些状态，这里简单等待一下
    await new Promise((resolve) => setTimeout(resolve, 10))
  }

  // 具体实现的 condition，第一次返回 false，第二次返回 true
  const condition = async () => {
    console.log(`执行 condition，第 ${conditionCheckCount} 次`)
    return conditionCheckCount >= 3
  }

  // 具体实现的 task，在执行时打印日志
  const task = async () => {
    taskExecutionCount++
    console.log(`执行 task，第 ${taskExecutionCount} 次`)
    // 模拟任务执行，这里简单等待一下
    await new Promise((resolve) => setTimeout(resolve, 10))
  }

  test('当条件初始为 false，autoRetry 为 true 时，应重试直到条件为 true，然后执行任务', async () => {
    const delay = 1000 // 延迟 1 秒

    const delayedTask = new DelayedTask(
      delay,
      task,
      condition,
      true, // autoRetry 为 true
      updateChecker
    )

    // 启动任务
    await delayedTask.start()

    await sleep(10 * 1000)
  }, 1E8)
})