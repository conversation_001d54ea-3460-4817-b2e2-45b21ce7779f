import path from 'path'
import fs from 'fs'
import URL from 'url'
import { generate } from 'short-uuid'

const rcWinntStartsWithDrive = /^[a-z]:/i
const rcWinntBackBack = /^\\\\/

export class PathHelper {
  /**
   * 把文件名转换为小写
   */
  public static async fileNameToLowerCase(folderPath: string, fileName: string): Promise<string> {
    const oldPath = path.join(folderPath, fileName)
    const newPath = path.join(folderPath, fileName.toLowerCase())
    return await this.rename(oldPath, newPath)
  }

  public static async rename(oldPath: string, newPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      fs.rename(oldPath, newPath, function (err) {
        if (err) {
          return reject(err)
        }
        resolve(newPath)
      })
    })
  }

  /**
   * 判断是否为链接
   * @param link
   */
  public static isUrlLink(link: string) {
    try {
      new URL.URL(link)
    } catch (err) {
      return false
    }

    return true
  }

  /**
   * 判断是否为网络链接
   * @param link
   */
  public static isHttpLink(link: string) {
    try {
      const url = new URL.URL(link)
      return url.protocol === 'http:' || url.protocol === 'https:'
    } catch (err) {
      return false
    }
  }

  /**
   * 判断是否为绝对路径
   * @param filePath
   */
  public static isAbsolute(filePath: string) {
    return path.isAbsolute(filePath)
  }

  /**
   * 相对路径转绝对路径
   */
  public static relativeToAbsolute(relative: string, currentDir: string) {
    return path.normalize(path.join(currentDir, relative))
  }

  /**
   * windows 路径转 posix 环境路径
   */
  public static windowsToPosix(filePath: string): string {
    return path.normalize(filePath).split(path.win32.sep).join(path.posix.sep)
  }

  /**
   * 删除 Windows 路径开头的卷标
   *
   * 例如
   * - C:\Windows\System32 --> \Windows\System32
   * - C:照片\1.png --> 照片\1.png
   * @param filePath
   */
  public static removeWindowsDrive(filePath: string): string {
    return filePath.replace(rcWinntStartsWithDrive, '')
  }

  /**
   * 检测是否 Windows NT 的 Namespace 格式的路径
   * 例如:
   * - \\computername\disk\日记.md
   * - \\192.168.0.1\disk2\文件.html
   * - \\?\GLOBALROOT
   *
   * 参考: https://web.archive.org/web/https://docs.microsoft.com/en-us/windows/win32/fileio/naming-a-file
   * @param link
   */
  public static isWindowsNamespace(link: string): boolean {
    return rcWinntBackBack.test(link)
  }

  /**
   * 检测一个路径是否 Windows 的路径
   *
   * Windows 的路径只有反斜杠 \，没有斜杠
   * @param link
   */
  public static isWindows(link: string): boolean {
    return link.indexOf(path.win32.sep) >= 0 && link.indexOf(path.posix.sep) < 0
  }

  /**
   * decodeURI 解码 URI
   * @param encodedURI
   */
  public static async decodeAndRename(encodedURI: string): Promise<string> {
    try {
      return await this.rename(encodedURI, decodeURI(encodedURI))
    } catch (e) {
      // 说明解析有误
      if (e instanceof URIError) {
        return encodedURI
      }
      throw e
    }
  }

  /**
   * 尝试对一串文本进行 encodedURI 解析
   * encodedURI 只允许出现以下字符，否则不会被解析
   * 类型          包含
   * 保留字符      ; , / ? : @ & = + $
   * 非转义的字符    字母 数字 - _ . ! ~ * ' ( )
   * 数字符号      #
   *
   * 外加 %，用来表示编码后的 utf-8 16 进制
   * @param encodedURI
   */
  public static decode(encodedURI: string): string {
    const ok = /^[!#-;=_~A-Za-z]*$/.test(encodedURI)
    if (!ok) {
      // 说明含有别的字符
      return encodedURI
    }
    try {
      return decodeURI(encodedURI)
    } catch (e) {
      // 说明解析有误
      if (e instanceof URIError) {
        return encodedURI
      }
      throw e
    }
  }

  /**
   * 将文件名内非法字符替换为 '-'， 推荐所有创建及写入文件，都使用此方法。
   * 参考： https://stackoverflow.com/questions/42210199/remove-illegal-characters-from-a-file-name-but-leave-spaces
   */
  public static formatFilename(filename: string, isFolder = false, lengthLimit = 20, withoutExt = false) {
    let newFilename = filename.replace(/[/\\?%*:#|"<>]/g, '-')

    // 缩短过长文件名
    if (lengthLimit === -1) {
      // -1 则不截取文件名
      newFilename = newFilename.trim()
    } else if (isFolder || withoutExt) {
      newFilename = newFilename.trim().slice(0, lengthLimit)
    } else {
      const extension = path.extname(newFilename)
      const filenameOnly = path.basename(newFilename, extension)
      newFilename = filenameOnly.trim().slice(0, lengthLimit) + extension
    }

    if (newFilename.startsWith('.')) {
      return newFilename.replace('.', '-')
    }
    return newFilename
  }

  /**
   * 获取文件后缀, 返回不带 . 的后缀, 并将后缀名小写
   * @param fileName
   * @param withDot
   */
  public static getFileExt(fileName: string, withDot = false) {
    if (!fileName || fileName.length <= 1) {
      return ''
    }

    const ext = path.extname(fileName).toLowerCase()
    if (withDot) {
      return ext
    }
    return ext.substr(1)
  }

  /**
   * 获取去除后缀名后的文件名。
   * @param fileName
   */
  public static getFileNameWithoutExtension(fileName: string) {
    const extname = path.extname(fileName)
    return path.basename(fileName, extname)
  }

  /**
   * 替换一个文件名的后缀
   * @param fileName
   * @param newExt
   */
  public static replaceFileExt(fileName: string, newExt: string) {
    const cleanFileName = PathHelper.getFileNameWithoutExtension(fileName)
    newExt = newExt.trim()
    if (newExt.startsWith('.') || newExt === '') {
      return `${cleanFileName}${newExt}`
    }
    return `${cleanFileName}.${newExt}`
  }

  public static generateUniqueFilename(filename: string) {
    const uuid = generate()

    const extension = path.extname(filename)
    const filenameOnly = path.basename(filename, extension)

    return `${filenameOnly}_${uuid}${extension}`
  }

  public static getFileNameFromUrl(source: string) {
    try {
      const url = new URL.URL(source)
      return path.basename(url.pathname)
    } catch (e) {
      return source
    }
  }

  public static addHttps(url: string) {
    if (!/^(?:f|ht)tps?:\/\//.test(url)) {
      url = `https://${url}`
    }
    return url
  }

  // 返回 A 相对于 B 的路径
  public static relativePath(A: string, B: string) {
    return path.join(path.relative(path.dirname(A), path.dirname(B)), path.basename(B))
  }

  public static getFilepathWithNewExt(filePath: string, ext: string) {
    const dirname = path.dirname(filePath)

    const basename = `${this.getFileNameWithoutExtension(filePath)}.${ext}`

    return path.join(dirname, basename)
  }

  public static encodeUrl(url: string): string {
    try {
      if (url.indexOf('/') > -1) {
        const nameChunks = url.split('/')
        const name = nameChunks[nameChunks.length - 1]
        nameChunks[nameChunks.length - 1] = encodeURIComponent(name)
        return nameChunks.join('/')
      } else {
        return encodeURIComponent(url)
      }
    } catch (e) {
      console.error('encodeUrl failed', e)
      return url
    }
  }
}
