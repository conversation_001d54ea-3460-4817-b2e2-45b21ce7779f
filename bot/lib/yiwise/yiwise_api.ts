import * as crypto from 'crypto'
import { Config } from '../../config/config'
import { HttpHelper } from '../http/httpHelper'
import { CustomerAcquisitionLink } from '../wx_biz_api/customer_acquisition_link'

/**
 * 一知接口
 */
export class YiwiseAPI {

  public static PhoneCallJobIdDay1_5min = 1704344075971
  public static PhoneCallJobIdDay1_30min = 1704344095058
  public static PhoneCallJobIdDay2 = 1704344045623
  public static PhoneCallSundayJob = 1704344062433

  public static SmsJobIdDay20min = 55763
  public static SmsJobIdDay1H = 55765

  /**
   * 将客户注册到语音外呼任务
   * @param jobId
   * @param phoneNumber
   * @param simpleName
   * @param channelName
   */
  public static async addFriendPhoneCallNotify(jobId: number, phoneNumber: string, simpleName: string, channelName: string) {
    const requestUrl = 'https://openapi.tanyibot.com/apiOpen/v1/job/importCustomerAsync'

    const customerPersons: any[] = []
    customerPersons.push({
      'phoneNumber': phoneNumber,
      properties: {
        '渠道名': channelName,
        '助教名片': CustomerAcquisitionLink.getLink(simpleName)
      }
    })

    // Create the request body
    const requestBody = {
      'robotCallJobId': jobId,
      'customerPersons': customerPersons,
    }

    return await HttpHelper.post<any>(requestUrl, requestBody, this.getHeaders())
  }

  public static async addFriendShortMessageNotify(templateID: number, phoneNumber: string, simpleName: string, channelName: string) {
    const requestUrl = 'https://openapi.tanyibot.com/apiOpen/v1/sms/batchSend'

    const customers: any[] = []
    customers.push({
      'phone': phoneNumber
    })

    const requestBody = {
      'smsTemplateId': templateID,
      'customers': customers
    }
    return await HttpHelper.post<any>(requestUrl, requestBody, this.getHeaders())
  }

  private static getHeaders() {
    const tenantSign = Config.setting.yiwise.tenantSign
    const appKey = Config.setting.yiwise.appKey
    const appSecret = Config.setting.yiwise.appSecret
    const version = Config.setting.yiwise.version

    const timestamp = Date.now()

    // Create the string to hash
    const strToHash = `${tenantSign}appKey=${appKey}&appSecret=${appSecret}&tenantSign=${tenantSign}&version=${version}&timestamp=${timestamp}`

    // Create SHA-256 hash
    const hash = crypto.createHash('sha256')
    hash.update(strToHash, 'utf8')
    const signature = hash.digest('hex')
    return {
      'appkey': appKey,
      'timestamp': timestamp,
      'signature': signature,
    }
  }

}