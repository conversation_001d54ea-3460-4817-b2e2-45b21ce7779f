import axios, { AxiosResponse } from 'axios'
import * as crypto from 'crypto'
import { QueryKnowLedgeCourseRequest, QueryKnowLedgeCourseResponse } from './type'

export class TanglangApi {
  static key1 = '465ab10f3304f8d4682a2f8d47f09b98'
  static key2 = 'e4efa0481e1ca58d7ddc840c672367af'
  static companyId = 81273
  static client = this.newTanglangAxiosClient(this.key2, this.companyId)

  static newTanglangAxiosClient(key:string, companyId:number) {
    const client = axios.create({
      baseURL:'https://api9.bjmantis.cn/api'
    })
    client.interceptors.request.use((config) => {
      const time = Date.now()
      const defaultBody = {
        time: time,
        token: md5(key + time),
        companyId: companyId
      }

      // 保证 config.data 是对象
      config.data = {
        ...defaultBody,
        ...(config.data || {})
      }
      return config
    })
    return client
  }

  public static async ping() {
    const response = await this.client.post('/ShiXinUserCon/queryUserList')
    console.log(response.data)
  }

  public static async queryKnowLedgeCourse(unionId:string, courseId:number):Promise<QueryKnowLedgeCourseResponse> {
    const response  = await this.client.post<QueryKnowLedgeCourseResponse, AxiosResponse<QueryKnowLedgeCourseResponse>, QueryKnowLedgeCourseRequest>('/scrmCourseApiCon/queryKnowLedgeCourse', {
      type:'ALL',
      courseId,
      unionId
    })
    return response.data
  }
}


function md5(input: string): string {
  return crypto.createHash('md5').update(input).digest('hex')
}