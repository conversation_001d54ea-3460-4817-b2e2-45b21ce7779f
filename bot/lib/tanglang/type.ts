export type CommonResponse = {
  flag:string
  count:number
  buttonFlag:string
  message:string
}

// doc: https://doc.bjmantis.cn/web/#/122?page_id=3124
export type QueryKnowLedgeCourseRequest = {
  type: CourseType
  unionId: string
  courseId: number
}

export type CourseType = 'ALL' | 'CAMP' | 'LIVE' | 'VIDEO' | 'TEXT' | 'AUDIO' | 'SPECIAL_COLUMN'

export interface QueryKnowLedgeCourseResponse extends CommonResponse {
  data: {
    id: number
    liveNum: number
    campId: number
    campPeriodId: number
    specialColumnId: number
    type: CourseType
    name: string
    url: string
    duration: number
    startTime: string
    endTime: string
    stuStatus: string
    progressNum: number
    watchLiveDuration: number
    watchPlaybackDuration: number
    introduction: string
    userNum: number
    externalRemark: string
    enterStartTime: string
    enterEndTime: string
    beginStartTime: string
    totalCourseDurationSum: number
    totalWatchLiveDurationSum: number
    totalWatchPlaybackDurationSum: number
    studyState: 0 | 1 | 2
    liveDuration: number
    coursePlaybackDuration: number
    enable: 'Y' | 'N'
    expire: number
    rightsType: string
    rightsCourseNum: number
    watchProgress: number
  }[]
}