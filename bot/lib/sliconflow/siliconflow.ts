import axios, { AxiosInstance } from 'axios'
import logger from '../../model/logger/logger'

interface RerankOptions {
    model?: string
    query: string
    documents: string[]
    top_n?: number
    return_documents?: boolean
    max_chunks_per_doc?: number
    overlap_tokens?: number

    threshhold?: number // 过滤阈值 小于 这个值的 回答会被丢弃
}

interface RerankResult {
    index: number
    relevance_score: number
}

interface RerankResponse {
    id: string
    results: RerankResult[]
    meta?: {
        billed_units: {
            input_tokens: number
            output_tokens: number
            search_units: number
            classifications: number
        }
        tokens: {
            input_tokens: number
            output_tokens: number
        }
    }
}

class SiliconFlow {
  private apiKey: string | null = null
  private axiosInstance: AxiosInstance

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: 'https://api.siliconflow.cn/v1',
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
      },
    })
  }

  // Method to authenticate the API key
  auth(apiKey: string): void {
    this.apiKey = apiKey
    this.axiosInstance.defaults.headers['authorization'] = `Bearer ${this.apiKey}`
  }

  // Method to make the rerank API call
  async createRerank(options: RerankOptions): Promise<RerankResponse> {
    if (!this.apiKey) {
      throw new Error('API Key is not set. Please call auth() first.')
    }

    const payload = {
      model: options.model ? options.model : 'BAAI/bge-reranker-v2-m3',
      query: options.query,
      documents: options.documents,
      top_n: options.top_n,
      return_documents: options.return_documents,
      max_chunks_per_doc: options.max_chunks_per_doc,
      overlap_tokens: options.overlap_tokens,
    }

    try {
      const response = await this.axiosInstance.post('/rerank', payload)
      if (response.status !== 200) {
        throw new Error(`An error occurred during reranking ${response.data}`)
      }

      return response.data as RerankResponse
    } catch (error) {
      logger.error('Error in creating rerank:', error)
      throw error
    }
  }
}

export default SiliconFlow