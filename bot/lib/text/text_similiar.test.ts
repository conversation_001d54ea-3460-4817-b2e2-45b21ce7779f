import { calculateJaccardSimilarity, cosineSimilarity } from './text_similarity'
import { OpenaiEmbedding } from '../ai/llm/openai_embedding'

describe('Test', function () {
  beforeAll(() => {

  })

  it('regex', async () => {
    console.log('你必须知道的Pytorch中文教程.'.replace(/[@#￥%……&*（）——+“”‘’；：、。.]/g, ''))
  }, 30000)

  it('', async () => {
    const embedding1 =  await OpenaiEmbedding.getInstance().embedQuery('好的，那祝你学习愉快，有问题随时找我哦~')
    const embedding2 =  await OpenaiEmbedding.getInstance().embedQuery('好的，有问题随时找我哦~')

    const similarity = cosineSimilarity(embedding1, embedding2)
    console.log(similarity) // 输出余弦相似度
  }, 30000)


  it('test', async () => {
    const embedding1 =  await OpenaiEmbedding.getInstance().embedQuery('好的，有需要随时联系我哦~')
    const embedding2 =  await OpenaiEmbedding.getInstance().embedQuery('不客气，有需要再联系我哦，祝你开学顺利!')

    const similarity = cosineSimilarity(embedding1, embedding2)
    console.log(similarity) // 输出余弦相似度
  }, 30000)

  it('wexinLink', async () => {
    // 这边 url 做一下裁剪
    const urlToCut = new URL('https://mp.weixin.qq.com/s?__biz=Mzg3NzIxMjcwMg==&mid=2247548255&idx=1&sn=728df4d035b901c47ff297e1087167d4&chksm=cf24182ef8539138fbc3a8e75a796be01117ceee6fc5b5edf9682d0d35ae2cb97417093d0dfa&mpshare=1&scene=1&srcid=0512EyaZQQMWCXjCm2EnKjjd&sharer_shareinfo=fc6a64564c823eb6e354f0d67339dd93&sharer_shareinfo_first=6e3033221363604d5fadffb7d78e7e59#rd')
    const biz = urlToCut.searchParams.get('__biz')
    const mid = urlToCut.searchParams.get('mid')
    const idx = urlToCut.searchParams.get('idx')
    const sn = urlToCut.searchParams.get('sn')

    if (biz && mid && idx && sn) {
      const params = new URLSearchParams()
      params.set('__biz', biz)
      params.set('mid', mid)
      params.set('idx', idx)
      params.set('sn', sn)
      const url = `https://mp.weixin.qq.com/s?${params.toString()}`
      console.log(url)
    }

  }, 60000)

  it('ss', async () => {
    console.log(JSON.stringify(calculateJaccardSimilarity(`冥想day1
    感悟分享：`, `🧘🏻‍♀#冥想day1

🔺1.感悟分享：`), null, 4))
  }, 60000)
})