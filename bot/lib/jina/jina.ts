import axios, { AxiosInstance } from 'axios'
import logger from '../../model/logger/logger'

interface JinaRerankOptions {
    model?: string
    query: string
    documents: string[]
    top_n?: number
    return_documents?: boolean
}

interface JinaRerankResult {
    index: number
    relevance_score: number
    document?: {
        text: string
    }
}

interface JinaRerankResponse {
    model: string
    usage: {
        total_tokens: number
        prompt_tokens: number
    }
    results: JinaRerankResult[]
}

class Jina {
  private apiKey: string | null = null
  private axiosInstance: AxiosInstance

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: 'https://api.jina.ai/v1',
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // Method to authenticate the API key
  auth(apiKey: string): void {
    this.apiKey = apiKey
    this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${this.apiKey}`
  }

  // Method to make the rerank API call
  async createRerank(options: JinaRerankOptions): Promise<JinaRerankResponse> {
    if (!this.apiKey) {
      throw new Error('API Key is not set. Please call auth() first.')
    }

    const payload = {
      model: options.model || 'jina-reranker-v2-base-multilingual',
      query: options.query,
      documents: options.documents,
      top_n: options.top_n,
      return_documents: options.return_documents || false,
    }

    try {
      const response = await this.axiosInstance.post('/rerank', payload)
      if (response.status !== 200) {
        throw new Error(`An error occurred during reranking ${response.data}`)
      }

      return response.data as JinaRerankResponse
    } catch (error) {
      logger.error('Error in creating Jina rerank:', error)
      throw error
    }
  }
}

export default Jina
