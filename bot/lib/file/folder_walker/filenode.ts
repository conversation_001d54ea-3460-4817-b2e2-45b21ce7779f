import { PathHelper } from '../../path'

interface Node {
  addChild(node: Node): void
}

export enum FileNodeType {
  Folder = 'folder',
  File = 'file',
}


export class FileNode {
  public filepath: string
  public originalName: string // 原文件名
  public type: string // 文件类型 'folder'|'file'
  public visited?: boolean // 是否被遍历过
  public parent?: FileNode // 注意 parent 在添加节点的时候一定要设置上

  public children: FileNode[]

  constructor(filepath: string, type: string, originalName?: string) {
    this.filepath = filepath
    this.originalName = originalName ?? filepath
    this.type = type
    this.children = []
  }

  public static clone(fileNode: FileNode): FileNode {
    return new FileNode(fileNode.filepath, fileNode.type, fileNode.originalName)
  }

  public isRoot(): boolean {
    return this.parent === undefined
  }

  public addChild(child: FileNode): void {
    this.children.push(child)
    child.parent = this
  }

  // 如果文件夹内只有一个 markdown 文件或者只有一个文件夹，则将该子级替换为父级。并将文件树进行排序。
  public optimizeTree(): void {
    const buildTree = (nodeA: FileNode, nodeB: FileNode) => {
      let currentNodeB = nodeB

      if (nodeA.hasOneFile('md')) {
        buildTree(nodeA.children[0], currentNodeB)
      } else {
        currentNodeB = FileNode.clone(nodeA)
        nodeB.addChild(currentNodeB)

        nodeA.children = nodeA.children.sort((a, b) => a.originalName.localeCompare(b.originalName, undefined, {
          numeric: true,
          sensitivity: 'base',
        }))
        nodeA.children.forEach((child) => {
          buildTree(child, currentNodeB)
        })
      }
    }

    const dummyNode = new FileNode('', FileNodeType.Folder)

    buildTree(this, dummyNode)

    this.filepath = dummyNode.children[0].filepath
    this.children = dummyNode.children[0].children
    this.parent = undefined // 根节点标志
    this.type = dummyNode.children[0].type
    this.originalName = dummyNode.children[0].originalName
  }

  /**
   * 将文件树转为其他树的辅助函数, 只需要传入节点转换逻辑即可, 如果转换失败可以返回 null, 该节点不会被加入到结果中
   * @param converter
   */
  public async toOtherTree<T extends Node>(converter: (fileNode: FileNode) => Promise<T | null>): Promise<T | null> {
    const dfs = async (fileNode: FileNode): Promise<T | null> => {
      const newRoot = await converter(fileNode)
      if (!newRoot) {
        return null
      }

      for (const child of fileNode.children) {
        const newChild = await dfs(child)

        if (newChild) {
          newRoot.addChild(newChild)
        }

      }

      return newRoot
    }
    return dfs(this)
  }

  // 文件夹下只含有一个以 fileExt 结尾的文件，或只有一个文件夹
  private hasOneFile(fileExt: string): boolean {
    if (this.children.length === 0) {
      return false
    }

    const filteredChildren = this.children.filter((child) => {
      return child.type === FileNodeType.Folder || PathHelper.getFileExt(child.filepath) === fileExt
    })

    return filteredChildren.length === 1
  }
}
