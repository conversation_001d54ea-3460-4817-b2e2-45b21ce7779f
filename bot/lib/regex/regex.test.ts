import { CommonRegexType, RegexHelper } from './regex'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const currentSentence = 'https://github.com/geongeorge/i-hate-regex'
    console.log(RegexHelper.strIncludeRegex(currentSentence, CommonRegexType.URL))
  })

  it('extract list', async () => {
    console.log(RegexHelper.extractList(`fk
    [1, 2, 3]u`))
  }, 60000)

  it('should return null when input is an empty string', async () => {
    const input = ''
    const result = await RegexHelper.extractPhoneNumber(input)
    expect(result).toBeNull()
  })

  it('should return null when input does not contain a phone number', async () => {
    const input = 'No phone number here'
    const result = await RegexHelper.extractPhoneNumber(input)
    expect(result).toBeNull()
  })

  it('should return the first phone number found in the input string', async () => {
    const input = '18210783811'
    const result = await RegexHelper.extractPhoneNumber(input)
    expect(result).toBe('18210783811')
  })

  it('emoji', async () => {
    const sentence = '上图是完成小讲堂的入学礼：冥想练习指南[礼物]，可以仔细看下我们的冥想准备工作哦。'
    const emoji = RegexHelper.extractEmoji(sentence)
    console.log(emoji)
  }, 60000)

  it('sayChinese', async () => {
    const sentence = '7.97 JIV:/ <EMAIL> 03/07 【抖音商城】https://v.douyin.com/ifUboEyq/ 墨尔21天冥想系统班|直播课程|赠送墨尔宣传册一份'
    console.log(sentence.length)
    const result = RegexHelper.isMajorityChinese(sentence)
    console.log(result)
  }, 60000)
})