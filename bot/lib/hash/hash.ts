import * as CryptoJS from 'crypto-js'

export class HashSum {
  /**
     * 默认获取 md5 的 hash 结果
     * @param content
     * @param algorithm
     * @returns
     */
  public static hash(content: string, algorithm = 'md5') {
    let hashValue

    switch (algorithm.toUpperCase()) {
      case 'MD5':
        hashValue = CryptoJS.MD5(content)
        break
      case 'SHA-1':
        hashValue = CryptoJS.SHA1(content)
        break
      case 'SHA-256':
        hashValue = CryptoJS.SHA256(content)
        break
      case 'SHA-512':
        hashValue = CryptoJS.SHA512(content)
        break
      default:
        throw new Error('Unsupported hash algorithm')
    }

    return hashValue.toString()
  }

}
