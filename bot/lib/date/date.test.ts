import dayjs from 'dayjs'
import { DateHelper } from './date'
import { fixDate } from './mock'
import { isJumpedTime, jumpDate, OriginalDate } from './jump'
import { sleep } from '../schedule/schedule'

describe('Test', function () {
  beforeAll(() => {

  })

  it('test jump', async () => {
    console.log(isJumpedTime)
    jumpDate('2024-11-01 09:00:00')
    console.log(isJumpedTime)

    // Print the adjusted date
    console.log('Adjusted Date:', new Date().toLocaleString())

    console.log('Original Date:', new OriginalDate().toLocaleString())

    await sleep(3 * 1000)
    console.log(new Date().toLocaleString())
  }, 60000)

  it('should pass', async () => {
    const date1 = dayjs('2024-08-22 08:00:00')
    const date2 = dayjs('2024-08-22 09:00:00')


    console.log(date1.isSame(date2, 'day'))
  }, 60000)

  it('date diff ', async () => {
    const date1 = dayjs('2024-08-22 00:00:00')
    const date2 = dayjs('2024-08-29 23:59:00')


    console.log(date2.diff(date1, 'day'))
  }, 60000)

  it('123', async () => {
    console.log(DateHelper.parseDate('2024-09-23 20:00:00').getHours())
  }, 60000)


  it('123123', async () => {
    fixDate('2024-10-13')
    console.log(new OriginalDate())
    console.log(new Date().toLocaleString())
    console.log(new Date(Date.now()).toLocaleString())
    console.log(DateHelper.add(new OriginalDate(), -1, 'day').toLocaleString())

    fixDate(DateHelper.add(new OriginalDate(), -1, 'day').toLocaleString())
    console.log(new Date().toLocaleString())
  }, 60000)
})