export interface IAliyunCredentials {
  region: string
  accountId: string
  accessKeyId: string
  secretAccessKey: string
  securityToken?: string
}

/**
 * 阿里云的访问Tick
 */
export class AliyunCredentials implements IAliyunCredentials {
  private static credentials: IAliyunCredentials
  private static initialized: boolean
  private static instance?: AliyunCredentials

  public get region(): string {
    if (!AliyunCredentials.credentials) {
      return ''
    }

    return AliyunCredentials.credentials.region
  }

  public get accountId(): string {
    if (!AliyunCredentials.credentials) {
      return ''
    }

    return AliyunCredentials.credentials.accountId
  }

  public get securityToken(): string | undefined {
    if (!AliyunCredentials.credentials) {
      return ''
    }
    return AliyunCredentials.credentials.securityToken
  }

  public get accessKeyId(): string {
    if (!AliyunCredentials.credentials) {
      return ''
    }

    return AliyunCredentials.credentials.accessKeyId
  }

  public get secretAccessKey(): string {
    if (!AliyunCredentials.credentials) {
      return ''
    }

    return AliyunCredentials.credentials.secretAccessKey
  }

  public static getInstance(): AliyunCredentials {
    if (!this.initialized) {
      throw new Error('AliyunCredentials has not initialized')
    }

    if (!this.instance) {
      this.instance = new AliyunCredentials()
    }

    return this.instance
  }

  public static initialize(credentials: IAliyunCredentials): void {
    this.credentials = {
      region: credentials.region,
      accountId: credentials.accountId,
      accessKeyId: credentials.accessKeyId,
      secretAccessKey: credentials.secretAccessKey,
    }

    if (credentials.securityToken != 'undefined') {
      this.credentials.securityToken = credentials.securityToken
    }

    this.initialized = true
  }
}
