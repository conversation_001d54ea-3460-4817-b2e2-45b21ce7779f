
const customerAcquisitionLinkMap = new Map<string, string>([
  ['冥想助教-麦子老师', 'cawcde1d992f730117'],
  ['高彤彤', 'cawcde7fc6c23dd9f6'],
  ['墨尔3', 'cawcdec4b64ae9546d'],
  ['墨尔4', 'cawcde0d6512c6ef33'],
  ['麦子5', 'cawcde7cd5a27a62be'],
  ['麦子6', 'cawcde32cc9c96d96b'],
  ['麦子7', 'cawcdeb9d76b10efa3'],
  ['麦子9', 'cawcde0cb29a41594b'],
  ['麦子10', 'cawcde9269225ad958'],
  ['麦子11', 'cawcde98a0746dfdbb'],
  ['麦子12', 'cawcded891654b0d58'],
  ['麦子13', 'cawcde86407dd78a6a'],
  ['麦子15', 'cawcdeae160400dd48']
])

export class CustomerAcquisitionLink {
  public static getLink(linkName: string) {
    const link = customerAcquisitionLinkMap.get(linkName)
    if (!link) {
      return
    }

    return link
  }
}