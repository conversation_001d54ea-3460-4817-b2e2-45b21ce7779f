import SiliconFlow from '../sliconflow/siliconflow'
import Jina from '../jina/jina'
import { Config } from '../../config/config'
import logger from '../../model/logger/logger'

interface RerankOptions {
    model?: string
    query: string
    documents: string[]
    top_n?: number
    return_documents?: boolean
    max_chunks_per_doc?: number
    overlap_tokens?: number
    threshhold?: number // 过滤阈值 小于 这个值的 回答会被丢弃
}

interface RerankResult {
    index: number
    relevance_score: number
}

interface RerankResponse {
    id?: string
    model?: string
    results: RerankResult[]
    meta?: {
        billed_units?: {
            input_tokens: number
            output_tokens: number
            search_units: number
            classifications: number
        }
        tokens?: {
            input_tokens: number
            output_tokens: number
        }
    }
    usage?: {
        total_tokens: number
        prompt_tokens: number
    }
}

export enum RerankProvider {
    SILICON_FLOW = 'siliconflow',
    JINA = 'jina'
}

class RerankService {
  private siliconFlow: SiliconFlow
  private jina: Jina

  constructor() {
    this.siliconFlow = new SiliconFlow()
    this.jina = new Jina()

    // 初始化认证
    this.siliconFlow.auth(Config.setting.siliconFlow.apiKey)
    this.jina.auth(Config.setting.jina.apiKey)
  }

  /**
     * 执行 rerank 操作，支持自动轮转
     * @param options rerank 选项
     * @param preferredProvider 首选提供商，默认为 SiliconFlow
     * @returns rerank 结果
     */
  async createRerank(
    options: RerankOptions,
    preferredProvider: RerankProvider = RerankProvider.SILICON_FLOW
  ): Promise<RerankResponse> {
    const providers = preferredProvider === RerankProvider.SILICON_FLOW
      ? [RerankProvider.SILICON_FLOW, RerankProvider.JINA]
      : [RerankProvider.JINA, RerankProvider.SILICON_FLOW]

    let lastError: Error | null = null

    for (const provider of providers) {
      try {
        return await this.executeRerank(provider, options)
      } catch (error) {
        lastError = error as Error
        logger.warn(`Rerank failed with provider ${provider}:`, error)

        // 如果不是最后一个提供商，继续尝试下一个
        if (provider !== providers[providers.length - 1]) {
          logger.log('Switching to next provider...')
          continue
        }
      }
    }

    // 如果所有提供商都失败了，抛出最后一个错误
    logger.error('All rerank providers failed')
    throw lastError || new Error('All rerank providers failed')
  }

  /**
     * 使用指定提供商执行 rerank
     * @param provider 提供商
     * @param options rerank 选项
     * @returns rerank 结果
     */
  private async executeRerank(provider: RerankProvider, options: RerankOptions): Promise<RerankResponse> {
    switch (provider) {
      case RerankProvider.SILICON_FLOW:
        return await this.siliconFlow.createRerank(options)

      case RerankProvider.JINA: {
        // 转换选项格式以适配 Jina API
        const jinaOptions = {
          model: 'jina-reranker-v2-base-multilingual',
          query: options.query,
          documents: options.documents,
          top_n: options.top_n,
          return_documents: options.return_documents || false
        }

        const jinaResponse = await this.jina.createRerank(jinaOptions)

        // 转换 Jina 响应格式以匹配统一接口
        return {
          model: jinaResponse.model,
          results: jinaResponse.results,
          usage: jinaResponse.usage
        }
      }

      default:
        throw new Error(`Unsupported rerank provider: ${provider}`)
    }
  }

  /**
     * 获取可用的提供商列表
     * @returns 提供商列表
     */
  getAvailableProviders(): RerankProvider[] {
    return [RerankProvider.SILICON_FLOW, RerankProvider.JINA]
  }

  /**
     * 检查提供商是否可用
     * @param provider 提供商
     * @returns 是否可用
     */
  async isProviderAvailable(provider: RerankProvider): Promise<boolean> {
    try {
      const testOptions: RerankOptions = {
        query: 'test',
        documents: ['test document'],
        top_n: 1
      }

      await this.executeRerank(provider, testOptions)
      return true
    } catch (error) {
      logger.warn(`Provider ${provider} is not available:`, error)
      return false
    }
  }
}

export default RerankService
