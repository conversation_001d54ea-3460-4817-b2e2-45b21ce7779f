import RerankService, { RerankProvider } from './rerank_service'
import { Config } from '../../config/config'

describe('RerankService', function () {
  let rerankService: RerankService

  beforeAll(() => {
    rerankService = new RerankService()
  })

  it('should successfully rerank with SiliconFlow', async () => {
    const response = await rerankService.createRerank({
      query: '牛油果',
      documents: ['苹果', '香蕉', '水果', '蔬菜'],
      top_n: 4,
      return_documents: true,
    }, RerankProvider.SILICON_FLOW)

    console.log('SiliconFlow response:', JSON.stringify(response, null, 2))
    expect(response.results).toBeDefined()
    expect(response.results.length).toBeGreaterThan(0)
  }, 30000)

  it('should successfully rerank with <PERSON><PERSON>', async () => {
    const response = await rerankService.createRerank({
      query: '牛油果',
      documents: ['苹果', '香蕉', '水果', '蔬菜'],
      top_n: 4,
      return_documents: true,
    }, RerankProvider.JINA)

    console.log('Jina response:', JSON.stringify(response, null, 2))
    expect(response.results).toBeDefined()
    expect(response.results.length).toBeGreaterThan(0)
  }, 30000)

  it('should fallback to Jina when SiliconFlow fails', async () => {
    // 模拟 SiliconFlow 失败的情况
    const originalApiKey = Config.setting.siliconFlow.apiKey

    // 临时设置无效的 API key 来模拟失败
    Config.setting.siliconFlow.apiKey = 'invalid-key'

    try {
      const response = await rerankService.createRerank({
        query: '牛油果',
        documents: ['苹果', '香蕉', '水果', '蔬菜'],
        top_n: 4,
        return_documents: true,
      }, RerankProvider.SILICON_FLOW)

      console.log('Fallback response:', JSON.stringify(response, null, 2))
      expect(response.results).toBeDefined()
      expect(response.results.length).toBeGreaterThan(0)
    } finally {
      // 恢复原始 API key
      Config.setting.siliconFlow.apiKey = originalApiKey
    }
  }, 60000)

  it('should check provider availability', async () => {
    const siliconFlowAvailable = await rerankService.isProviderAvailable(RerankProvider.SILICON_FLOW)
    const jinaAvailable = await rerankService.isProviderAvailable(RerankProvider.JINA)

    console.log('SiliconFlow available:', siliconFlowAvailable)
    console.log('Jina available:', jinaAvailable)

    expect(typeof siliconFlowAvailable).toBe('boolean')
    expect(typeof jinaAvailable).toBe('boolean')
  }, 60000)

  it('should get available providers', () => {
    const providers = rerankService.getAvailableProviders()

    expect(providers).toContain(RerankProvider.SILICON_FLOW)
    expect(providers).toContain(RerankProvider.JINA)
    expect(providers.length).toBe(2)
  })
})
