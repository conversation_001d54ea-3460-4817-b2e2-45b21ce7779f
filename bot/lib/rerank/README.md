# Rerank Service

这个模块提供了一个统一的 rerank 服务，支持多个提供商之间的自动轮转。

## 功能特性

- **多提供商支持**: 支持 SiliconFlow 和 Jina 两个 rerank 提供商
- **自动轮转**: 当主要提供商失败时，自动切换到备用提供商
- **统一接口**: 提供统一的 API 接口，屏蔽不同提供商的差异
- **错误处理**: 完善的错误处理和日志记录
- **可用性检查**: 支持检查提供商的可用性

## 使用方法

### 基本使用

```typescript
import RerankService, { RerankProvider } from './rerank_service'

const rerankService = new RerankService()

// 使用默认提供商 (SiliconFlow)
const response = await rerankService.createRerank({
  query: '牛油果',
  documents: ['苹果', '香蕉', '水果', '蔬菜'],
  top_n: 4,
  return_documents: true,
})

// 指定使用 Jina 提供商
const response2 = await rerankService.createRerank({
  query: '牛油果',
  documents: ['苹果', '香蕉', '水果', '蔬菜'],
  top_n: 4,
  return_documents: true,
}, RerankProvider.JINA)
```

### 检查提供商可用性

```typescript
const siliconFlowAvailable = await rerankService.isProviderAvailable(RerankProvider.SILICON_FLOW)
const jinaAvailable = await rerankService.isProviderAvailable(RerankProvider.JINA)

console.log('SiliconFlow available:', siliconFlowAvailable)
console.log('Jina available:', jinaAvailable)
```

### 获取可用提供商列表

```typescript
const providers = rerankService.getAvailableProviders()
console.log('Available providers:', providers)
```

## 配置

在配置文件中需要添加 Jina 的 API 配置：

```typescript
// config/interface.ts
interface IDevelopConfig {
  // ... 其他配置
  
  siliconFlow: {
    apiKey: string
  }

  jina: {
    apiKey: string
  }
}

// config/develop.ts 和 config/prod.ts
export const DevelopConfigure: IDevelopConfig = {
  // ... 其他配置
  
  siliconFlow: {
    apiKey: 'your-siliconflow-api-key'
  },

  jina: {
    apiKey: 'your-jina-api-key'
  }
}
```

## 轮转机制

1. **默认行为**: 优先使用 SiliconFlow，失败时自动切换到 Jina
2. **指定提供商**: 可以指定首选提供商，失败时仍会轮转到其他提供商
3. **错误处理**: 所有提供商都失败时，抛出最后一个错误

## API 接口

### RerankOptions

```typescript
interface RerankOptions {
    model?: string                    // 模型名称
    query: string                     // 查询文本
    documents: string[]               // 文档列表
    top_n?: number                    // 返回结果数量
    return_documents?: boolean        // 是否返回文档内容
    max_chunks_per_doc?: number       // 每个文档最大块数
    overlap_tokens?: number           // 重叠 token 数
    threshhold?: number              // 过滤阈值
}
```

### RerankResponse

```typescript
interface RerankResponse {
    id?: string                       // 请求 ID
    model?: string                    // 使用的模型
    results: RerankResult[]           // 排序结果
    meta?: object                     // 元数据
    usage?: object                    // 使用统计
}
```

## 测试

运行测试：

```bash
npm test -- rerank_service.test.ts
```

测试包括：
- SiliconFlow 提供商测试
- Jina 提供商测试
- 轮转机制测试
- 可用性检查测试
