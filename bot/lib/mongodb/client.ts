import { MongodbReadPreference, MongooseConnector } from './index'

export class MongodbClient {
  private static connector?: MongooseConnector

  public static async close() {
    if (this.connector) {
      try {
        await this.connector.close()
      } catch (e) {
        console.warn(e)
      }
    }
    this.connector = undefined
  }

  protected static getConnectorWithUrl(uri: string, readPreference?: MongodbReadPreference): MongooseConnector {
    if (!this.connector) {
      this.connector = new MongooseConnector(uri, readPreference)
    }

    return this.connector
  }
}
