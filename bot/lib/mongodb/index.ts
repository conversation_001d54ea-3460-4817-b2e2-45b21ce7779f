import mongoose from 'mongoose'

export enum MongodbReadPreference {
  Nearest = 'nearest',
  Primary = 'primary',
  Secondary = 'secondary',
  PrimaryPreferred = 'primaryPreferred',
  SecondaryPreferred = 'secondaryPreferred',
}

export class MongooseConnector {
  private readonly uri: string
  private readonly readPreference: MongodbReadPreference
  private connection?: mongoose.Connection

  constructor(uri: string, readPreference = MongodbReadPreference.Nearest) {
    if (!uri) {
      throw new Error('please provide mongodb config')
    }

    this.uri = uri
    this.readPreference = readPreference
  }

  public close() {
    if (this.connection) {
      this.connection.close(true).catch(console.error)
    }
    this.connection = undefined
  }

  /**
   * 获取连接
   */
  public getConnection(): mongoose.Connection {
    if (!this.connection || this.connection.readyState != 1) { // reconnect when connection is closed
      const option: mongoose.ConnectOptions = {
        maxPoolSize: 1000,
        serverSelectionTimeoutMS: 5000,
        readPreference: this.readPreference,
        readConcern: {
          level: 'majority',
        }
      }

      this.connection = mongoose.createConnection(this.uri, option)

      this.connection.addListener('disconnected', () => {
        this.close()
      })

      this.connection.addListener('close', () => {
        this.close()
      })

      this.connection.addListener('error', (e) => {
        console.error(e)
      })
    }

    return this.connection
  }
}
