import RerankService, { RerankProvider } from '../lib/rerank/rerank_service'

/**
 * Rerank 轮转功能使用示例
 */
async function rerankExample() {
  console.log('=== Rerank 轮转功能示例 ===\n')

  const rerankService = new RerankService()

  // 测试数据
  const query = '有机护肤产品'
  const documents = [
    '有机护肤品，含有芦荟和洋甘菊，专为敏感肌肤设计',
    '新的化妆趋势注重鲜艳的颜色和创新的技巧',
    '天然有机护肤产品，温和滋润，保护敏感肌肤',
    '化妆艺术的新纪元，大胆的颜色和创新的技巧',
    '敏感肌专用的天然护肤品，含有芦荟和洋甘菊提取物'
  ]

  try {
    console.log('1. 使用默认轮转 (SiliconFlow -> Jina)')
    const result1 = await rerankService.createRerank({
      query,
      documents,
      top_n: 3,
      return_documents: true
    })
    
    console.log('结果:', result1.results.map(r => ({
      index: r.index,
      score: r.relevance_score,
      document: documents[r.index]
    })))
    console.log()

    console.log('2. 指定使用 Jina 提供商')
    const result2 = await rerankService.createRerank({
      query,
      documents,
      top_n: 3,
      return_documents: true
    }, RerankProvider.JINA)
    
    console.log('结果:', result2.results.map(r => ({
      index: r.index,
      score: r.relevance_score,
      document: documents[r.index]
    })))
    console.log()

    console.log('3. 检查提供商可用性')
    const siliconFlowAvailable = await rerankService.isProviderAvailable(RerankProvider.SILICON_FLOW)
    const jinaAvailable = await rerankService.isProviderAvailable(RerankProvider.JINA)
    
    console.log(`SiliconFlow 可用: ${siliconFlowAvailable}`)
    console.log(`Jina 可用: ${jinaAvailable}`)
    console.log()

    console.log('4. 获取可用提供商列表')
    const providers = rerankService.getAvailableProviders()
    console.log('可用提供商:', providers)

  } catch (error) {
    console.error('示例执行失败:', error)
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  rerankExample().catch(console.error)
}

export { rerankExample }
